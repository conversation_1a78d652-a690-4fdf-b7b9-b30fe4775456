import { defineConfig } from "drizzle-kit";

// For development, use SQLite if no DATABASE_URL is provided or if explicitly set to use SQLite
const useSqlite = !process.env.DATABASE_URL ||
                  process.env.DATABASE_URL === 'sqlite' ||
                  process.env.NODE_ENV === 'development' && !process.env.DATABASE_URL?.includes('postgresql');

let config;

if (useSqlite) {
  config = defineConfig({
    out: "./migrations",
    schema: "./shared/schema.ts",
    dialect: "sqlite",
    dbCredentials: {
      url: "./colette.db",
    },
  });
} else {
  if (!process.env.DATABASE_URL) {
    throw new Error("DATABASE_URL, ensure the database is provisioned");
  }

  config = defineConfig({
    out: "./migrations",
    schema: "./shared/schema.ts",
    dialect: "postgresql",
    dbCredentials: {
      url: process.env.DATABASE_URL,
    },
  });
}

export default config;
