{"version": 3, "sources": ["../../src/entity.ts", "../../src/column.ts", "../../src/column-builder.ts", "../../src/table.utils.ts", "../../src/pg-core/foreign-keys.ts", "../../src/pg-core/unique-constraint.ts", "../../src/pg-core/utils/array.ts", "../../src/tracing-utils.ts", "../../src/pg-core/columns/common.ts", "../../src/pg-core/columns/enum.ts", "../../src/subquery.ts", "../../src/view-common.ts", "../../src/table.ts", "../../drizzle-orm/version.js", "../../src/tracing.ts", "../../src/sql/sql.ts", "../../src/alias.ts", "../../src/errors.ts", "../../src/sql/expressions/conditions.ts", "../../src/sql/expressions/select.ts", "../../src/query-promise.ts", "../../src/utils.ts", "../../src/pg-core/columns/int.common.ts", "../../src/pg-core/columns/bigint.ts", "../../src/pg-core/columns/bigserial.ts", "../../src/pg-core/columns/boolean.ts", "../../src/pg-core/columns/char.ts", "../../src/pg-core/columns/cidr.ts", "../../src/pg-core/columns/custom.ts", "../../src/pg-core/columns/date.common.ts", "../../src/pg-core/columns/date.ts", "../../src/pg-core/columns/double-precision.ts", "../../src/pg-core/columns/inet.ts", "../../src/pg-core/columns/integer.ts", "../../src/pg-core/columns/interval.ts", "../../src/pg-core/columns/json.ts", "../../src/pg-core/columns/jsonb.ts", "../../src/pg-core/columns/line.ts", "../../src/pg-core/columns/macaddr.ts", "../../src/pg-core/columns/macaddr8.ts", "../../src/pg-core/columns/numeric.ts", "../../src/pg-core/columns/point.ts", "../../src/pg-core/columns/postgis_extension/utils.ts", "../../src/pg-core/columns/postgis_extension/geometry.ts", "../../src/pg-core/columns/real.ts", "../../src/pg-core/columns/serial.ts", "../../src/pg-core/columns/smallint.ts", "../../src/pg-core/columns/smallserial.ts", "../../src/pg-core/columns/text.ts", "../../src/pg-core/columns/time.ts", "../../src/pg-core/columns/timestamp.ts", "../../src/pg-core/columns/uuid.ts", "../../src/pg-core/columns/varchar.ts", "../../src/pg-core/columns/vector_extension/bit.ts", "../../src/pg-core/columns/vector_extension/halfvec.ts", "../../src/pg-core/columns/vector_extension/sparsevec.ts", "../../src/pg-core/columns/vector_extension/vector.ts", "../../src/pg-core/columns/all.ts", "../../src/pg-core/table.ts", "../../src/pg-core/primary-keys.ts", "../../src/relations.ts", "../../src/sql/functions/aggregate.ts", "../../src/sql/functions/vector.ts"], "sourcesContent": ["export const entityKind = Symbol.for('drizzle:entityKind');\nexport const hasOwnEntityKind = Symbol.for('drizzle:hasOwnEntityKind');\n\nexport interface DrizzleEntity {\n\t[entityKind]: string;\n}\n\nexport type DrizzleEntityClass<T> =\n\t& ((abstract new(...args: any[]) => T) | (new(...args: any[]) => T))\n\t& DrizzleEntity;\n\nexport function is<T extends DrizzleEntityClass<any>>(value: any, type: T): value is InstanceType<T> {\n\tif (!value || typeof value !== 'object') {\n\t\treturn false;\n\t}\n\n\tif (value instanceof type) { // eslint-disable-line no-instanceof/no-instanceof\n\t\treturn true;\n\t}\n\n\tif (!Object.prototype.hasOwnProperty.call(type, entityKind)) {\n\t\tthrow new Error(\n\t\t\t`Class \"${\n\t\t\t\ttype.name ?? '<unknown>'\n\t\t\t}\" doesn't look like a Drizzle entity. If this is incorrect and the class is provided by Dr<PERSON>zle, please report this as a bug.`,\n\t\t);\n\t}\n\n\tlet cls = Object.getPrototypeOf(value).constructor;\n\tif (cls) {\n\t\t// Traverse the prototype chain to find the entityKind\n\t\twhile (cls) {\n\t\t\tif (entityKind in cls && cls[entityKind] === type[entityKind]) {\n\t\t\t\treturn true;\n\t\t\t}\n\n\t\t\tcls = Object.getPrototypeOf(cls);\n\t\t}\n\t}\n\n\treturn false;\n}\n", "import type {\n\tColumnBuilderBaseConfig,\n\tColumnBuilderRuntimeConfig,\n\tColumnDataType,\n\tGeneratedColumnConfig,\n\tGeneratedIdentityConfig,\n} from './column-builder.ts';\nimport { entityKind } from './entity.ts';\nimport type { DriverValueMapper, SQL, SQLWrapper } from './sql/sql.ts';\nimport type { Table } from './table.ts';\nimport type { Update } from './utils.ts';\n\nexport interface ColumnBaseConfig<\n\tTDataType extends ColumnDataType,\n\tTColumnType extends string,\n> extends ColumnBuilderBaseConfig<TDataType, TColumnType> {\n\ttableName: string;\n\tnotNull: boolean;\n\thasDefault: boolean;\n\tisPrimaryKey: boolean;\n\tisAutoincrement: boolean;\n\thasRuntimeDefault: boolean;\n}\n\nexport type ColumnTypeConfig<T extends ColumnBaseConfig<ColumnDataType, string>, TTypeConfig extends object> = T & {\n\tbrand: 'Column';\n\ttableName: T['tableName'];\n\tname: T['name'];\n\tdataType: T['dataType'];\n\tcolumnType: T['columnType'];\n\tdata: T['data'];\n\tdriverParam: T['driverParam'];\n\tnotNull: T['notNull'];\n\thasDefault: T['hasDefault'];\n\tisPrimaryKey: T['isPrimaryKey'];\n\tisAutoincrement: T['isAutoincrement'];\n\thasRuntimeDefault: T['hasRuntimeDefault'];\n\tenumValues: T['enumValues'];\n\tbaseColumn: T extends { baseColumn: infer U } ? U : unknown;\n\tgenerated: GeneratedColumnConfig<T['data']> | undefined;\n\tidentity: undefined | 'always' | 'byDefault';\n} & TTypeConfig;\n\nexport type ColumnRuntimeConfig<TData, TRuntimeConfig extends object> = ColumnBuilderRuntimeConfig<\n\tTData,\n\tTRuntimeConfig\n>;\n\nexport interface Column<\n\tT extends ColumnBaseConfig<ColumnDataType, string> = ColumnBaseConfig<ColumnDataType, string>,\n\t// eslint-disable-next-line @typescript-eslint/no-unused-vars\n\tTRuntimeConfig extends object = object,\n\t// eslint-disable-next-line @typescript-eslint/no-unused-vars\n\tTTypeConfig extends object = object,\n> extends DriverValueMapper<T['data'], T['driverParam']>, SQLWrapper {\n\t// SQLWrapper runtime implementation is defined in 'sql/sql.ts'\n}\n/*\n\t`Column` only accepts a full `ColumnConfig` as its generic.\n\tTo infer parts of the config, use `AnyColumn` that accepts a partial config.\n\tSee `GetColumnData` for example usage of inferring.\n*/\nexport abstract class Column<\n\tT extends ColumnBaseConfig<ColumnDataType, string> = ColumnBaseConfig<ColumnDataType, string>,\n\tTRuntimeConfig extends object = object,\n\tTTypeConfig extends object = object,\n> implements DriverValueMapper<T['data'], T['driverParam']>, SQLWrapper {\n\tstatic readonly [entityKind]: string = 'Column';\n\n\tdeclare readonly _: ColumnTypeConfig<T, TTypeConfig>;\n\n\treadonly name: string;\n\treadonly keyAsName: boolean;\n\treadonly primary: boolean;\n\treadonly notNull: boolean;\n\treadonly default: T['data'] | SQL | undefined;\n\treadonly defaultFn: (() => T['data'] | SQL) | undefined;\n\treadonly onUpdateFn: (() => T['data'] | SQL) | undefined;\n\treadonly hasDefault: boolean;\n\treadonly isUnique: boolean;\n\treadonly uniqueName: string | undefined;\n\treadonly uniqueType: string | undefined;\n\treadonly dataType: T['dataType'];\n\treadonly columnType: T['columnType'];\n\treadonly enumValues: T['enumValues'] = undefined;\n\treadonly generated: GeneratedColumnConfig<T['data']> | undefined = undefined;\n\treadonly generatedIdentity: GeneratedIdentityConfig | undefined = undefined;\n\n\tprotected config: ColumnRuntimeConfig<T['data'], TRuntimeConfig>;\n\n\tconstructor(\n\t\treadonly table: Table,\n\t\tconfig: ColumnRuntimeConfig<T['data'], TRuntimeConfig>,\n\t) {\n\t\tthis.config = config;\n\t\tthis.name = config.name;\n\t\tthis.keyAsName = config.keyAsName;\n\t\tthis.notNull = config.notNull;\n\t\tthis.default = config.default;\n\t\tthis.defaultFn = config.defaultFn;\n\t\tthis.onUpdateFn = config.onUpdateFn;\n\t\tthis.hasDefault = config.hasDefault;\n\t\tthis.primary = config.primaryKey;\n\t\tthis.isUnique = config.isUnique;\n\t\tthis.uniqueName = config.uniqueName;\n\t\tthis.uniqueType = config.uniqueType;\n\t\tthis.dataType = config.dataType as T['dataType'];\n\t\tthis.columnType = config.columnType;\n\t\tthis.generated = config.generated;\n\t\tthis.generatedIdentity = config.generatedIdentity;\n\t}\n\n\tabstract getSQLType(): string;\n\n\tmapFromDriverValue(value: unknown): unknown {\n\t\treturn value;\n\t}\n\n\tmapToDriverValue(value: unknown): unknown {\n\t\treturn value;\n\t}\n\n\t// ** @internal */\n\tshouldDisableInsert(): boolean {\n\t\treturn this.config.generated !== undefined && this.config.generated.type !== 'byDefault';\n\t}\n}\n\nexport type UpdateColConfig<\n\tT extends ColumnBaseConfig<ColumnDataType, string>,\n\tTUpdate extends Partial<ColumnBaseConfig<ColumnDataType, string>>,\n> = Update<T, TUpdate>;\n\nexport type AnyColumn<TPartial extends Partial<ColumnBaseConfig<ColumnDataType, string>> = {}> = Column<\n\tRequired<Update<ColumnBaseConfig<ColumnDataType, string>, TPartial>>\n>;\n\nexport type GetColumnData<TColumn extends Column, TInferMode extends 'query' | 'raw' = 'query'> =\n\t// dprint-ignore\n\tTInferMode extends 'raw' // Raw mode\n\t\t? TColumn['_']['data'] // Just return the underlying type\n\t\t: TColumn['_']['notNull'] extends true // Query mode\n\t\t? TColumn['_']['data'] // Query mode, not null\n\t\t: TColumn['_']['data'] | null; // Query mode, nullable\n\nexport type InferColumnsDataTypes<TColumns extends Record<string, Column>> = {\n\t[Key in keyof TColumns]: GetColumnData<TColumns[Key], 'query'>;\n};\n", "import { entityKind } from '~/entity.ts';\nimport type { Column } from './column.ts';\nimport type { MySqlColumn } from './mysql-core/index.ts';\nimport type { ExtraConfigColumn, PgColumn, PgSequenceOptions } from './pg-core/index.ts';\nimport type { SingleStoreColumn } from './singlestore-core/index.ts';\nimport type { SQL } from './sql/sql.ts';\nimport type { SQLiteColumn } from './sqlite-core/index.ts';\nimport type { Assume, Simplify } from './utils.ts';\n\nexport type ColumnDataType =\n\t| 'string'\n\t| 'number'\n\t| 'boolean'\n\t| 'array'\n\t| 'json'\n\t| 'date'\n\t| 'bigint'\n\t| 'custom'\n\t| 'buffer';\n\nexport type Dialect = 'pg' | 'mysql' | 'sqlite' | 'singlestore' | 'common';\n\nexport type GeneratedStorageMode = 'virtual' | 'stored';\n\nexport type GeneratedType = 'always' | 'byDefault';\n\nexport type GeneratedColumnConfig<TDataType> = {\n\tas: TDataType | SQL | (() => SQL);\n\ttype?: GeneratedType;\n\tmode?: GeneratedStorageMode;\n};\n\nexport type GeneratedIdentityConfig = {\n\tsequenceName?: string;\n\tsequenceOptions?: PgSequenceOptions;\n\ttype: 'always' | 'byDefault';\n};\n\nexport interface ColumnBuilderBaseConfig<TDataType extends ColumnDataType, TColumnType extends string> {\n\tname: string;\n\tdataType: TDataType;\n\tcolumnType: TColumnType;\n\tdata: unknown;\n\tdriverParam: unknown;\n\tenumValues: string[] | undefined;\n}\n\nexport type MakeColumnConfig<\n\tT extends ColumnBuilderBaseConfig<ColumnDataType, string>,\n\tTTableName extends string,\n\tTData = T extends { $type: infer U } ? U : T['data'],\n> = {\n\tname: T['name'];\n\ttableName: TTableName;\n\tdataType: T['dataType'];\n\tcolumnType: T['columnType'];\n\tdata: TData;\n\tdriverParam: T['driverParam'];\n\tnotNull: T extends { notNull: true } ? true : false;\n\thasDefault: T extends { hasDefault: true } ? true : false;\n\tisPrimaryKey: T extends { isPrimaryKey: true } ? true : false;\n\tisAutoincrement: T extends { isAutoincrement: true } ? true : false;\n\thasRuntimeDefault: T extends { hasRuntimeDefault: true } ? true : false;\n\tenumValues: T['enumValues'];\n\tbaseColumn: T extends { baseBuilder: infer U extends ColumnBuilderBase } ? BuildColumn<TTableName, U, 'common'>\n\t\t: never;\n\tidentity: T extends { identity: 'always' } ? 'always' : T extends { identity: 'byDefault' } ? 'byDefault' : undefined;\n\tgenerated: T extends { generated: infer G } ? unknown extends G ? undefined\n\t\t: G extends undefined ? undefined\n\t\t: G\n\t\t: undefined;\n} & {};\n\nexport type ColumnBuilderTypeConfig<\n\t// eslint-disable-next-line @typescript-eslint/no-unused-vars\n\tT extends ColumnBuilderBaseConfig<ColumnDataType, string>,\n\tTTypeConfig extends object = object,\n> = Simplify<\n\t& {\n\t\tbrand: 'ColumnBuilder';\n\t\tname: T['name'];\n\t\tdataType: T['dataType'];\n\t\tcolumnType: T['columnType'];\n\t\tdata: T['data'];\n\t\tdriverParam: T['driverParam'];\n\t\tnotNull: T extends { notNull: infer U } ? U : boolean;\n\t\thasDefault: T extends { hasDefault: infer U } ? U : boolean;\n\t\tenumValues: T['enumValues'];\n\t\tidentity: T extends { identity: infer U } ? U : unknown;\n\t\tgenerated: T extends { generated: infer G } ? G extends undefined ? unknown : G : unknown;\n\t}\n\t& TTypeConfig\n>;\n\nexport type ColumnBuilderRuntimeConfig<TData, TRuntimeConfig extends object = object> = {\n\tname: string;\n\tkeyAsName: boolean;\n\tnotNull: boolean;\n\tdefault: TData | SQL | undefined;\n\tdefaultFn: (() => TData | SQL) | undefined;\n\tonUpdateFn: (() => TData | SQL) | undefined;\n\thasDefault: boolean;\n\tprimaryKey: boolean;\n\tisUnique: boolean;\n\tuniqueName: string | undefined;\n\tuniqueType: string | undefined;\n\tdataType: string;\n\tcolumnType: string;\n\tgenerated: GeneratedColumnConfig<TData> | undefined;\n\tgeneratedIdentity: GeneratedIdentityConfig | undefined;\n} & TRuntimeConfig;\n\nexport interface ColumnBuilderExtraConfig {\n\tprimaryKeyHasDefault?: boolean;\n}\n\nexport type NotNull<T extends ColumnBuilderBase> = T & {\n\t_: {\n\t\tnotNull: true;\n\t};\n};\n\nexport type HasDefault<T extends ColumnBuilderBase> = T & {\n\t_: {\n\t\thasDefault: true;\n\t};\n};\n\nexport type IsPrimaryKey<T extends ColumnBuilderBase> = T & {\n\t_: {\n\t\tisPrimaryKey: true;\n\t};\n};\n\nexport type IsAutoincrement<T extends ColumnBuilderBase> = T & {\n\t_: {\n\t\tisAutoincrement: true;\n\t};\n};\n\nexport type HasRuntimeDefault<T extends ColumnBuilderBase> = T & {\n\t_: {\n\t\thasRuntimeDefault: true;\n\t};\n};\n\nexport type $Type<T extends ColumnBuilderBase, TType> = T & {\n\t_: {\n\t\t$type: TType;\n\t};\n};\n\nexport type HasGenerated<T extends ColumnBuilderBase, TGenerated extends {} = {}> = T & {\n\t_: {\n\t\thasDefault: true;\n\t\tgenerated: TGenerated;\n\t};\n};\n\nexport type IsIdentity<\n\tT extends ColumnBuilderBase,\n\tTType extends 'always' | 'byDefault',\n> = T & {\n\t_: {\n\t\tnotNull: true;\n\t\thasDefault: true;\n\t\tidentity: TType;\n\t};\n};\nexport interface ColumnBuilderBase<\n\tT extends ColumnBuilderBaseConfig<ColumnDataType, string> = ColumnBuilderBaseConfig<ColumnDataType, string>,\n\tTTypeConfig extends object = object,\n> {\n\t_: ColumnBuilderTypeConfig<T, TTypeConfig>;\n}\n\n// To understand how to use `ColumnBuilder` and `AnyColumnBuilder`, see `Column` and `AnyColumn` documentation.\nexport abstract class ColumnBuilder<\n\tT extends ColumnBuilderBaseConfig<ColumnDataType, string> = ColumnBuilderBaseConfig<ColumnDataType, string>,\n\tTRuntimeConfig extends object = object,\n\tTTypeConfig extends object = object,\n\tTExtraConfig extends ColumnBuilderExtraConfig = ColumnBuilderExtraConfig,\n> implements ColumnBuilderBase<T, TTypeConfig> {\n\tstatic readonly [entityKind]: string = 'ColumnBuilder';\n\n\tdeclare _: ColumnBuilderTypeConfig<T, TTypeConfig>;\n\n\tprotected config: ColumnBuilderRuntimeConfig<T['data'], TRuntimeConfig>;\n\n\tconstructor(name: T['name'], dataType: T['dataType'], columnType: T['columnType']) {\n\t\tthis.config = {\n\t\t\tname,\n\t\t\tkeyAsName: name === '',\n\t\t\tnotNull: false,\n\t\t\tdefault: undefined,\n\t\t\thasDefault: false,\n\t\t\tprimaryKey: false,\n\t\t\tisUnique: false,\n\t\t\tuniqueName: undefined,\n\t\t\tuniqueType: undefined,\n\t\t\tdataType,\n\t\t\tcolumnType,\n\t\t\tgenerated: undefined,\n\t\t} as ColumnBuilderRuntimeConfig<T['data'], TRuntimeConfig>;\n\t}\n\n\t/**\n\t * Changes the data type of the column. Commonly used with `json` columns. Also, useful for branded types.\n\t *\n\t * @example\n\t * ```ts\n\t * const users = pgTable('users', {\n\t * \tid: integer('id').$type<UserId>().primaryKey(),\n\t * \tdetails: json('details').$type<UserDetails>().notNull(),\n\t * });\n\t * ```\n\t */\n\t$type<TType>(): $Type<this, TType> {\n\t\treturn this as $Type<this, TType>;\n\t}\n\n\t/**\n\t * Adds a `not null` clause to the column definition.\n\t *\n\t * Affects the `select` model of the table - columns *without* `not null` will be nullable on select.\n\t */\n\tnotNull(): NotNull<this> {\n\t\tthis.config.notNull = true;\n\t\treturn this as NotNull<this>;\n\t}\n\n\t/**\n\t * Adds a `default <value>` clause to the column definition.\n\t *\n\t * Affects the `insert` model of the table - columns *with* `default` are optional on insert.\n\t *\n\t * If you need to set a dynamic default value, use {@link $defaultFn} instead.\n\t */\n\tdefault(value: (this['_'] extends { $type: infer U } ? U : this['_']['data']) | SQL): HasDefault<this> {\n\t\tthis.config.default = value;\n\t\tthis.config.hasDefault = true;\n\t\treturn this as HasDefault<this>;\n\t}\n\n\t/**\n\t * Adds a dynamic default value to the column.\n\t * The function will be called when the row is inserted, and the returned value will be used as the column value.\n\t *\n\t * **Note:** This value does not affect the `drizzle-kit` behavior, it is only used at runtime in `drizzle-orm`.\n\t */\n\t$defaultFn(\n\t\tfn: () => (this['_'] extends { $type: infer U } ? U : this['_']['data']) | SQL,\n\t): HasRuntimeDefault<HasDefault<this>> {\n\t\tthis.config.defaultFn = fn;\n\t\tthis.config.hasDefault = true;\n\t\treturn this as HasRuntimeDefault<HasDefault<this>>;\n\t}\n\n\t/**\n\t * Alias for {@link $defaultFn}.\n\t */\n\t$default = this.$defaultFn;\n\n\t/**\n\t * Adds a dynamic update value to the column.\n\t * The function will be called when the row is updated, and the returned value will be used as the column value if none is provided.\n\t * If no `default` (or `$defaultFn`) value is provided, the function will be called when the row is inserted as well, and the returned value will be used as the column value.\n\t *\n\t * **Note:** This value does not affect the `drizzle-kit` behavior, it is only used at runtime in `drizzle-orm`.\n\t */\n\t$onUpdateFn(\n\t\tfn: () => (this['_'] extends { $type: infer U } ? U : this['_']['data']) | SQL,\n\t): HasDefault<this> {\n\t\tthis.config.onUpdateFn = fn;\n\t\tthis.config.hasDefault = true;\n\t\treturn this as HasDefault<this>;\n\t}\n\n\t/**\n\t * Alias for {@link $onUpdateFn}.\n\t */\n\t$onUpdate = this.$onUpdateFn;\n\n\t/**\n\t * Adds a `primary key` clause to the column definition. This implicitly makes the column `not null`.\n\t *\n\t * In SQLite, `integer primary key` implicitly makes the column auto-incrementing.\n\t */\n\tprimaryKey(): TExtraConfig['primaryKeyHasDefault'] extends true ? IsPrimaryKey<HasDefault<NotNull<this>>>\n\t\t: IsPrimaryKey<NotNull<this>>\n\t{\n\t\tthis.config.primaryKey = true;\n\t\tthis.config.notNull = true;\n\t\treturn this as TExtraConfig['primaryKeyHasDefault'] extends true ? IsPrimaryKey<HasDefault<NotNull<this>>>\n\t\t\t: IsPrimaryKey<NotNull<this>>;\n\t}\n\n\tabstract generatedAlwaysAs(\n\t\tas: SQL | T['data'] | (() => SQL),\n\t\tconfig?: Partial<GeneratedColumnConfig<unknown>>,\n\t): HasGenerated<this, {\n\t\ttype: 'always';\n\t}>;\n\n\t/** @internal Sets the name of the column to the key within the table definition if a name was not given. */\n\tsetName(name: string) {\n\t\tif (this.config.name !== '') return;\n\t\tthis.config.name = name;\n\t}\n}\n\nexport type BuildColumn<\n\tTTableName extends string,\n\tTBuilder extends ColumnBuilderBase,\n\tTDialect extends Dialect,\n> = TDialect extends 'pg' ? PgColumn<\n\t\tMakeColumnConfig<TBuilder['_'], TTableName>,\n\t\t{},\n\t\tSimplify<Omit<TBuilder['_'], keyof MakeColumnConfig<TBuilder['_'], TTableName> | 'brand' | 'dialect'>>\n\t>\n\t: TDialect extends 'mysql' ? MySqlColumn<\n\t\t\tMakeColumnConfig<TBuilder['_'], TTableName>,\n\t\t\t{},\n\t\t\tSimplify<\n\t\t\t\tOmit<\n\t\t\t\t\tTBuilder['_'],\n\t\t\t\t\t| keyof MakeColumnConfig<TBuilder['_'], TTableName>\n\t\t\t\t\t| 'brand'\n\t\t\t\t\t| 'dialect'\n\t\t\t\t\t| 'primaryKeyHasDefault'\n\t\t\t\t\t| 'mysqlColumnBuilderBrand'\n\t\t\t\t>\n\t\t\t>\n\t\t>\n\t: TDialect extends 'sqlite' ? SQLiteColumn<\n\t\t\tMakeColumnConfig<TBuilder['_'], TTableName>,\n\t\t\t{},\n\t\t\tSimplify<Omit<TBuilder['_'], keyof MakeColumnConfig<TBuilder['_'], TTableName> | 'brand' | 'dialect'>>\n\t\t>\n\t: TDialect extends 'common' ? Column<\n\t\t\tMakeColumnConfig<TBuilder['_'], TTableName>,\n\t\t\t{},\n\t\t\tSimplify<Omit<TBuilder['_'], keyof MakeColumnConfig<TBuilder['_'], TTableName> | 'brand' | 'dialect'>>\n\t\t>\n\t: TDialect extends 'singlestore' ? SingleStoreColumn<\n\t\t\tMakeColumnConfig<TBuilder['_'], TTableName>,\n\t\t\t{},\n\t\t\tSimplify<\n\t\t\t\tOmit<\n\t\t\t\t\tTBuilder['_'],\n\t\t\t\t\t| keyof MakeColumnConfig<TBuilder['_'], TTableName>\n\t\t\t\t\t| 'brand'\n\t\t\t\t\t| 'dialect'\n\t\t\t\t\t| 'primaryKeyHasDefault'\n\t\t\t\t\t| 'singlestoreColumnBuilderBrand'\n\t\t\t\t>\n\t\t\t>\n\t\t>\n\t: never;\n\nexport type BuildIndexColumn<\n\tTDialect extends Dialect,\n> = TDialect extends 'pg' ? ExtraConfigColumn : never;\n\n// TODO\n// try to make sql as well + indexRaw\n\n// optional after everything will be working as expected\n// also try to leave only needed methods for extraConfig\n// make an error if I pass .asc() to fk and so on\n\nexport type BuildColumns<\n\tTTableName extends string,\n\tTConfigMap extends Record<string, ColumnBuilderBase>,\n\tTDialect extends Dialect,\n> =\n\t& {\n\t\t[Key in keyof TConfigMap]: BuildColumn<TTableName, {\n\t\t\t_:\n\t\t\t\t& Omit<TConfigMap[Key]['_'], 'name'>\n\t\t\t\t& { name: TConfigMap[Key]['_']['name'] extends '' ? Assume<Key, string> : TConfigMap[Key]['_']['name'] };\n\t\t}, TDialect>;\n\t}\n\t& {};\n\nexport type BuildExtraConfigColumns<\n\t_TTableName extends string,\n\tTConfigMap extends Record<string, ColumnBuilderBase>,\n\tTDialect extends Dialect,\n> =\n\t& {\n\t\t[Key in keyof TConfigMap]: BuildIndexColumn<TDialect>;\n\t}\n\t& {};\n\nexport type ChangeColumnTableName<TColumn extends Column, TAlias extends string, TDialect extends Dialect> =\n\tTDialect extends 'pg' ? PgColumn<MakeColumnConfig<TColumn['_'], TAlias>>\n\t\t: TDialect extends 'mysql' ? MySqlColumn<MakeColumnConfig<TColumn['_'], TAlias>>\n\t\t: TDialect extends 'singlestore' ? SingleStoreColumn<MakeColumnConfig<TColumn['_'], TAlias>>\n\t\t: TDialect extends 'sqlite' ? SQLiteColumn<MakeColumnConfig<TColumn['_'], TAlias>>\n\t\t: never;\n", "/** @internal */\nexport const TableName = Symbol.for('drizzle:Name');\n", "import { entityKind } from '~/entity.ts';\nimport { TableName } from '~/table.utils.ts';\nimport type { AnyPgColumn, PgColumn } from './columns/index.ts';\nimport type { PgTable } from './table.ts';\n\nexport type UpdateDeleteAction = 'cascade' | 'restrict' | 'no action' | 'set null' | 'set default';\n\nexport type Reference = () => {\n\treadonly name?: string;\n\treadonly columns: PgColumn[];\n\treadonly foreignTable: PgTable;\n\treadonly foreignColumns: PgColumn[];\n};\n\nexport class ForeignKeyBuilder {\n\tstatic readonly [entityKind]: string = 'PgForeignKeyBuilder';\n\n\t/** @internal */\n\treference: Reference;\n\n\t/** @internal */\n\t_onUpdate: UpdateDeleteAction | undefined = 'no action';\n\n\t/** @internal */\n\t_onDelete: UpdateDeleteAction | undefined = 'no action';\n\n\tconstructor(\n\t\tconfig: () => {\n\t\t\tname?: string;\n\t\t\tcolumns: PgColumn[];\n\t\t\tforeignColumns: PgColumn[];\n\t\t},\n\t\tactions?: {\n\t\t\tonUpdate?: UpdateDeleteAction;\n\t\t\tonDelete?: UpdateDeleteAction;\n\t\t} | undefined,\n\t) {\n\t\tthis.reference = () => {\n\t\t\tconst { name, columns, foreignColumns } = config();\n\t\t\treturn { name, columns, foreignTable: foreignColumns[0]!.table as PgTable, foreignColumns };\n\t\t};\n\t\tif (actions) {\n\t\t\tthis._onUpdate = actions.onUpdate;\n\t\t\tthis._onDelete = actions.onDelete;\n\t\t}\n\t}\n\n\tonUpdate(action: UpdateDeleteAction): this {\n\t\tthis._onUpdate = action === undefined ? 'no action' : action;\n\t\treturn this;\n\t}\n\n\tonDelete(action: UpdateDeleteAction): this {\n\t\tthis._onDelete = action === undefined ? 'no action' : action;\n\t\treturn this;\n\t}\n\n\t/** @internal */\n\tbuild(table: PgTable): ForeignKey {\n\t\treturn new ForeignKey(table, this);\n\t}\n}\n\nexport type AnyForeignKeyBuilder = ForeignKeyBuilder;\n\nexport class ForeignKey {\n\tstatic readonly [entityKind]: string = 'PgForeignKey';\n\n\treadonly reference: Reference;\n\treadonly onUpdate: UpdateDeleteAction | undefined;\n\treadonly onDelete: UpdateDeleteAction | undefined;\n\n\tconstructor(readonly table: PgTable, builder: ForeignKeyBuilder) {\n\t\tthis.reference = builder.reference;\n\t\tthis.onUpdate = builder._onUpdate;\n\t\tthis.onDelete = builder._onDelete;\n\t}\n\n\tgetName(): string {\n\t\tconst { name, columns, foreignColumns } = this.reference();\n\t\tconst columnNames = columns.map((column) => column.name);\n\t\tconst foreignColumnNames = foreignColumns.map((column) => column.name);\n\t\tconst chunks = [\n\t\t\tthis.table[TableName],\n\t\t\t...columnNames,\n\t\t\tforeignColumns[0]!.table[TableName],\n\t\t\t...foreignColumnNames,\n\t\t];\n\t\treturn name ?? `${chunks.join('_')}_fk`;\n\t}\n}\n\ntype ColumnsWithTable<\n\tTTableName extends string,\n\tTColumns extends PgColumn[],\n> = { [Key in keyof TColumns]: AnyPgColumn<{ tableName: TTableName }> };\n\nexport function foreignKey<\n\tTTableName extends string,\n\tTForeignTableName extends string,\n\tTColumns extends [AnyPgColumn<{ tableName: TTableName }>, ...AnyPgColumn<{ tableName: TTableName }>[]],\n>(\n\tconfig: {\n\t\tname?: string;\n\t\tcolumns: TColumns;\n\t\tforeignColumns: ColumnsWithTable<TForeignTableName, TColumns>;\n\t},\n): ForeignKeyBuilder {\n\tfunction mappedConfig() {\n\t\tconst { name, columns, foreignColumns } = config;\n\t\treturn {\n\t\t\tname,\n\t\t\tcolumns,\n\t\t\tforeignColumns,\n\t\t};\n\t}\n\n\treturn new ForeignKeyBuilder(mappedConfig);\n}\n", "import { entityKind } from '~/entity.ts';\nimport { TableName } from '~/table.utils.ts';\nimport type { PgColumn } from './columns/index.ts';\nimport type { PgTable } from './table.ts';\n\nexport function unique(name?: string): UniqueOnConstraintBuilder {\n\treturn new UniqueOnConstraintBuilder(name);\n}\n\nexport function uniqueKeyName(table: PgTable, columns: string[]) {\n\treturn `${table[TableName]}_${columns.join('_')}_unique`;\n}\n\nexport class UniqueConstraintBuilder {\n\tstatic readonly [entityKind]: string = 'PgUniqueConstraintBuilder';\n\n\t/** @internal */\n\tcolumns: PgColumn[];\n\t/** @internal */\n\tnullsNotDistinctConfig = false;\n\n\tconstructor(\n\t\tcolumns: PgColumn[],\n\t\tprivate name?: string,\n\t) {\n\t\tthis.columns = columns;\n\t}\n\n\tnullsNotDistinct() {\n\t\tthis.nullsNotDistinctConfig = true;\n\t\treturn this;\n\t}\n\n\t/** @internal */\n\tbuild(table: PgTable): UniqueConstraint {\n\t\treturn new UniqueConstraint(table, this.columns, this.nullsNotDistinctConfig, this.name);\n\t}\n}\n\nexport class UniqueOnConstraintBuilder {\n\tstatic readonly [entityKind]: string = 'PgUniqueOnConstraintBuilder';\n\n\t/** @internal */\n\tname?: string;\n\n\tconstructor(\n\t\tname?: string,\n\t) {\n\t\tthis.name = name;\n\t}\n\n\ton(...columns: [PgColumn, ...PgColumn[]]) {\n\t\treturn new UniqueConstraintBuilder(columns, this.name);\n\t}\n}\n\nexport class UniqueConstraint {\n\tstatic readonly [entityKind]: string = 'PgUniqueConstraint';\n\n\treadonly columns: PgColumn[];\n\treadonly name?: string;\n\treadonly nullsNotDistinct: boolean = false;\n\n\tconstructor(readonly table: PgTable, columns: PgColumn[], nullsNotDistinct: boolean, name?: string) {\n\t\tthis.columns = columns;\n\t\tthis.name = name ?? uniqueKeyName(this.table, this.columns.map((column) => column.name));\n\t\tthis.nullsNotDistinct = nullsNotDistinct;\n\t}\n\n\tgetName() {\n\t\treturn this.name;\n\t}\n}\n", "function parsePgArrayValue(arrayString: string, startFrom: number, inQuotes: boolean): [string, number] {\n\tfor (let i = startFrom; i < arrayString.length; i++) {\n\t\tconst char = arrayString[i];\n\n\t\tif (char === '\\\\') {\n\t\t\ti++;\n\t\t\tcontinue;\n\t\t}\n\n\t\tif (char === '\"') {\n\t\t\treturn [arrayString.slice(startFrom, i).replace(/\\\\/g, ''), i + 1];\n\t\t}\n\n\t\tif (inQuotes) {\n\t\t\tcontinue;\n\t\t}\n\n\t\tif (char === ',' || char === '}') {\n\t\t\treturn [arrayString.slice(startFrom, i).replace(/\\\\/g, ''), i];\n\t\t}\n\t}\n\n\treturn [arrayString.slice(startFrom).replace(/\\\\/g, ''), arrayString.length];\n}\n\nexport function parsePgNestedArray(arrayString: string, startFrom = 0): [any[], number] {\n\tconst result: any[] = [];\n\tlet i = startFrom;\n\tlet lastCharIsComma = false;\n\n\twhile (i < arrayString.length) {\n\t\tconst char = arrayString[i];\n\n\t\tif (char === ',') {\n\t\t\tif (lastCharIsComma || i === startFrom) {\n\t\t\t\tresult.push('');\n\t\t\t}\n\t\t\tlastCharIsComma = true;\n\t\t\ti++;\n\t\t\tcontinue;\n\t\t}\n\n\t\tlastCharIsComma = false;\n\n\t\tif (char === '\\\\') {\n\t\t\ti += 2;\n\t\t\tcontinue;\n\t\t}\n\n\t\tif (char === '\"') {\n\t\t\tconst [value, startFrom] = parsePgArrayValue(arrayString, i + 1, true);\n\t\t\tresult.push(value);\n\t\t\ti = startFrom;\n\t\t\tcontinue;\n\t\t}\n\n\t\tif (char === '}') {\n\t\t\treturn [result, i + 1];\n\t\t}\n\n\t\tif (char === '{') {\n\t\t\tconst [value, startFrom] = parsePgNestedArray(arrayString, i + 1);\n\t\t\tresult.push(value);\n\t\t\ti = startFrom;\n\t\t\tcontinue;\n\t\t}\n\n\t\tconst [value, newStartFrom] = parsePgArrayValue(arrayString, i, false);\n\t\tresult.push(value);\n\t\ti = newStartFrom;\n\t}\n\n\treturn [result, i];\n}\n\nexport function parsePgArray(arrayString: string): any[] {\n\tconst [result] = parsePgNestedArray(arrayString, 1);\n\treturn result;\n}\n\nexport function makePgArray(array: any[]): string {\n\treturn `{${\n\t\tarray.map((item) => {\n\t\t\tif (Array.isArray(item)) {\n\t\t\t\treturn makePgArray(item);\n\t\t\t}\n\n\t\t\tif (typeof item === 'string') {\n\t\t\t\treturn `\"${item.replace(/\\\\/g, '\\\\\\\\').replace(/\"/g, '\\\\\"')}\"`;\n\t\t\t}\n\n\t\t\treturn `${item}`;\n\t\t}).join(',')\n\t}}`;\n}\n", "export function iife<T extends unknown[], U>(fn: (...args: T) => U, ...args: T): U {\n\treturn fn(...args);\n}\n", "import type {\n\tColumnBuilderBase,\n\tColumnBuilderBaseConfig,\n\tColumnBuilderExtraConfig,\n\tColumnBuilderRuntimeConfig,\n\tColumnDataType,\n\tHasGenerated,\n\tMakeColumnConfig,\n} from '~/column-builder.ts';\nimport { ColumnBuilder } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { Column } from '~/column.ts';\nimport { entityKind, is } from '~/entity.ts';\nimport type { Simplify, Update } from '~/utils.ts';\n\nimport type { ForeignKey, UpdateDeleteAction } from '~/pg-core/foreign-keys.ts';\nimport { ForeignKeyBuilder } from '~/pg-core/foreign-keys.ts';\nimport type { AnyPgTable, PgTable } from '~/pg-core/table.ts';\nimport type { SQL } from '~/sql/sql.ts';\nimport { iife } from '~/tracing-utils.ts';\nimport type { PgIndexOpClass } from '../indexes.ts';\nimport { uniqueKeyName } from '../unique-constraint.ts';\nimport { makePgArray, parsePgArray } from '../utils/array.ts';\n\nexport interface ReferenceConfig {\n\tref: () => PgColumn;\n\tactions: {\n\t\tonUpdate?: UpdateDeleteAction;\n\t\tonDelete?: UpdateDeleteAction;\n\t};\n}\n\nexport interface PgColumnBuilderBase<\n\tT extends ColumnBuilderBaseConfig<ColumnDataType, string> = ColumnBuilderBaseConfig<ColumnDataType, string>,\n\tTTypeConfig extends object = object,\n> extends ColumnBuilderBase<T, TTypeConfig & { dialect: 'pg' }> {}\n\nexport abstract class PgColumnBuilder<\n\tT extends ColumnBuilderBaseConfig<ColumnDataType, string> = ColumnBuilderBaseConfig<ColumnDataType, string>,\n\tTRuntimeConfig extends object = object,\n\tTTypeConfig extends object = object,\n\tTExtraConfig extends ColumnBuilderExtraConfig = ColumnBuilderExtraConfig,\n> extends ColumnBuilder<T, TRuntimeConfig, TTypeConfig & { dialect: 'pg' }, TExtraConfig>\n\timplements PgColumnBuilderBase<T, TTypeConfig>\n{\n\tprivate foreignKeyConfigs: ReferenceConfig[] = [];\n\n\tstatic override readonly [entityKind]: string = 'PgColumnBuilder';\n\n\tarray<TSize extends number | undefined = undefined>(size?: TSize): PgArrayBuilder<\n\t\t& {\n\t\t\tname: T['name'];\n\t\t\tdataType: 'array';\n\t\t\tcolumnType: 'PgArray';\n\t\t\tdata: T['data'][];\n\t\t\tdriverParam: T['driverParam'][] | string;\n\t\t\tenumValues: T['enumValues'];\n\t\t\tsize: TSize;\n\t\t\tbaseBuilder: T;\n\t\t}\n\t\t& (T extends { notNull: true } ? { notNull: true } : {})\n\t\t& (T extends { hasDefault: true } ? { hasDefault: true } : {}),\n\t\tT\n\t> {\n\t\treturn new PgArrayBuilder(this.config.name, this as PgColumnBuilder<any, any>, size as any);\n\t}\n\n\treferences(\n\t\tref: ReferenceConfig['ref'],\n\t\tactions: ReferenceConfig['actions'] = {},\n\t): this {\n\t\tthis.foreignKeyConfigs.push({ ref, actions });\n\t\treturn this;\n\t}\n\n\tunique(\n\t\tname?: string,\n\t\tconfig?: { nulls: 'distinct' | 'not distinct' },\n\t): this {\n\t\tthis.config.isUnique = true;\n\t\tthis.config.uniqueName = name;\n\t\tthis.config.uniqueType = config?.nulls;\n\t\treturn this;\n\t}\n\n\tgeneratedAlwaysAs(as: SQL | T['data'] | (() => SQL)): HasGenerated<this, {\n\t\ttype: 'always';\n\t}> {\n\t\tthis.config.generated = {\n\t\t\tas,\n\t\t\ttype: 'always',\n\t\t\tmode: 'stored',\n\t\t};\n\t\treturn this as HasGenerated<this, {\n\t\t\ttype: 'always';\n\t\t}>;\n\t}\n\n\t/** @internal */\n\tbuildForeignKeys(column: PgColumn, table: PgTable): ForeignKey[] {\n\t\treturn this.foreignKeyConfigs.map(({ ref, actions }) => {\n\t\t\treturn iife(\n\t\t\t\t(ref, actions) => {\n\t\t\t\t\tconst builder = new ForeignKeyBuilder(() => {\n\t\t\t\t\t\tconst foreignColumn = ref();\n\t\t\t\t\t\treturn { columns: [column], foreignColumns: [foreignColumn] };\n\t\t\t\t\t});\n\t\t\t\t\tif (actions.onUpdate) {\n\t\t\t\t\t\tbuilder.onUpdate(actions.onUpdate);\n\t\t\t\t\t}\n\t\t\t\t\tif (actions.onDelete) {\n\t\t\t\t\t\tbuilder.onDelete(actions.onDelete);\n\t\t\t\t\t}\n\t\t\t\t\treturn builder.build(table);\n\t\t\t\t},\n\t\t\t\tref,\n\t\t\t\tactions,\n\t\t\t);\n\t\t});\n\t}\n\n\t/** @internal */\n\tabstract build<TTableName extends string>(\n\t\ttable: AnyPgTable<{ name: TTableName }>,\n\t): PgColumn<MakeColumnConfig<T, TTableName>>;\n\n\t/** @internal */\n\tbuildExtraConfigColumn<TTableName extends string>(\n\t\ttable: AnyPgTable<{ name: TTableName }>,\n\t): ExtraConfigColumn {\n\t\treturn new ExtraConfigColumn(table, this.config);\n\t}\n}\n\n// To understand how to use `PgColumn` and `PgColumn`, see `Column` and `AnyColumn` documentation.\nexport abstract class PgColumn<\n\tT extends ColumnBaseConfig<ColumnDataType, string> = ColumnBaseConfig<ColumnDataType, string>,\n\tTRuntimeConfig extends object = {},\n\tTTypeConfig extends object = {},\n> extends Column<T, TRuntimeConfig, TTypeConfig & { dialect: 'pg' }> {\n\tstatic override readonly [entityKind]: string = 'PgColumn';\n\n\tconstructor(\n\t\toverride readonly table: PgTable,\n\t\tconfig: ColumnBuilderRuntimeConfig<T['data'], TRuntimeConfig>,\n\t) {\n\t\tif (!config.uniqueName) {\n\t\t\tconfig.uniqueName = uniqueKeyName(table, [config.name]);\n\t\t}\n\t\tsuper(table, config);\n\t}\n}\n\nexport type IndexedExtraConfigType = { order?: 'asc' | 'desc'; nulls?: 'first' | 'last'; opClass?: string };\n\nexport class ExtraConfigColumn<\n\tT extends ColumnBaseConfig<ColumnDataType, string> = ColumnBaseConfig<ColumnDataType, string>,\n> extends PgColumn<T, IndexedExtraConfigType> {\n\tstatic override readonly [entityKind]: string = 'ExtraConfigColumn';\n\n\toverride getSQLType(): string {\n\t\treturn this.getSQLType();\n\t}\n\n\tindexConfig: IndexedExtraConfigType = {\n\t\torder: this.config.order ?? 'asc',\n\t\tnulls: this.config.nulls ?? 'last',\n\t\topClass: this.config.opClass,\n\t};\n\tdefaultConfig: IndexedExtraConfigType = {\n\t\torder: 'asc',\n\t\tnulls: 'last',\n\t\topClass: undefined,\n\t};\n\n\tasc(): Omit<this, 'asc' | 'desc'> {\n\t\tthis.indexConfig.order = 'asc';\n\t\treturn this;\n\t}\n\n\tdesc(): Omit<this, 'asc' | 'desc'> {\n\t\tthis.indexConfig.order = 'desc';\n\t\treturn this;\n\t}\n\n\tnullsFirst(): Omit<this, 'nullsFirst' | 'nullsLast'> {\n\t\tthis.indexConfig.nulls = 'first';\n\t\treturn this;\n\t}\n\n\tnullsLast(): Omit<this, 'nullsFirst' | 'nullsLast'> {\n\t\tthis.indexConfig.nulls = 'last';\n\t\treturn this;\n\t}\n\n\t/**\n\t * ### PostgreSQL documentation quote\n\t *\n\t * > An operator class with optional parameters can be specified for each column of an index.\n\t * The operator class identifies the operators to be used by the index for that column.\n\t * For example, a B-tree index on four-byte integers would use the int4_ops class;\n\t * this operator class includes comparison functions for four-byte integers.\n\t * In practice the default operator class for the column's data type is usually sufficient.\n\t * The main point of having operator classes is that for some data types, there could be more than one meaningful ordering.\n\t * For example, we might want to sort a complex-number data type either by absolute value or by real part.\n\t * We could do this by defining two operator classes for the data type and then selecting the proper class when creating an index.\n\t * More information about operator classes check:\n\t *\n\t * ### Useful links\n\t * https://www.postgresql.org/docs/current/sql-createindex.html\n\t *\n\t * https://www.postgresql.org/docs/current/indexes-opclass.html\n\t *\n\t * https://www.postgresql.org/docs/current/xindex.html\n\t *\n\t * ### Additional types\n\t * If you have the `pg_vector` extension installed in your database, you can use the\n\t * `vector_l2_ops`, `vector_ip_ops`, `vector_cosine_ops`, `vector_l1_ops`, `bit_hamming_ops`, `bit_jaccard_ops`, `halfvec_l2_ops`, `sparsevec_l2_ops` options, which are predefined types.\n\t *\n\t * **You can always specify any string you want in the operator class, in case Drizzle doesn't have it natively in its types**\n\t *\n\t * @param opClass\n\t * @returns\n\t */\n\top(opClass: PgIndexOpClass): Omit<this, 'op'> {\n\t\tthis.indexConfig.opClass = opClass;\n\t\treturn this;\n\t}\n}\n\nexport class IndexedColumn {\n\tstatic readonly [entityKind]: string = 'IndexedColumn';\n\tconstructor(\n\t\tname: string | undefined,\n\t\tkeyAsName: boolean,\n\t\ttype: string,\n\t\tindexConfig: IndexedExtraConfigType,\n\t) {\n\t\tthis.name = name;\n\t\tthis.keyAsName = keyAsName;\n\t\tthis.type = type;\n\t\tthis.indexConfig = indexConfig;\n\t}\n\n\tname: string | undefined;\n\tkeyAsName: boolean;\n\ttype: string;\n\tindexConfig: IndexedExtraConfigType;\n}\n\nexport type AnyPgColumn<TPartial extends Partial<ColumnBaseConfig<ColumnDataType, string>> = {}> = PgColumn<\n\tRequired<Update<ColumnBaseConfig<ColumnDataType, string>, TPartial>>\n>;\n\nexport type PgArrayColumnBuilderBaseConfig = ColumnBuilderBaseConfig<'array', 'PgArray'> & {\n\tsize: number | undefined;\n\tbaseBuilder: ColumnBuilderBaseConfig<ColumnDataType, string>;\n};\n\nexport class PgArrayBuilder<\n\tT extends PgArrayColumnBuilderBaseConfig,\n\tTBase extends ColumnBuilderBaseConfig<ColumnDataType, string> | PgArrayColumnBuilderBaseConfig,\n> extends PgColumnBuilder<\n\tT,\n\t{\n\t\tbaseBuilder: TBase extends PgArrayColumnBuilderBaseConfig ? PgArrayBuilder<\n\t\t\t\tTBase,\n\t\t\t\tTBase extends { baseBuilder: infer TBaseBuilder extends ColumnBuilderBaseConfig<any, any> } ? TBaseBuilder\n\t\t\t\t\t: never\n\t\t\t>\n\t\t\t: PgColumnBuilder<TBase, {}, Simplify<Omit<TBase, keyof ColumnBuilderBaseConfig<any, any>>>>;\n\t\tsize: T['size'];\n\t},\n\t{\n\t\tbaseBuilder: TBase extends PgArrayColumnBuilderBaseConfig ? PgArrayBuilder<\n\t\t\t\tTBase,\n\t\t\t\tTBase extends { baseBuilder: infer TBaseBuilder extends ColumnBuilderBaseConfig<any, any> } ? TBaseBuilder\n\t\t\t\t\t: never\n\t\t\t>\n\t\t\t: PgColumnBuilder<TBase, {}, Simplify<Omit<TBase, keyof ColumnBuilderBaseConfig<any, any>>>>;\n\t\tsize: T['size'];\n\t}\n> {\n\tstatic override readonly [entityKind] = 'PgArrayBuilder';\n\n\tconstructor(\n\t\tname: string,\n\t\tbaseBuilder: PgArrayBuilder<T, TBase>['config']['baseBuilder'],\n\t\tsize: T['size'],\n\t) {\n\t\tsuper(name, 'array', 'PgArray');\n\t\tthis.config.baseBuilder = baseBuilder;\n\t\tthis.config.size = size;\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyPgTable<{ name: TTableName }>,\n\t): PgArray<MakeColumnConfig<T, TTableName> & { size: T['size']; baseBuilder: T['baseBuilder'] }, TBase> {\n\t\tconst baseColumn = this.config.baseBuilder.build(table);\n\t\treturn new PgArray<MakeColumnConfig<T, TTableName> & { size: T['size']; baseBuilder: T['baseBuilder'] }, TBase>(\n\t\t\ttable as AnyPgTable<{ name: MakeColumnConfig<T, TTableName>['tableName'] }>,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t\tbaseColumn,\n\t\t);\n\t}\n}\n\nexport class PgArray<\n\tT extends ColumnBaseConfig<'array', 'PgArray'> & {\n\t\tsize: number | undefined;\n\t\tbaseBuilder: ColumnBuilderBaseConfig<ColumnDataType, string>;\n\t},\n\tTBase extends ColumnBuilderBaseConfig<ColumnDataType, string>,\n> extends PgColumn<T, {}, { size: T['size']; baseBuilder: T['baseBuilder'] }> {\n\treadonly size: T['size'];\n\n\tstatic override readonly [entityKind]: string = 'PgArray';\n\n\tconstructor(\n\t\ttable: AnyPgTable<{ name: T['tableName'] }>,\n\t\tconfig: PgArrayBuilder<T, TBase>['config'],\n\t\treadonly baseColumn: PgColumn,\n\t\treadonly range?: [number | undefined, number | undefined],\n\t) {\n\t\tsuper(table, config);\n\t\tthis.size = config.size;\n\t}\n\n\tgetSQLType(): string {\n\t\treturn `${this.baseColumn.getSQLType()}[${typeof this.size === 'number' ? this.size : ''}]`;\n\t}\n\n\toverride mapFromDriverValue(value: unknown[] | string): T['data'] {\n\t\tif (typeof value === 'string') {\n\t\t\t// Thank you node-postgres for not parsing enum arrays\n\t\t\tvalue = parsePgArray(value);\n\t\t}\n\t\treturn value.map((v) => this.baseColumn.mapFromDriverValue(v));\n\t}\n\n\toverride mapToDriverValue(value: unknown[], isNestedArray = false): unknown[] | string {\n\t\tconst a = value.map((v) =>\n\t\t\tv === null\n\t\t\t\t? null\n\t\t\t\t: is(this.baseColumn, PgArray)\n\t\t\t\t? this.baseColumn.mapToDriverValue(v as unknown[], true)\n\t\t\t\t: this.baseColumn.mapToDriverValue(v)\n\t\t);\n\t\tif (isNestedArray) return a;\n\t\treturn makePgArray(a);\n\t}\n}\n", "import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyPgTable } from '~/pg-core/table.ts';\nimport type { Writable } from '~/utils.ts';\nimport { PgColumn, PgColumnBuilder } from './common.ts';\n\nexport type PgEnumColumnBuilderInitial<TName extends string, TValues extends [string, ...string[]]> =\n\tPgEnumColumnBuilder<{\n\t\tname: TName;\n\t\tdataType: 'string';\n\t\tcolumnType: 'PgEnumColumn';\n\t\tdata: TValues[number];\n\t\tenumValues: TValues;\n\t\tdriverParam: string;\n\t}>;\n\nconst isPgEnumSym = Symbol.for('drizzle:isPgEnum');\nexport interface PgEnum<TValues extends [string, ...string[]]> {\n\t(): PgEnumColumnBuilderInitial<'', TValues>;\n\t<TName extends string>(name: TName): PgEnumColumnBuilderInitial<TName, TValues>;\n\t<TName extends string>(name?: TName): PgEnumColumnBuilderInitial<TName, TValues>;\n\n\treadonly enumName: string;\n\treadonly enumValues: TValues;\n\treadonly schema: string | undefined;\n\t/** @internal */\n\t[isPgEnumSym]: true;\n}\n\nexport function isPgEnum(obj: unknown): obj is PgEnum<[string, ...string[]]> {\n\treturn !!obj && typeof obj === 'function' && isPgEnumSym in obj && obj[isPgEnumSym] === true;\n}\n\nexport class PgEnumColumnBuilder<\n\tT extends ColumnBuilderBaseConfig<'string', 'PgEnumColumn'> & { enumValues: [string, ...string[]] },\n> extends PgColumnBuilder<T, { enum: PgEnum<T['enumValues']> }> {\n\tstatic override readonly [entityKind]: string = 'PgEnumColumnBuilder';\n\n\tconstructor(name: T['name'], enumInstance: PgEnum<T['enumValues']>) {\n\t\tsuper(name, 'string', 'PgEnumColumn');\n\t\tthis.config.enum = enumInstance;\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyPgTable<{ name: TTableName }>,\n\t): PgEnumColumn<MakeColumnConfig<T, TTableName>> {\n\t\treturn new PgEnumColumn<MakeColumnConfig<T, TTableName>>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n}\n\nexport class PgEnumColumn<T extends ColumnBaseConfig<'string', 'PgEnumColumn'> & { enumValues: [string, ...string[]] }>\n\textends PgColumn<T, { enum: PgEnum<T['enumValues']> }>\n{\n\tstatic override readonly [entityKind]: string = 'PgEnumColumn';\n\n\treadonly enum = this.config.enum;\n\toverride readonly enumValues = this.config.enum.enumValues;\n\n\tconstructor(\n\t\ttable: AnyPgTable<{ name: T['tableName'] }>,\n\t\tconfig: PgEnumColumnBuilder<T>['config'],\n\t) {\n\t\tsuper(table, config);\n\t\tthis.enum = config.enum;\n\t}\n\n\tgetSQLType(): string {\n\t\treturn this.enum.enumName;\n\t}\n}\n\n// Gratitude to zod for the enum function types\nexport function pgEnum<U extends string, T extends Readonly<[U, ...U[]]>>(\n\tenumName: string,\n\tvalues: T | Writable<T>,\n): PgEnum<Writable<T>> {\n\treturn pgEnumWithSchema(enumName, values, undefined);\n}\n\n/** @internal */\nexport function pgEnumWithSchema<U extends string, T extends Readonly<[U, ...U[]]>>(\n\tenumName: string,\n\tvalues: T | Writable<T>,\n\tschema?: string,\n): PgEnum<Writable<T>> {\n\tconst enumInstance: PgEnum<Writable<T>> = Object.assign(\n\t\t<TName extends string>(name?: TName): PgEnumColumnBuilderInitial<TName, Writable<T>> =>\n\t\t\tnew PgEnumColumnBuilder(name ?? '' as TName, enumInstance),\n\t\t{\n\t\t\tenumName,\n\t\t\tenumValues: values,\n\t\t\tschema,\n\t\t\t[isPgEnumSym]: true,\n\t\t} as const,\n\t);\n\n\treturn enumInstance;\n}\n", "import { entityKind } from './entity.ts';\nimport type { SQL, SQLWrapper } from './sql/sql.ts';\n\nexport interface Subquery<\n\t// eslint-disable-next-line @typescript-eslint/no-unused-vars\n\tTAlias extends string = string,\n\t// eslint-disable-next-line @typescript-eslint/no-unused-vars\n\tTSelectedFields extends Record<string, unknown> = Record<string, unknown>,\n> extends SQLWrapper {\n\t// SQLWrapper runtime implementation is defined in 'sql/sql.ts'\n}\nexport class Subquery<\n\tTAlias extends string = string,\n\tTSelectedFields extends Record<string, unknown> = Record<string, unknown>,\n> implements SQLWrapper {\n\tstatic readonly [entityKind]: string = 'Subquery';\n\n\tdeclare _: {\n\t\tbrand: 'Subquery';\n\t\tsql: SQL;\n\t\tselectedFields: TSelectedFields;\n\t\talias: TAlias;\n\t\tisWith: boolean;\n\t};\n\n\tconstructor(sql: SQL, selection: Record<string, unknown>, alias: string, isWith = false) {\n\t\tthis._ = {\n\t\t\tbrand: 'Subquery',\n\t\t\tsql,\n\t\t\tselectedFields: selection as TSelectedFields,\n\t\t\talias: alias as T<PERSON><PERSON><PERSON>,\n\t\t\tisWith,\n\t\t};\n\t}\n\n\t// getSQL(): SQL<unknown> {\n\t// \treturn new SQL([this]);\n\t// }\n}\n\nexport class WithSubquery<\n\tTAlias extends string = string,\n\tTSelection extends Record<string, unknown> = Record<string, unknown>,\n> extends Subquery<TAlias, TSelection> {\n\tstatic override readonly [entityKind]: string = 'WithSubquery';\n}\n\nexport type WithSubqueryWithoutSelection<TAlias extends string> = WithSubquery<TAlias, {}>;\n", "export const ViewBaseConfig = Symbol.for('drizzle:ViewBaseConfig');\n", "import type { Column, GetColumnData } from './column.ts';\nimport { entityKind } from './entity.ts';\nimport type { OptionalKeyOnly, RequiredKeyOnly } from './operations.ts';\nimport type { ExtraConfigColumn } from './pg-core/index.ts';\nimport type { SQLWrapper } from './sql/sql.ts';\nimport { TableName } from './table.utils.ts';\nimport type { Simplify, Update } from './utils.ts';\n\nexport interface TableConfig<TColumn extends Column = Column<any>> {\n\tname: string;\n\tschema: string | undefined;\n\tcolumns: Record<string, TColumn>;\n\tdialect: string;\n}\n\nexport type UpdateTableConfig<T extends TableConfig, TUpdate extends Partial<TableConfig>> = Required<\n\tUpdate<T, TUpdate>\n>;\n\n/** @internal */\nexport const Schema = Symbol.for('drizzle:Schema');\n\n/** @internal */\nexport const Columns = Symbol.for('drizzle:Columns');\n\n/** @internal */\nexport const ExtraConfigColumns = Symbol.for('drizzle:ExtraConfigColumns');\n\n/** @internal */\nexport const OriginalName = Symbol.for('drizzle:OriginalName');\n\n/** @internal */\nexport const BaseName = Symbol.for('drizzle:BaseName');\n\n/** @internal */\nexport const IsAlias = Symbol.for('drizzle:IsAlias');\n\n/** @internal */\nexport const ExtraConfigBuilder = Symbol.for('drizzle:ExtraConfigBuilder');\n\nconst IsDrizzleTable = Symbol.for('drizzle:IsDrizzleTable');\n\nexport interface Table<\n\t// eslint-disable-next-line @typescript-eslint/no-unused-vars\n\tT extends TableConfig = TableConfig,\n> extends SQLWrapper {\n\t// SQLWrapper runtime implementation is defined in 'sql/sql.ts'\n}\n\nexport class Table<T extends TableConfig = TableConfig> implements SQLWrapper {\n\tstatic readonly [entityKind]: string = 'Table';\n\n\tdeclare readonly _: {\n\t\treadonly brand: 'Table';\n\t\treadonly config: T;\n\t\treadonly name: T['name'];\n\t\treadonly schema: T['schema'];\n\t\treadonly columns: T['columns'];\n\t\treadonly inferSelect: InferSelectModel<Table<T>>;\n\t\treadonly inferInsert: InferInsertModel<Table<T>>;\n\t};\n\n\tdeclare readonly $inferSelect: InferSelectModel<Table<T>>;\n\tdeclare readonly $inferInsert: InferInsertModel<Table<T>>;\n\n\t/** @internal */\n\tstatic readonly Symbol = {\n\t\tName: TableName as typeof TableName,\n\t\tSchema: Schema as typeof Schema,\n\t\tOriginalName: OriginalName as typeof OriginalName,\n\t\tColumns: Columns as typeof Columns,\n\t\tExtraConfigColumns: ExtraConfigColumns as typeof ExtraConfigColumns,\n\t\tBaseName: BaseName as typeof BaseName,\n\t\tIsAlias: IsAlias as typeof IsAlias,\n\t\tExtraConfigBuilder: ExtraConfigBuilder as typeof ExtraConfigBuilder,\n\t};\n\n\t/**\n\t * @internal\n\t * Can be changed if the table is aliased.\n\t */\n\t[TableName]: string;\n\n\t/**\n\t * @internal\n\t * Used to store the original name of the table, before any aliasing.\n\t */\n\t[OriginalName]: string;\n\n\t/** @internal */\n\t[Schema]: string | undefined;\n\n\t/** @internal */\n\t[Columns]!: T['columns'];\n\n\t/** @internal */\n\t[ExtraConfigColumns]!: Record<string, ExtraConfigColumn>;\n\n\t/**\n\t *  @internal\n\t * Used to store the table name before the transformation via the `tableCreator` functions.\n\t */\n\t[BaseName]: string;\n\n\t/** @internal */\n\t[IsAlias] = false;\n\n\t/** @internal */\n\t[IsDrizzleTable] = true;\n\n\t/** @internal */\n\t[ExtraConfigBuilder]: ((self: any) => Record<string, unknown> | unknown[]) | undefined = undefined;\n\n\tconstructor(name: string, schema: string | undefined, baseName: string) {\n\t\tthis[TableName] = this[OriginalName] = name;\n\t\tthis[Schema] = schema;\n\t\tthis[BaseName] = baseName;\n\t}\n}\n\nexport function isTable(table: unknown): table is Table {\n\treturn typeof table === 'object' && table !== null && IsDrizzleTable in table;\n}\n\n/**\n * Any table with a specified boundary.\n *\n * @example\n\t```ts\n\t// Any table with a specific name\n\ttype AnyUsersTable = AnyTable<{ name: 'users' }>;\n\t```\n *\n * To describe any table with any config, simply use `Table` without any type arguments, like this:\n *\n\t```ts\n\tfunction needsTable(table: Table) {\n\t\t...\n\t}\n\t```\n */\nexport type AnyTable<TPartial extends Partial<TableConfig>> = Table<UpdateTableConfig<TableConfig, TPartial>>;\n\nexport function getTableName<T extends Table>(table: T): T['_']['name'] {\n\treturn table[TableName];\n}\n\nexport function getTableUniqueName<T extends Table>(table: T): `${T['_']['schema']}.${T['_']['name']}` {\n\treturn `${table[Schema] ?? 'public'}.${table[TableName]}`;\n}\n\nexport type MapColumnName<TName extends string, TColumn extends Column, TDBColumNames extends boolean> =\n\tTDBColumNames extends true ? TColumn['_']['name']\n\t\t: TName;\n\nexport type InferModelFromColumns<\n\tTColumns extends Record<string, Column>,\n\tTInferMode extends 'select' | 'insert' = 'select',\n\tTConfig extends { dbColumnNames: boolean; override?: boolean } = { dbColumnNames: false; override: false },\n> = Simplify<\n\tTInferMode extends 'insert' ?\n\t\t\t& {\n\t\t\t\t[\n\t\t\t\t\tKey in keyof TColumns & string as RequiredKeyOnly<\n\t\t\t\t\t\tMapColumnName<Key, TColumns[Key], TConfig['dbColumnNames']>,\n\t\t\t\t\t\tTColumns[Key]\n\t\t\t\t\t>\n\t\t\t\t]: GetColumnData<TColumns[Key], 'query'>;\n\t\t\t}\n\t\t\t& {\n\t\t\t\t[\n\t\t\t\t\tKey in keyof TColumns & string as OptionalKeyOnly<\n\t\t\t\t\t\tMapColumnName<Key, TColumns[Key], TConfig['dbColumnNames']>,\n\t\t\t\t\t\tTColumns[Key],\n\t\t\t\t\t\tTConfig['override']\n\t\t\t\t\t>\n\t\t\t\t]?: GetColumnData<TColumns[Key], 'query'> | undefined;\n\t\t\t}\n\t\t: {\n\t\t\t[\n\t\t\t\tKey in keyof TColumns & string as MapColumnName<\n\t\t\t\t\tKey,\n\t\t\t\t\tTColumns[Key],\n\t\t\t\t\tTConfig['dbColumnNames']\n\t\t\t\t>\n\t\t\t]: GetColumnData<TColumns[Key], 'query'>;\n\t\t}\n>;\n\n/** @deprecated Use one of the alternatives: {@link InferSelectModel} / {@link InferInsertModel}, or `table.$inferSelect` / `table.$inferInsert`\n */\nexport type InferModel<\n\tTTable extends Table,\n\tTInferMode extends 'select' | 'insert' = 'select',\n\tTConfig extends { dbColumnNames: boolean } = { dbColumnNames: false },\n> = InferModelFromColumns<TTable['_']['columns'], TInferMode, TConfig>;\n\nexport type InferSelectModel<\n\tTTable extends Table,\n\tTConfig extends { dbColumnNames: boolean } = { dbColumnNames: false },\n> = InferModelFromColumns<TTable['_']['columns'], 'select', TConfig>;\n\nexport type InferInsertModel<\n\tTTable extends Table,\n\tTConfig extends { dbColumnNames: boolean; override?: boolean } = { dbColumnNames: false; override: false },\n> = InferModelFromColumns<TTable['_']['columns'], 'insert', TConfig>;\n", "// package.json\nvar version = \"0.39.3\";\n\n// src/version.ts\nvar compatibilityVersion = 10;\nexport {\n  compatibilityVersion,\n  version as npmVersion\n};\n", "import type { Span, Tracer } from '@opentelemetry/api';\nimport { iife } from '~/tracing-utils.ts';\nimport { npmVersion } from '~/version.ts';\n\nlet otel: typeof import('@opentelemetry/api') | undefined;\nlet rawTracer: Tracer | undefined;\n// try {\n// \totel = await import('@opentelemetry/api');\n// } catch (err: any) {\n// \tif (err.code !== 'MODULE_NOT_FOUND' && err.code !== 'ERR_MODULE_NOT_FOUND') {\n// \t\tthrow err;\n// \t}\n// }\n\ntype SpanName =\n\t| 'drizzle.operation'\n\t| 'drizzle.prepareQuery'\n\t| 'drizzle.buildSQL'\n\t| 'drizzle.execute'\n\t| 'drizzle.driver.execute'\n\t| 'drizzle.mapResponse';\n\n/** @internal */\nexport const tracer = {\n\tstartActiveSpan<F extends (span?: Span) => unknown>(name: SpanName, fn: F): ReturnType<F> {\n\t\tif (!otel) {\n\t\t\treturn fn() as ReturnType<F>;\n\t\t}\n\n\t\tif (!rawTracer) {\n\t\t\trawTracer = otel.trace.getTracer('drizzle-orm', npmVersion);\n\t\t}\n\n\t\treturn iife(\n\t\t\t(otel, rawTracer) =>\n\t\t\t\trawTracer.startActiveSpan(\n\t\t\t\t\tname,\n\t\t\t\t\t((span: Span) => {\n\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\treturn fn(span);\n\t\t\t\t\t\t} catch (e) {\n\t\t\t\t\t\t\tspan.setStatus({\n\t\t\t\t\t\t\t\tcode: otel.SpanStatusCode.ERROR,\n\t\t\t\t\t\t\t\tmessage: e instanceof Error ? e.message : 'Unknown error', // eslint-disable-line no-instanceof/no-instanceof\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\tthrow e;\n\t\t\t\t\t\t} finally {\n\t\t\t\t\t\t\tspan.end();\n\t\t\t\t\t\t}\n\t\t\t\t\t}) as F,\n\t\t\t\t),\n\t\t\totel,\n\t\t\trawTracer,\n\t\t);\n\t},\n};\n", "import type { CasingCache } from '~/casing.ts';\nimport { entityKind, is } from '~/entity.ts';\nimport { isPgEnum } from '~/pg-core/columns/enum.ts';\nimport type { SelectResult } from '~/query-builders/select.types.ts';\nimport { Subquery } from '~/subquery.ts';\nimport { tracer } from '~/tracing.ts';\nimport type { Assume, Equal } from '~/utils.ts';\nimport { ViewBaseConfig } from '~/view-common.ts';\nimport type { AnyColumn } from '../column.ts';\nimport { Column } from '../column.ts';\nimport { IsAlias, Table } from '../table.ts';\n\n/**\n * This class is used to indicate a primitive param value that is used in `sql` tag.\n * It is only used on type level and is never instantiated at runtime.\n * If you see a value of this type in the code, its runtime value is actually the primitive param value.\n */\nexport class FakePrimitiveParam {\n\tstatic readonly [entityKind]: string = 'FakePrimitiveParam';\n}\n\nexport type Chunk =\n\t| string\n\t| Table\n\t| View\n\t| AnyColumn\n\t| Name\n\t| Param\n\t| Placeholder\n\t| SQL;\n\nexport interface BuildQueryConfig {\n\tcasing: CasingCache;\n\tescapeName(name: string): string;\n\tescapeParam(num: number, value: unknown): string;\n\tescapeString(str: string): string;\n\tprepareTyping?: (encoder: DriverValueEncoder<unknown, unknown>) => QueryTypingsValue;\n\tparamStartIndex?: { value: number };\n\tinlineParams?: boolean;\n\tinvokeSource?: 'indexes' | undefined;\n}\n\nexport type QueryTypingsValue = 'json' | 'decimal' | 'time' | 'timestamp' | 'uuid' | 'date' | 'none';\n\nexport interface Query {\n\tsql: string;\n\tparams: unknown[];\n}\n\nexport interface QueryWithTypings extends Query {\n\ttypings?: QueryTypingsValue[];\n}\n\n/**\n * Any value that implements the `getSQL` method. The implementations include:\n * - `Table`\n * - `Column`\n * - `View`\n * - `Subquery`\n * - `SQL`\n * - `SQL.Aliased`\n * - `Placeholder`\n * - `Param`\n */\nexport interface SQLWrapper {\n\tgetSQL(): SQL;\n\tshouldOmitSQLParens?(): boolean;\n}\n\nexport function isSQLWrapper(value: unknown): value is SQLWrapper {\n\treturn value !== null && value !== undefined && typeof (value as any).getSQL === 'function';\n}\n\nfunction mergeQueries(queries: QueryWithTypings[]): QueryWithTypings {\n\tconst result: QueryWithTypings = { sql: '', params: [] };\n\tfor (const query of queries) {\n\t\tresult.sql += query.sql;\n\t\tresult.params.push(...query.params);\n\t\tif (query.typings?.length) {\n\t\t\tif (!result.typings) {\n\t\t\t\tresult.typings = [];\n\t\t\t}\n\t\t\tresult.typings.push(...query.typings);\n\t\t}\n\t}\n\treturn result;\n}\n\nexport class StringChunk implements SQLWrapper {\n\tstatic readonly [entityKind]: string = 'StringChunk';\n\n\treadonly value: string[];\n\n\tconstructor(value: string | string[]) {\n\t\tthis.value = Array.isArray(value) ? value : [value];\n\t}\n\n\tgetSQL(): SQL<unknown> {\n\t\treturn new SQL([this]);\n\t}\n}\n\nexport class SQL<T = unknown> implements SQLWrapper {\n\tstatic readonly [entityKind]: string = 'SQL';\n\n\tdeclare _: {\n\t\tbrand: 'SQL';\n\t\ttype: T;\n\t};\n\n\t/** @internal */\n\tdecoder: DriverValueDecoder<T, any> = noopDecoder;\n\tprivate shouldInlineParams = false;\n\n\tconstructor(readonly queryChunks: SQLChunk[]) {}\n\n\tappend(query: SQL): this {\n\t\tthis.queryChunks.push(...query.queryChunks);\n\t\treturn this;\n\t}\n\n\ttoQuery(config: BuildQueryConfig): QueryWithTypings {\n\t\treturn tracer.startActiveSpan('drizzle.buildSQL', (span) => {\n\t\t\tconst query = this.buildQueryFromSourceParams(this.queryChunks, config);\n\t\t\tspan?.setAttributes({\n\t\t\t\t'drizzle.query.text': query.sql,\n\t\t\t\t'drizzle.query.params': JSON.stringify(query.params),\n\t\t\t});\n\t\t\treturn query;\n\t\t});\n\t}\n\n\tbuildQueryFromSourceParams(chunks: SQLChunk[], _config: BuildQueryConfig): Query {\n\t\tconst config = Object.assign({}, _config, {\n\t\t\tinlineParams: _config.inlineParams || this.shouldInlineParams,\n\t\t\tparamStartIndex: _config.paramStartIndex || { value: 0 },\n\t\t});\n\n\t\tconst {\n\t\t\tcasing,\n\t\t\tescapeName,\n\t\t\tescapeParam,\n\t\t\tprepareTyping,\n\t\t\tinlineParams,\n\t\t\tparamStartIndex,\n\t\t} = config;\n\n\t\treturn mergeQueries(chunks.map((chunk): QueryWithTypings => {\n\t\t\tif (is(chunk, StringChunk)) {\n\t\t\t\treturn { sql: chunk.value.join(''), params: [] };\n\t\t\t}\n\n\t\t\tif (is(chunk, Name)) {\n\t\t\t\treturn { sql: escapeName(chunk.value), params: [] };\n\t\t\t}\n\n\t\t\tif (chunk === undefined) {\n\t\t\t\treturn { sql: '', params: [] };\n\t\t\t}\n\n\t\t\tif (Array.isArray(chunk)) {\n\t\t\t\tconst result: SQLChunk[] = [new StringChunk('(')];\n\t\t\t\tfor (const [i, p] of chunk.entries()) {\n\t\t\t\t\tresult.push(p);\n\t\t\t\t\tif (i < chunk.length - 1) {\n\t\t\t\t\t\tresult.push(new StringChunk(', '));\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tresult.push(new StringChunk(')'));\n\t\t\t\treturn this.buildQueryFromSourceParams(result, config);\n\t\t\t}\n\n\t\t\tif (is(chunk, SQL)) {\n\t\t\t\treturn this.buildQueryFromSourceParams(chunk.queryChunks, {\n\t\t\t\t\t...config,\n\t\t\t\t\tinlineParams: inlineParams || chunk.shouldInlineParams,\n\t\t\t\t});\n\t\t\t}\n\n\t\t\tif (is(chunk, Table)) {\n\t\t\t\tconst schemaName = chunk[Table.Symbol.Schema];\n\t\t\t\tconst tableName = chunk[Table.Symbol.Name];\n\t\t\t\treturn {\n\t\t\t\t\tsql: schemaName === undefined || chunk[IsAlias]\n\t\t\t\t\t\t? escapeName(tableName)\n\t\t\t\t\t\t: escapeName(schemaName) + '.' + escapeName(tableName),\n\t\t\t\t\tparams: [],\n\t\t\t\t};\n\t\t\t}\n\n\t\t\tif (is(chunk, Column)) {\n\t\t\t\tconst columnName = casing.getColumnCasing(chunk);\n\t\t\t\tif (_config.invokeSource === 'indexes') {\n\t\t\t\t\treturn { sql: escapeName(columnName), params: [] };\n\t\t\t\t}\n\n\t\t\t\tconst schemaName = chunk.table[Table.Symbol.Schema];\n\t\t\t\treturn {\n\t\t\t\t\tsql: chunk.table[IsAlias] || schemaName === undefined\n\t\t\t\t\t\t? escapeName(chunk.table[Table.Symbol.Name]) + '.' + escapeName(columnName)\n\t\t\t\t\t\t: escapeName(schemaName) + '.' + escapeName(chunk.table[Table.Symbol.Name]) + '.'\n\t\t\t\t\t\t\t+ escapeName(columnName),\n\t\t\t\t\tparams: [],\n\t\t\t\t};\n\t\t\t}\n\n\t\t\tif (is(chunk, View)) {\n\t\t\t\tconst schemaName = chunk[ViewBaseConfig].schema;\n\t\t\t\tconst viewName = chunk[ViewBaseConfig].name;\n\t\t\t\treturn {\n\t\t\t\t\tsql: schemaName === undefined || chunk[ViewBaseConfig].isAlias\n\t\t\t\t\t\t? escapeName(viewName)\n\t\t\t\t\t\t: escapeName(schemaName) + '.' + escapeName(viewName),\n\t\t\t\t\tparams: [],\n\t\t\t\t};\n\t\t\t}\n\n\t\t\tif (is(chunk, Param)) {\n\t\t\t\tif (is(chunk.value, Placeholder)) {\n\t\t\t\t\treturn { sql: escapeParam(paramStartIndex.value++, chunk), params: [chunk], typings: ['none'] };\n\t\t\t\t}\n\n\t\t\t\tconst mappedValue = chunk.value === null ? null : chunk.encoder.mapToDriverValue(chunk.value);\n\n\t\t\t\tif (is(mappedValue, SQL)) {\n\t\t\t\t\treturn this.buildQueryFromSourceParams([mappedValue], config);\n\t\t\t\t}\n\n\t\t\t\tif (inlineParams) {\n\t\t\t\t\treturn { sql: this.mapInlineParam(mappedValue, config), params: [] };\n\t\t\t\t}\n\n\t\t\t\tlet typings: QueryTypingsValue[] = ['none'];\n\t\t\t\tif (prepareTyping) {\n\t\t\t\t\ttypings = [prepareTyping(chunk.encoder)];\n\t\t\t\t}\n\n\t\t\t\treturn { sql: escapeParam(paramStartIndex.value++, mappedValue), params: [mappedValue], typings };\n\t\t\t}\n\n\t\t\tif (is(chunk, Placeholder)) {\n\t\t\t\treturn { sql: escapeParam(paramStartIndex.value++, chunk), params: [chunk], typings: ['none'] };\n\t\t\t}\n\n\t\t\tif (is(chunk, SQL.Aliased) && chunk.fieldAlias !== undefined) {\n\t\t\t\treturn { sql: escapeName(chunk.fieldAlias), params: [] };\n\t\t\t}\n\n\t\t\tif (is(chunk, Subquery)) {\n\t\t\t\tif (chunk._.isWith) {\n\t\t\t\t\treturn { sql: escapeName(chunk._.alias), params: [] };\n\t\t\t\t}\n\t\t\t\treturn this.buildQueryFromSourceParams([\n\t\t\t\t\tnew StringChunk('('),\n\t\t\t\t\tchunk._.sql,\n\t\t\t\t\tnew StringChunk(') '),\n\t\t\t\t\tnew Name(chunk._.alias),\n\t\t\t\t], config);\n\t\t\t}\n\n\t\t\tif (isPgEnum(chunk)) {\n\t\t\t\tif (chunk.schema) {\n\t\t\t\t\treturn { sql: escapeName(chunk.schema) + '.' + escapeName(chunk.enumName), params: [] };\n\t\t\t\t}\n\t\t\t\treturn { sql: escapeName(chunk.enumName), params: [] };\n\t\t\t}\n\n\t\t\tif (isSQLWrapper(chunk)) {\n\t\t\t\tif (chunk.shouldOmitSQLParens?.()) {\n\t\t\t\t\treturn this.buildQueryFromSourceParams([chunk.getSQL()], config);\n\t\t\t\t}\n\t\t\t\treturn this.buildQueryFromSourceParams([\n\t\t\t\t\tnew StringChunk('('),\n\t\t\t\t\tchunk.getSQL(),\n\t\t\t\t\tnew StringChunk(')'),\n\t\t\t\t], config);\n\t\t\t}\n\n\t\t\tif (inlineParams) {\n\t\t\t\treturn { sql: this.mapInlineParam(chunk, config), params: [] };\n\t\t\t}\n\n\t\t\treturn { sql: escapeParam(paramStartIndex.value++, chunk), params: [chunk], typings: ['none'] };\n\t\t}));\n\t}\n\n\tprivate mapInlineParam(\n\t\tchunk: unknown,\n\t\t{ escapeString }: BuildQueryConfig,\n\t): string {\n\t\tif (chunk === null) {\n\t\t\treturn 'null';\n\t\t}\n\t\tif (typeof chunk === 'number' || typeof chunk === 'boolean') {\n\t\t\treturn chunk.toString();\n\t\t}\n\t\tif (typeof chunk === 'string') {\n\t\t\treturn escapeString(chunk);\n\t\t}\n\t\tif (typeof chunk === 'object') {\n\t\t\tconst mappedValueAsString = chunk.toString();\n\t\t\tif (mappedValueAsString === '[object Object]') {\n\t\t\t\treturn escapeString(JSON.stringify(chunk));\n\t\t\t}\n\t\t\treturn escapeString(mappedValueAsString);\n\t\t}\n\t\tthrow new Error('Unexpected param value: ' + chunk);\n\t}\n\n\tgetSQL(): SQL {\n\t\treturn this;\n\t}\n\n\tas(alias: string): SQL.Aliased<T>;\n\t/**\n\t * @deprecated\n\t * Use ``sql<DataType>`query`.as(alias)`` instead.\n\t */\n\tas<TData>(): SQL<TData>;\n\t/**\n\t * @deprecated\n\t * Use ``sql<DataType>`query`.as(alias)`` instead.\n\t */\n\tas<TData>(alias: string): SQL.Aliased<TData>;\n\tas(alias?: string): SQL<T> | SQL.Aliased<T> {\n\t\t// TODO: remove with deprecated overloads\n\t\tif (alias === undefined) {\n\t\t\treturn this;\n\t\t}\n\n\t\treturn new SQL.Aliased(this, alias);\n\t}\n\n\tmapWith<\n\t\tTDecoder extends\n\t\t\t| DriverValueDecoder<any, any>\n\t\t\t| DriverValueDecoder<any, any>['mapFromDriverValue'],\n\t>(decoder: TDecoder): SQL<GetDecoderResult<TDecoder>> {\n\t\tthis.decoder = typeof decoder === 'function' ? { mapFromDriverValue: decoder } : decoder;\n\t\treturn this as SQL<GetDecoderResult<TDecoder>>;\n\t}\n\n\tinlineParams(): this {\n\t\tthis.shouldInlineParams = true;\n\t\treturn this;\n\t}\n\n\t/**\n\t * This method is used to conditionally include a part of the query.\n\t *\n\t * @param condition - Condition to check\n\t * @returns itself if the condition is `true`, otherwise `undefined`\n\t */\n\tif(condition: any | undefined): this | undefined {\n\t\treturn condition ? this : undefined;\n\t}\n}\n\nexport type GetDecoderResult<T> = T extends Column ? T['_']['data'] : T extends\n\t| DriverValueDecoder<infer TData, any>\n\t| DriverValueDecoder<infer TData, any>['mapFromDriverValue'] ? TData\n: never;\n\n/**\n * Any DB name (table, column, index etc.)\n */\nexport class Name implements SQLWrapper {\n\tstatic readonly [entityKind]: string = 'Name';\n\n\tprotected brand!: 'Name';\n\n\tconstructor(readonly value: string) {}\n\n\tgetSQL(): SQL<unknown> {\n\t\treturn new SQL([this]);\n\t}\n}\n\n/**\n * Any DB name (table, column, index etc.)\n * @deprecated Use `sql.identifier` instead.\n */\nexport function name(value: string): Name {\n\treturn new Name(value);\n}\n\nexport interface DriverValueDecoder<TData, TDriverParam> {\n\tmapFromDriverValue(value: TDriverParam): TData;\n}\n\nexport interface DriverValueEncoder<TData, TDriverParam> {\n\tmapToDriverValue(value: TData): TDriverParam | SQL;\n}\n\nexport function isDriverValueEncoder(value: unknown): value is DriverValueEncoder<any, any> {\n\treturn typeof value === 'object' && value !== null && 'mapToDriverValue' in value\n\t\t&& typeof (value as any).mapToDriverValue === 'function';\n}\n\nexport const noopDecoder: DriverValueDecoder<any, any> = {\n\tmapFromDriverValue: (value) => value,\n};\n\nexport const noopEncoder: DriverValueEncoder<any, any> = {\n\tmapToDriverValue: (value) => value,\n};\n\nexport interface DriverValueMapper<TData, TDriverParam>\n\textends DriverValueDecoder<TData, TDriverParam>, DriverValueEncoder<TData, TDriverParam>\n{}\n\nexport const noopMapper: DriverValueMapper<any, any> = {\n\t...noopDecoder,\n\t...noopEncoder,\n};\n\n/** Parameter value that is optionally bound to an encoder (for example, a column). */\nexport class Param<TDataType = unknown, TDriverParamType = TDataType> implements SQLWrapper {\n\tstatic readonly [entityKind]: string = 'Param';\n\n\tprotected brand!: 'BoundParamValue';\n\n\t/**\n\t * @param value - Parameter value\n\t * @param encoder - Encoder to convert the value to a driver parameter\n\t */\n\tconstructor(\n\t\treadonly value: TDataType,\n\t\treadonly encoder: DriverValueEncoder<TDataType, TDriverParamType> = noopEncoder,\n\t) {}\n\n\tgetSQL(): SQL<unknown> {\n\t\treturn new SQL([this]);\n\t}\n}\n\n/** @deprecated Use `sql.param` instead. */\nexport function param<TData, TDriver>(\n\tvalue: TData,\n\tencoder?: DriverValueEncoder<TData, TDriver>,\n): Param<TData, TDriver> {\n\treturn new Param(value, encoder);\n}\n\n/**\n * Anything that can be passed to the `` sql`...` `` tagged function.\n */\nexport type SQLChunk =\n\t| StringChunk\n\t| SQLChunk[]\n\t| SQLWrapper\n\t| SQL\n\t| Table\n\t| View\n\t| Subquery\n\t| AnyColumn\n\t| Param\n\t| Name\n\t| undefined\n\t| FakePrimitiveParam\n\t| Placeholder;\n\nexport function sql<T>(strings: TemplateStringsArray, ...params: any[]): SQL<T>;\n/*\n\tThe type of `params` is specified as `SQLChunk[]`, but that's slightly incorrect -\n\tin runtime, users won't pass `FakePrimitiveParam` instances as `params` - they will pass primitive values\n\twhich will be wrapped in `Param`. That's why the overload specifies `params` as `any[]` and not as `SQLSourceParam[]`.\n\tThis type is used to make our lives easier and the type checker happy.\n*/\nexport function sql(strings: TemplateStringsArray, ...params: SQLChunk[]): SQL {\n\tconst queryChunks: SQLChunk[] = [];\n\tif (params.length > 0 || (strings.length > 0 && strings[0] !== '')) {\n\t\tqueryChunks.push(new StringChunk(strings[0]!));\n\t}\n\tfor (const [paramIndex, param] of params.entries()) {\n\t\tqueryChunks.push(param, new StringChunk(strings[paramIndex + 1]!));\n\t}\n\n\treturn new SQL(queryChunks);\n}\n\nexport namespace sql {\n\texport function empty(): SQL {\n\t\treturn new SQL([]);\n\t}\n\n\t/** @deprecated - use `sql.join()` */\n\texport function fromList(list: SQLChunk[]): SQL {\n\t\treturn new SQL(list);\n\t}\n\n\t/**\n\t * Convenience function to create an SQL query from a raw string.\n\t * @param str The raw SQL query string.\n\t */\n\texport function raw(str: string): SQL {\n\t\treturn new SQL([new StringChunk(str)]);\n\t}\n\n\t/**\n\t * Join a list of SQL chunks with a separator.\n\t * @example\n\t * ```ts\n\t * const query = sql.join([sql`a`, sql`b`, sql`c`]);\n\t * // sql`abc`\n\t * ```\n\t * @example\n\t * ```ts\n\t * const query = sql.join([sql`a`, sql`b`, sql`c`], sql`, `);\n\t * // sql`a, b, c`\n\t * ```\n\t */\n\texport function join(chunks: SQLChunk[], separator?: SQLChunk): SQL {\n\t\tconst result: SQLChunk[] = [];\n\t\tfor (const [i, chunk] of chunks.entries()) {\n\t\t\tif (i > 0 && separator !== undefined) {\n\t\t\t\tresult.push(separator);\n\t\t\t}\n\t\t\tresult.push(chunk);\n\t\t}\n\t\treturn new SQL(result);\n\t}\n\n\t/**\n\t * Create a SQL chunk that represents a DB identifier (table, column, index etc.).\n\t * When used in a query, the identifier will be escaped based on the DB engine.\n\t * For example, in PostgreSQL, identifiers are escaped with double quotes.\n\t *\n\t * **WARNING: This function does not offer any protection against SQL injections, so you must validate any user input beforehand.**\n\t *\n\t * @example ```ts\n\t * const query = sql`SELECT * FROM ${sql.identifier('my-table')}`;\n\t * // 'SELECT * FROM \"my-table\"'\n\t * ```\n\t */\n\texport function identifier(value: string): Name {\n\t\treturn new Name(value);\n\t}\n\n\texport function placeholder<TName extends string>(name: TName): Placeholder<TName> {\n\t\treturn new Placeholder(name);\n\t}\n\n\texport function param<TData, TDriver>(\n\t\tvalue: TData,\n\t\tencoder?: DriverValueEncoder<TData, TDriver>,\n\t): Param<TData, TDriver> {\n\t\treturn new Param(value, encoder);\n\t}\n}\n\nexport namespace SQL {\n\texport class Aliased<T = unknown> implements SQLWrapper {\n\t\tstatic readonly [entityKind]: string = 'SQL.Aliased';\n\n\t\tdeclare _: {\n\t\t\tbrand: 'SQL.Aliased';\n\t\t\ttype: T;\n\t\t};\n\n\t\t/** @internal */\n\t\tisSelectionField = false;\n\n\t\tconstructor(\n\t\t\treadonly sql: SQL,\n\t\t\treadonly fieldAlias: string,\n\t\t) {}\n\n\t\tgetSQL(): SQL {\n\t\t\treturn this.sql;\n\t\t}\n\n\t\t/** @internal */\n\t\tclone() {\n\t\t\treturn new Aliased(this.sql, this.fieldAlias);\n\t\t}\n\t}\n}\n\nexport class Placeholder<TName extends string = string, TValue = any> implements SQLWrapper {\n\tstatic readonly [entityKind]: string = 'Placeholder';\n\n\tdeclare protected: TValue;\n\n\tconstructor(readonly name: TName) {}\n\n\tgetSQL(): SQL {\n\t\treturn new SQL([this]);\n\t}\n}\n\n/** @deprecated Use `sql.placeholder` instead. */\nexport function placeholder<TName extends string>(name: TName): Placeholder<TName> {\n\treturn new Placeholder(name);\n}\n\nexport function fillPlaceholders(params: unknown[], values: Record<string, unknown>): unknown[] {\n\treturn params.map((p) => {\n\t\tif (is(p, Placeholder)) {\n\t\t\tif (!(p.name in values)) {\n\t\t\t\tthrow new Error(`No value for placeholder \"${p.name}\" was provided`);\n\t\t\t}\n\n\t\t\treturn values[p.name];\n\t\t}\n\n\t\tif (is(p, Param) && is(p.value, Placeholder)) {\n\t\t\tif (!(p.value.name in values)) {\n\t\t\t\tthrow new Error(`No value for placeholder \"${p.value.name}\" was provided`);\n\t\t\t}\n\n\t\t\treturn p.encoder.mapToDriverValue(values[p.value.name]);\n\t\t}\n\n\t\treturn p;\n\t});\n}\n\nexport type ColumnsSelection = Record<string, unknown>;\n\nconst IsDrizzleView = Symbol.for('drizzle:IsDrizzleView');\n\nexport abstract class View<\n\tTName extends string = string,\n\tTExisting extends boolean = boolean,\n\tTSelection extends ColumnsSelection = ColumnsSelection,\n> implements SQLWrapper {\n\tstatic readonly [entityKind]: string = 'View';\n\n\tdeclare _: {\n\t\tbrand: 'View';\n\t\tviewBrand: string;\n\t\tname: TName;\n\t\texisting: TExisting;\n\t\tselectedFields: TSelection;\n\t};\n\n\t/** @internal */\n\t[ViewBaseConfig]: {\n\t\tname: TName;\n\t\toriginalName: TName;\n\t\tschema: string | undefined;\n\t\tselectedFields: ColumnsSelection;\n\t\tisExisting: TExisting;\n\t\tquery: TExisting extends true ? undefined : SQL;\n\t\tisAlias: boolean;\n\t};\n\n\t/** @internal */\n\t[IsDrizzleView] = true;\n\n\tdeclare readonly $inferSelect: InferSelectViewModel<View<Assume<TName, string>, TExisting, TSelection>>;\n\n\tconstructor(\n\t\t{ name, schema, selectedFields, query }: {\n\t\t\tname: TName;\n\t\t\tschema: string | undefined;\n\t\t\tselectedFields: ColumnsSelection;\n\t\t\tquery: SQL | undefined;\n\t\t},\n\t) {\n\t\tthis[ViewBaseConfig] = {\n\t\t\tname,\n\t\t\toriginalName: name,\n\t\t\tschema,\n\t\t\tselectedFields,\n\t\t\tquery: query as (TExisting extends true ? undefined : SQL),\n\t\t\tisExisting: !query as TExisting,\n\t\t\tisAlias: false,\n\t\t};\n\t}\n\n\tgetSQL(): SQL<unknown> {\n\t\treturn new SQL([this]);\n\t}\n}\n\nexport function isView(view: unknown): view is View {\n\treturn typeof view === 'object' && view !== null && IsDrizzleView in view;\n}\n\nexport function getViewName<T extends View>(view: T): T['_']['name'] {\n\treturn view[ViewBaseConfig].name;\n}\n\nexport type InferSelectViewModel<TView extends View> =\n\tEqual<TView['_']['selectedFields'], { [x: string]: unknown }> extends true ? { [x: string]: unknown }\n\t\t: SelectResult<\n\t\t\tTView['_']['selectedFields'],\n\t\t\t'single',\n\t\t\tRecord<TView['_']['name'], 'not-null'>\n\t\t>;\n\n// Defined separately from the Column class to resolve circular dependency\nColumn.prototype.getSQL = function() {\n\treturn new SQL([this]);\n};\n\n// Defined separately from the Table class to resolve circular dependency\nTable.prototype.getSQL = function() {\n\treturn new SQL([this]);\n};\n\n// Defined separately from the Column class to resolve circular dependency\nSubquery.prototype.getSQL = function() {\n\treturn new SQL([this]);\n};\n", "import type { AnyColumn } from './column.ts';\nimport { Column } from './column.ts';\nimport { entityKind, is } from './entity.ts';\nimport type { Relation } from './relations.ts';\nimport type { View } from './sql/sql.ts';\nimport { SQL, sql } from './sql/sql.ts';\nimport { Table } from './table.ts';\nimport { ViewBaseConfig } from './view-common.ts';\n\nexport class ColumnAliasProxyHandler<TColumn extends Column> implements ProxyHandler<TColumn> {\n\tstatic readonly [entityKind]: string = 'ColumnAliasProxyHandler';\n\n\tconstructor(private table: Table | View) {}\n\n\tget(columnObj: TColumn, prop: string | symbol): any {\n\t\tif (prop === 'table') {\n\t\t\treturn this.table;\n\t\t}\n\n\t\treturn columnObj[prop as keyof TColumn];\n\t}\n}\n\nexport class TableAliasProxyHandler<T extends Table | View> implements ProxyHandler<T> {\n\tstatic readonly [entityKind]: string = 'TableAliasProxyHandler';\n\n\tconstructor(private alias: string, private replaceOriginalName: boolean) {}\n\n\tget(target: T, prop: string | symbol): any {\n\t\tif (prop === Table.Symbol.IsAlias) {\n\t\t\treturn true;\n\t\t}\n\n\t\tif (prop === Table.Symbol.Name) {\n\t\t\treturn this.alias;\n\t\t}\n\n\t\tif (this.replaceOriginalName && prop === Table.Symbol.OriginalName) {\n\t\t\treturn this.alias;\n\t\t}\n\n\t\tif (prop === ViewBaseConfig) {\n\t\t\treturn {\n\t\t\t\t...target[ViewBaseConfig as keyof typeof target],\n\t\t\t\tname: this.alias,\n\t\t\t\tisAlias: true,\n\t\t\t};\n\t\t}\n\n\t\tif (prop === Table.Symbol.Columns) {\n\t\t\tconst columns = (target as Table)[Table.Symbol.Columns];\n\t\t\tif (!columns) {\n\t\t\t\treturn columns;\n\t\t\t}\n\n\t\t\tconst proxiedColumns: { [key: string]: any } = {};\n\n\t\t\tObject.keys(columns).map((key) => {\n\t\t\t\tproxiedColumns[key] = new Proxy(\n\t\t\t\t\tcolumns[key]!,\n\t\t\t\t\tnew ColumnAliasProxyHandler(new Proxy(target, this)),\n\t\t\t\t);\n\t\t\t});\n\n\t\t\treturn proxiedColumns;\n\t\t}\n\n\t\tconst value = target[prop as keyof typeof target];\n\t\tif (is(value, Column)) {\n\t\t\treturn new Proxy(value as AnyColumn, new ColumnAliasProxyHandler(new Proxy(target, this)));\n\t\t}\n\n\t\treturn value;\n\t}\n}\n\nexport class RelationTableAliasProxyHandler<T extends Relation> implements ProxyHandler<T> {\n\tstatic readonly [entityKind]: string = 'RelationTableAliasProxyHandler';\n\n\tconstructor(private alias: string) {}\n\n\tget(target: T, prop: string | symbol): any {\n\t\tif (prop === 'sourceTable') {\n\t\t\treturn aliasedTable(target.sourceTable, this.alias);\n\t\t}\n\n\t\treturn target[prop as keyof typeof target];\n\t}\n}\n\nexport function aliasedTable<T extends Table | View>(\n\ttable: T,\n\ttableAlias: string,\n): T {\n\treturn new Proxy(table, new TableAliasProxyHandler(tableAlias, false)) as any;\n}\n\nexport function aliasedRelation<T extends Relation>(relation: T, tableAlias: string): T {\n\treturn new Proxy(relation, new RelationTableAliasProxyHandler(tableAlias));\n}\n\nexport function aliasedTableColumn<T extends AnyColumn>(column: T, tableAlias: string): T {\n\treturn new Proxy(\n\t\tcolumn,\n\t\tnew ColumnAliasProxyHandler(new Proxy(column.table, new TableAliasProxyHandler(tableAlias, false))),\n\t);\n}\n\nexport function mapColumnsInAliasedSQLToAlias(query: SQL.Aliased, alias: string): SQL.Aliased {\n\treturn new SQL.Aliased(mapColumnsInSQLToAlias(query.sql, alias), query.fieldAlias);\n}\n\nexport function mapColumnsInSQLToAlias(query: SQL, alias: string): SQL {\n\treturn sql.join(query.queryChunks.map((c) => {\n\t\tif (is(c, Column)) {\n\t\t\treturn aliasedTableColumn(c, alias);\n\t\t}\n\t\tif (is(c, SQL)) {\n\t\t\treturn mapColumnsInSQLToAlias(c, alias);\n\t\t}\n\t\tif (is(c, SQL.Aliased)) {\n\t\t\treturn mapColumnsInAliasedSQLToAlias(c, alias);\n\t\t}\n\t\treturn c;\n\t}));\n}\n", "import { entityKind } from '~/entity.ts';\n\nexport class DrizzleError extends Error {\n\tstatic readonly [entityKind]: string = 'DrizzleError';\n\n\tconstructor({ message, cause }: { message?: string; cause?: unknown }) {\n\t\tsuper(message);\n\t\tthis.name = 'DrizzleError';\n\t\tthis.cause = cause;\n\t}\n}\n\nexport class TransactionRollbackError extends DrizzleError {\n\tstatic override readonly [entityKind]: string = 'TransactionRollbackError';\n\n\tconstructor() {\n\t\tsuper({ message: 'Rollback' });\n\t}\n}\n", "import { type AnyColumn, Column, type GetColumnData } from '~/column.ts';\nimport { is } from '~/entity.ts';\nimport { Table } from '~/table.ts';\nimport {\n\tisDriverValueEncoder,\n\tisSQLWrapper,\n\tParam,\n\tPlaceholder,\n\tSQL,\n\tsql,\n\ttype S<PERSON><PERSON>hunk,\n\ttype SQLWrapper,\n\tStringChunk,\n\tView,\n} from '../sql.ts';\n\nexport function bindIfParam(value: unknown, column: SQLWrapper): SQLChunk {\n\tif (\n\t\tisDriverValueEncoder(column)\n\t\t&& !isSQLWrapper(value)\n\t\t&& !is(value, Param)\n\t\t&& !is(value, Placeholder)\n\t\t&& !is(value, Column)\n\t\t&& !is(value, Table)\n\t\t&& !is(value, View)\n\t) {\n\t\treturn new Param(value, column);\n\t}\n\treturn value as SQLChunk;\n}\n\nexport interface BinaryOperator {\n\t<TColumn extends Column>(\n\t\tleft: TColumn,\n\t\tright: GetColumnData<TColumn, 'raw'> | SQLWrapper,\n\t): SQL;\n\t<T>(left: SQL.Aliased<T>, right: T | SQLWrapper): SQL;\n\t<T extends SQLWrapper>(\n\t\tleft: Exclude<T, SQL.Aliased | Column>,\n\t\tright: unknown,\n\t): SQL;\n}\n\n/**\n * Test that two values are equal.\n *\n * Remember that the SQL standard dictates that\n * two NULL values are not equal, so if you want to test\n * whether a value is null, you may want to use\n * `isNull` instead.\n *\n * ## Examples\n *\n * ```ts\n * // Select cars made by Ford\n * db.select().from(cars)\n *   .where(eq(cars.make, 'Ford'))\n * ```\n *\n * @see isNull for a way to test equality to NULL.\n */\nexport const eq: BinaryOperator = (left: SQLWrapper, right: unknown): SQL => {\n\treturn sql`${left} = ${bindIfParam(right, left)}`;\n};\n\n/**\n * Test that two values are not equal.\n *\n * Remember that the SQL standard dictates that\n * two NULL values are not equal, so if you want to test\n * whether a value is not null, you may want to use\n * `isNotNull` instead.\n *\n * ## Examples\n *\n * ```ts\n * // Select cars not made by Ford\n * db.select().from(cars)\n *   .where(ne(cars.make, 'Ford'))\n * ```\n *\n * @see isNotNull for a way to test whether a value is not null.\n */\nexport const ne: BinaryOperator = (left: SQLWrapper, right: unknown): SQL => {\n\treturn sql`${left} <> ${bindIfParam(right, left)}`;\n};\n\n/**\n * Combine a list of conditions with the `and` operator. Conditions\n * that are equal `undefined` are automatically ignored.\n *\n * ## Examples\n *\n * ```ts\n * db.select().from(cars)\n *   .where(\n *     and(\n *       eq(cars.make, 'Volvo'),\n *       eq(cars.year, 1950),\n *     )\n *   )\n * ```\n */\nexport function and(...conditions: (SQLWrapper | undefined)[]): SQL | undefined;\nexport function and(\n\t...unfilteredConditions: (SQLWrapper | undefined)[]\n): SQL | undefined {\n\tconst conditions = unfilteredConditions.filter(\n\t\t(c): c is Exclude<typeof c, undefined> => c !== undefined,\n\t);\n\n\tif (conditions.length === 0) {\n\t\treturn undefined;\n\t}\n\n\tif (conditions.length === 1) {\n\t\treturn new SQL(conditions);\n\t}\n\n\treturn new SQL([\n\t\tnew StringChunk('('),\n\t\tsql.join(conditions, new StringChunk(' and ')),\n\t\tnew StringChunk(')'),\n\t]);\n}\n\n/**\n * Combine a list of conditions with the `or` operator. Conditions\n * that are equal `undefined` are automatically ignored.\n *\n * ## Examples\n *\n * ```ts\n * db.select().from(cars)\n *   .where(\n *     or(\n *       eq(cars.make, 'GM'),\n *       eq(cars.make, 'Ford'),\n *     )\n *   )\n * ```\n */\nexport function or(...conditions: (SQLWrapper | undefined)[]): SQL | undefined;\nexport function or(\n\t...unfilteredConditions: (SQLWrapper | undefined)[]\n): SQL | undefined {\n\tconst conditions = unfilteredConditions.filter(\n\t\t(c): c is Exclude<typeof c, undefined> => c !== undefined,\n\t);\n\n\tif (conditions.length === 0) {\n\t\treturn undefined;\n\t}\n\n\tif (conditions.length === 1) {\n\t\treturn new SQL(conditions);\n\t}\n\n\treturn new SQL([\n\t\tnew StringChunk('('),\n\t\tsql.join(conditions, new StringChunk(' or ')),\n\t\tnew StringChunk(')'),\n\t]);\n}\n\n/**\n * Negate the meaning of an expression using the `not` keyword.\n *\n * ## Examples\n *\n * ```ts\n * // Select cars _not_ made by GM or Ford.\n * db.select().from(cars)\n *   .where(not(inArray(cars.make, ['GM', 'Ford'])))\n * ```\n */\nexport function not(condition: SQLWrapper): SQL {\n\treturn sql`not ${condition}`;\n}\n\n/**\n * Test that the first expression passed is greater than\n * the second expression.\n *\n * ## Examples\n *\n * ```ts\n * // Select cars made after 2000.\n * db.select().from(cars)\n *   .where(gt(cars.year, 2000))\n * ```\n *\n * @see gte for greater-than-or-equal\n */\nexport const gt: BinaryOperator = (left: SQLWrapper, right: unknown): SQL => {\n\treturn sql`${left} > ${bindIfParam(right, left)}`;\n};\n\n/**\n * Test that the first expression passed is greater than\n * or equal to the second expression. Use `gt` to\n * test whether an expression is strictly greater\n * than another.\n *\n * ## Examples\n *\n * ```ts\n * // Select cars made on or after 2000.\n * db.select().from(cars)\n *   .where(gte(cars.year, 2000))\n * ```\n *\n * @see gt for a strictly greater-than condition\n */\nexport const gte: BinaryOperator = (left: SQLWrapper, right: unknown): SQL => {\n\treturn sql`${left} >= ${bindIfParam(right, left)}`;\n};\n\n/**\n * Test that the first expression passed is less than\n * the second expression.\n *\n * ## Examples\n *\n * ```ts\n * // Select cars made before 2000.\n * db.select().from(cars)\n *   .where(lt(cars.year, 2000))\n * ```\n *\n * @see lte for less-than-or-equal\n */\nexport const lt: BinaryOperator = (left: SQLWrapper, right: unknown): SQL => {\n\treturn sql`${left} < ${bindIfParam(right, left)}`;\n};\n\n/**\n * Test that the first expression passed is less than\n * or equal to the second expression.\n *\n * ## Examples\n *\n * ```ts\n * // Select cars made before 2000.\n * db.select().from(cars)\n *   .where(lte(cars.year, 2000))\n * ```\n *\n * @see lt for a strictly less-than condition\n */\nexport const lte: BinaryOperator = (left: SQLWrapper, right: unknown): SQL => {\n\treturn sql`${left} <= ${bindIfParam(right, left)}`;\n};\n\n/**\n * Test whether the first parameter, a column or expression,\n * has a value from a list passed as the second argument.\n *\n * ## Examples\n *\n * ```ts\n * // Select cars made by Ford or GM.\n * db.select().from(cars)\n *   .where(inArray(cars.make, ['Ford', 'GM']))\n * ```\n *\n * @see notInArray for the inverse of this test\n */\nexport function inArray<T>(\n\tcolumn: SQL.Aliased<T>,\n\tvalues: (T | Placeholder)[] | SQLWrapper,\n): SQL;\nexport function inArray<TColumn extends Column>(\n\tcolumn: TColumn,\n\tvalues: (GetColumnData<TColumn, 'raw'> | Placeholder)[] | SQLWrapper,\n): SQL;\nexport function inArray<T extends SQLWrapper>(\n\tcolumn: Exclude<T, SQL.Aliased | Column>,\n\tvalues: (unknown | Placeholder)[] | SQLWrapper,\n): SQL;\nexport function inArray(\n\tcolumn: SQLWrapper,\n\tvalues: (unknown | Placeholder)[] | SQLWrapper,\n): SQL {\n\tif (Array.isArray(values)) {\n\t\tif (values.length === 0) {\n\t\t\treturn sql`false`;\n\t\t}\n\t\treturn sql`${column} in ${values.map((v) => bindIfParam(v, column))}`;\n\t}\n\n\treturn sql`${column} in ${bindIfParam(values, column)}`;\n}\n\n/**\n * Test whether the first parameter, a column or expression,\n * has a value that is not present in a list passed as the\n * second argument.\n *\n * ## Examples\n *\n * ```ts\n * // Select cars made by any company except Ford or GM.\n * db.select().from(cars)\n *   .where(notInArray(cars.make, ['Ford', 'GM']))\n * ```\n *\n * @see inArray for the inverse of this test\n */\nexport function notInArray<T>(\n\tcolumn: SQL.Aliased<T>,\n\tvalues: (T | Placeholder)[] | SQLWrapper,\n): SQL;\nexport function notInArray<TColumn extends Column>(\n\tcolumn: TColumn,\n\tvalues: (GetColumnData<TColumn, 'raw'> | Placeholder)[] | SQLWrapper,\n): SQL;\nexport function notInArray<T extends SQLWrapper>(\n\tcolumn: Exclude<T, SQL.Aliased | Column>,\n\tvalues: (unknown | Placeholder)[] | SQLWrapper,\n): SQL;\nexport function notInArray(\n\tcolumn: SQLWrapper,\n\tvalues: (unknown | Placeholder)[] | SQLWrapper,\n): SQL {\n\tif (Array.isArray(values)) {\n\t\tif (values.length === 0) {\n\t\t\treturn sql`true`;\n\t\t}\n\t\treturn sql`${column} not in ${values.map((v) => bindIfParam(v, column))}`;\n\t}\n\n\treturn sql`${column} not in ${bindIfParam(values, column)}`;\n}\n\n/**\n * Test whether an expression is NULL. By the SQL standard,\n * NULL is neither equal nor not equal to itself, so\n * it's recommended to use `isNull` and `notIsNull` for\n * comparisons to NULL.\n *\n * ## Examples\n *\n * ```ts\n * // Select cars that have no discontinuedAt date.\n * db.select().from(cars)\n *   .where(isNull(cars.discontinuedAt))\n * ```\n *\n * @see isNotNull for the inverse of this test\n */\nexport function isNull(value: SQLWrapper): SQL {\n\treturn sql`${value} is null`;\n}\n\n/**\n * Test whether an expression is not NULL. By the SQL standard,\n * NULL is neither equal nor not equal to itself, so\n * it's recommended to use `isNull` and `notIsNull` for\n * comparisons to NULL.\n *\n * ## Examples\n *\n * ```ts\n * // Select cars that have been discontinued.\n * db.select().from(cars)\n *   .where(isNotNull(cars.discontinuedAt))\n * ```\n *\n * @see isNull for the inverse of this test\n */\nexport function isNotNull(value: SQLWrapper): SQL {\n\treturn sql`${value} is not null`;\n}\n\n/**\n * Test whether a subquery evaluates to have any rows.\n *\n * ## Examples\n *\n * ```ts\n * // Users whose `homeCity` column has a match in a cities\n * // table.\n * db\n *   .select()\n *   .from(users)\n *   .where(\n *     exists(db.select()\n *       .from(cities)\n *       .where(eq(users.homeCity, cities.id))),\n *   );\n * ```\n *\n * @see notExists for the inverse of this test\n */\nexport function exists(subquery: SQLWrapper): SQL {\n\treturn sql`exists ${subquery}`;\n}\n\n/**\n * Test whether a subquery doesn't include any result\n * rows.\n *\n * ## Examples\n *\n * ```ts\n * // Users whose `homeCity` column doesn't match\n * // a row in the cities table.\n * db\n *   .select()\n *   .from(users)\n *   .where(\n *     notExists(db.select()\n *       .from(cities)\n *       .where(eq(users.homeCity, cities.id))),\n *   );\n * ```\n *\n * @see exists for the inverse of this test\n */\nexport function notExists(subquery: SQLWrapper): SQL {\n\treturn sql`not exists ${subquery}`;\n}\n\n/**\n * Test whether an expression is between two values. This\n * is an easier way to express range tests, which would be\n * expressed mathematically as `x <= a <= y` but in SQL\n * would have to be like `a >= x AND a <= y`.\n *\n * Between is inclusive of the endpoints: if `column`\n * is equal to `min` or `max`, it will be TRUE.\n *\n * ## Examples\n *\n * ```ts\n * // Select cars made between 1990 and 2000\n * db.select().from(cars)\n *   .where(between(cars.year, 1990, 2000))\n * ```\n *\n * @see notBetween for the inverse of this test\n */\nexport function between<T>(\n\tcolumn: SQL.Aliased,\n\tmin: T | SQLWrapper,\n\tmax: T | SQLWrapper,\n): SQL;\nexport function between<TColumn extends AnyColumn>(\n\tcolumn: TColumn,\n\tmin: GetColumnData<TColumn, 'raw'> | SQLWrapper,\n\tmax: GetColumnData<TColumn, 'raw'> | SQLWrapper,\n): SQL;\nexport function between<T extends SQLWrapper>(\n\tcolumn: Exclude<T, SQL.Aliased | Column>,\n\tmin: unknown,\n\tmax: unknown,\n): SQL;\nexport function between(column: SQLWrapper, min: unknown, max: unknown): SQL {\n\treturn sql`${column} between ${bindIfParam(min, column)} and ${\n\t\tbindIfParam(\n\t\t\tmax,\n\t\t\tcolumn,\n\t\t)\n\t}`;\n}\n\n/**\n * Test whether an expression is not between two values.\n *\n * This, like `between`, includes its endpoints, so if\n * the `column` is equal to `min` or `max`, in this case\n * it will evaluate to FALSE.\n *\n * ## Examples\n *\n * ```ts\n * // Exclude cars made in the 1970s\n * db.select().from(cars)\n *   .where(notBetween(cars.year, 1970, 1979))\n * ```\n *\n * @see between for the inverse of this test\n */\nexport function notBetween<T>(\n\tcolumn: SQL.Aliased,\n\tmin: T | SQLWrapper,\n\tmax: T | SQLWrapper,\n): SQL;\nexport function notBetween<TColumn extends AnyColumn>(\n\tcolumn: TColumn,\n\tmin: GetColumnData<TColumn, 'raw'> | SQLWrapper,\n\tmax: GetColumnData<TColumn, 'raw'> | SQLWrapper,\n): SQL;\nexport function notBetween<T extends SQLWrapper>(\n\tcolumn: Exclude<T, SQL.Aliased | Column>,\n\tmin: unknown,\n\tmax: unknown,\n): SQL;\nexport function notBetween(\n\tcolumn: SQLWrapper,\n\tmin: unknown,\n\tmax: unknown,\n): SQL {\n\treturn sql`${column} not between ${\n\t\tbindIfParam(\n\t\t\tmin,\n\t\t\tcolumn,\n\t\t)\n\t} and ${bindIfParam(max, column)}`;\n}\n\n/**\n * Compare a column to a pattern, which can include `%` and `_`\n * characters to match multiple variations. Including `%`\n * in the pattern matches zero or more characters, and including\n * `_` will match a single character.\n *\n * ## Examples\n *\n * ```ts\n * // Select all cars with 'Turbo' in their names.\n * db.select().from(cars)\n *   .where(like(cars.name, '%Turbo%'))\n * ```\n *\n * @see ilike for a case-insensitive version of this condition\n */\nexport function like(column: Column | SQL.Aliased | SQL, value: string | SQLWrapper): SQL {\n\treturn sql`${column} like ${value}`;\n}\n\n/**\n * The inverse of like - this tests that a given column\n * does not match a pattern, which can include `%` and `_`\n * characters to match multiple variations. Including `%`\n * in the pattern matches zero or more characters, and including\n * `_` will match a single character.\n *\n * ## Examples\n *\n * ```ts\n * // Select all cars that don't have \"ROver\" in their name.\n * db.select().from(cars)\n *   .where(notLike(cars.name, '%Rover%'))\n * ```\n *\n * @see like for the inverse condition\n * @see notIlike for a case-insensitive version of this condition\n */\nexport function notLike(column: Column | SQL.Aliased | SQL, value: string | SQLWrapper): SQL {\n\treturn sql`${column} not like ${value}`;\n}\n\n/**\n * Case-insensitively compare a column to a pattern,\n * which can include `%` and `_`\n * characters to match multiple variations. Including `%`\n * in the pattern matches zero or more characters, and including\n * `_` will match a single character.\n *\n * Unlike like, this performs a case-insensitive comparison.\n *\n * ## Examples\n *\n * ```ts\n * // Select all cars with 'Turbo' in their names.\n * db.select().from(cars)\n *   .where(ilike(cars.name, '%Turbo%'))\n * ```\n *\n * @see like for a case-sensitive version of this condition\n */\nexport function ilike(column: Column | SQL.Aliased | SQL, value: string | SQLWrapper): SQL {\n\treturn sql`${column} ilike ${value}`;\n}\n\n/**\n * The inverse of ilike - this case-insensitively tests that a given column\n * does not match a pattern, which can include `%` and `_`\n * characters to match multiple variations. Including `%`\n * in the pattern matches zero or more characters, and including\n * `_` will match a single character.\n *\n * ## Examples\n *\n * ```ts\n * // Select all cars that don't have \"Rover\" in their name.\n * db.select().from(cars)\n *   .where(notLike(cars.name, '%Rover%'))\n * ```\n *\n * @see ilike for the inverse condition\n * @see notLike for a case-sensitive version of this condition\n */\nexport function notIlike(column: Column | SQL.Aliased | SQL, value: string | SQLWrapper): SQL {\n\treturn sql`${column} not ilike ${value}`;\n}\n\n/**\n * Test that a column or expression contains all elements of\n * the list passed as the second argument.\n *\n * ## Throws\n *\n * The argument passed in the second array can't be empty:\n * if an empty is provided, this method will throw.\n *\n * ## Examples\n *\n * ```ts\n * // Select posts where its tags contain \"Typescript\" and \"ORM\".\n * db.select().from(posts)\n *   .where(arrayContains(posts.tags, ['Typescript', 'ORM']))\n * ```\n *\n * @see arrayContained to find if an array contains all elements of a column or expression\n * @see arrayOverlaps to find if a column or expression contains any elements of an array\n */\nexport function arrayContains<T>(\n\tcolumn: SQL.Aliased<T>,\n\tvalues: (T | Placeholder) | SQLWrapper,\n): SQL;\nexport function arrayContains<TColumn extends Column>(\n\tcolumn: TColumn,\n\tvalues: (GetColumnData<TColumn, 'raw'> | Placeholder) | SQLWrapper,\n): SQL;\nexport function arrayContains<T extends SQLWrapper>(\n\tcolumn: Exclude<T, SQL.Aliased | Column>,\n\tvalues: (unknown | Placeholder)[] | SQLWrapper,\n): SQL;\nexport function arrayContains(\n\tcolumn: SQLWrapper,\n\tvalues: (unknown | Placeholder)[] | SQLWrapper,\n): SQL {\n\tif (Array.isArray(values)) {\n\t\tif (values.length === 0) {\n\t\t\tthrow new Error('arrayContains requires at least one value');\n\t\t}\n\t\tconst array = sql`${bindIfParam(values, column)}`;\n\t\treturn sql`${column} @> ${array}`;\n\t}\n\n\treturn sql`${column} @> ${bindIfParam(values, column)}`;\n}\n\n/**\n * Test that the list passed as the second argument contains\n * all elements of a column or expression.\n *\n * ## Throws\n *\n * The argument passed in the second array can't be empty:\n * if an empty is provided, this method will throw.\n *\n * ## Examples\n *\n * ```ts\n * // Select posts where its tags contain \"Typescript\", \"ORM\" or both,\n * // but filtering posts that have additional tags.\n * db.select().from(posts)\n *   .where(arrayContained(posts.tags, ['Typescript', 'ORM']))\n * ```\n *\n * @see arrayContains to find if a column or expression contains all elements of an array\n * @see arrayOverlaps to find if a column or expression contains any elements of an array\n */\nexport function arrayContained<T>(\n\tcolumn: SQL.Aliased<T>,\n\tvalues: (T | Placeholder) | SQLWrapper,\n): SQL;\nexport function arrayContained<TColumn extends Column>(\n\tcolumn: TColumn,\n\tvalues: (GetColumnData<TColumn, 'raw'> | Placeholder) | SQLWrapper,\n): SQL;\nexport function arrayContained<T extends SQLWrapper>(\n\tcolumn: Exclude<T, SQL.Aliased | Column>,\n\tvalues: (unknown | Placeholder)[] | SQLWrapper,\n): SQL;\nexport function arrayContained(\n\tcolumn: SQLWrapper,\n\tvalues: (unknown | Placeholder)[] | SQLWrapper,\n): SQL {\n\tif (Array.isArray(values)) {\n\t\tif (values.length === 0) {\n\t\t\tthrow new Error('arrayContained requires at least one value');\n\t\t}\n\t\tconst array = sql`${bindIfParam(values, column)}`;\n\t\treturn sql`${column} <@ ${array}`;\n\t}\n\n\treturn sql`${column} <@ ${bindIfParam(values, column)}`;\n}\n\n/**\n * Test that a column or expression contains any elements of\n * the list passed as the second argument.\n *\n * ## Throws\n *\n * The argument passed in the second array can't be empty:\n * if an empty is provided, this method will throw.\n *\n * ## Examples\n *\n * ```ts\n * // Select posts where its tags contain \"Typescript\", \"ORM\" or both.\n * db.select().from(posts)\n *   .where(arrayOverlaps(posts.tags, ['Typescript', 'ORM']))\n * ```\n *\n * @see arrayContains to find if a column or expression contains all elements of an array\n * @see arrayContained to find if an array contains all elements of a column or expression\n */\nexport function arrayOverlaps<T>(\n\tcolumn: SQL.Aliased<T>,\n\tvalues: (T | Placeholder) | SQLWrapper,\n): SQL;\nexport function arrayOverlaps<TColumn extends Column>(\n\tcolumn: TColumn,\n\tvalues: (GetColumnData<TColumn, 'raw'> | Placeholder) | SQLWrapper,\n): SQL;\nexport function arrayOverlaps<T extends SQLWrapper>(\n\tcolumn: Exclude<T, SQL.Aliased | Column>,\n\tvalues: (unknown | Placeholder)[] | SQLWrapper,\n): SQL;\nexport function arrayOverlaps(\n\tcolumn: SQLWrapper,\n\tvalues: (unknown | Placeholder)[] | SQLWrapper,\n): SQL {\n\tif (Array.isArray(values)) {\n\t\tif (values.length === 0) {\n\t\t\tthrow new Error('arrayOverlaps requires at least one value');\n\t\t}\n\t\tconst array = sql`${bindIfParam(values, column)}`;\n\t\treturn sql`${column} && ${array}`;\n\t}\n\n\treturn sql`${column} && ${bindIfParam(values, column)}`;\n}\n", "import type { AnyColumn } from '../../column.ts';\nimport type { SQL, SQLWrapper } from '../sql.ts';\nimport { sql } from '../sql.ts';\n\n/**\n * Used in sorting, this specifies that the given\n * column or expression should be sorted in ascending\n * order. By the SQL standard, ascending order is the\n * default, so it is not usually necessary to specify\n * ascending sort order.\n *\n * ## Examples\n *\n * ```ts\n * // Return cars, starting with the oldest models\n * // and going in ascending order to the newest.\n * db.select().from(cars)\n *   .orderBy(asc(cars.year));\n * ```\n *\n * @see desc to sort in descending order\n */\nexport function asc(column: AnyColumn | SQLWrapper): SQL {\n\treturn sql`${column} asc`;\n}\n\n/**\n * Used in sorting, this specifies that the given\n * column or expression should be sorted in descending\n * order.\n *\n * ## Examples\n *\n * ```ts\n * // Select users, with the most recently created\n * // records coming first.\n * db.select().from(users)\n *   .orderBy(desc(users.createdAt));\n * ```\n *\n * @see asc to sort in ascending order\n */\nexport function desc(column: AnyColumn | SQLWrapper): SQL {\n\treturn sql`${column} desc`;\n}\n", "import { entityKind } from '~/entity.ts';\n\nexport abstract class QueryPromise<T> implements Promise<T> {\n\tstatic readonly [entityKind]: string = 'QueryPromise';\n\n\t[Symbol.toStringTag] = 'QueryPromise';\n\n\tcatch<TResult = never>(\n\t\tonRejected?: ((reason: any) => TResult | PromiseLike<TResult>) | null | undefined,\n\t): Promise<T | TResult> {\n\t\treturn this.then(undefined, onRejected);\n\t}\n\n\tfinally(onFinally?: (() => void) | null | undefined): Promise<T> {\n\t\treturn this.then(\n\t\t\t(value) => {\n\t\t\t\tonFinally?.();\n\t\t\t\treturn value;\n\t\t\t},\n\t\t\t(reason) => {\n\t\t\t\tonFinally?.();\n\t\t\t\tthrow reason;\n\t\t\t},\n\t\t);\n\t}\n\n\tthen<TResult1 = T, TResult2 = never>(\n\t\tonFulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null,\n\t\tonRejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null,\n\t): Promise<TResult1 | TResult2> {\n\t\treturn this.execute().then(onFulfilled, onRejected);\n\t}\n\n\tabstract execute(): Promise<T>;\n}\n", "import type { AnyColumn } from './column.ts';\nimport { Column } from './column.ts';\nimport { is } from './entity.ts';\nimport type { Logger } from './logger.ts';\nimport type { SelectedFieldsOrdered } from './operations.ts';\nimport type { TableLike } from './query-builders/select.types.ts';\nimport { Param, SQL, View } from './sql/sql.ts';\nimport type { DriverValueDecoder } from './sql/sql.ts';\nimport { Subquery } from './subquery.ts';\nimport { getTableName, Table } from './table.ts';\nimport { ViewBaseConfig } from './view-common.ts';\n\n/** @internal */\nexport function mapResultRow<TResult>(\n\tcolumns: SelectedFieldsOrdered<AnyColumn>,\n\trow: unknown[],\n\tjoinsNotNullableMap: Record<string, boolean> | undefined,\n): TResult {\n\t// Key -> nested object key, value -> table name if all fields in the nested object are from the same table, false otherwise\n\tconst nullifyMap: Record<string, string | false> = {};\n\n\tconst result = columns.reduce<Record<string, any>>(\n\t\t(result, { path, field }, columnIndex) => {\n\t\t\tlet decoder: DriverValueDecoder<unknown, unknown>;\n\t\t\tif (is(field, Column)) {\n\t\t\t\tdecoder = field;\n\t\t\t} else if (is(field, SQL)) {\n\t\t\t\tdecoder = field.decoder;\n\t\t\t} else {\n\t\t\t\tdecoder = field.sql.decoder;\n\t\t\t}\n\t\t\tlet node = result;\n\t\t\tfor (const [pathChunkIndex, pathChunk] of path.entries()) {\n\t\t\t\tif (pathChunkIndex < path.length - 1) {\n\t\t\t\t\tif (!(pathChunk in node)) {\n\t\t\t\t\t\tnode[pathChunk] = {};\n\t\t\t\t\t}\n\t\t\t\t\tnode = node[pathChunk];\n\t\t\t\t} else {\n\t\t\t\t\tconst rawValue = row[columnIndex]!;\n\t\t\t\t\tconst value = node[pathChunk] = rawValue === null ? null : decoder.mapFromDriverValue(rawValue);\n\n\t\t\t\t\tif (joinsNotNullableMap && is(field, Column) && path.length === 2) {\n\t\t\t\t\t\tconst objectName = path[0]!;\n\t\t\t\t\t\tif (!(objectName in nullifyMap)) {\n\t\t\t\t\t\t\tnullifyMap[objectName] = value === null ? getTableName(field.table) : false;\n\t\t\t\t\t\t} else if (\n\t\t\t\t\t\t\ttypeof nullifyMap[objectName] === 'string' && nullifyMap[objectName] !== getTableName(field.table)\n\t\t\t\t\t\t) {\n\t\t\t\t\t\t\tnullifyMap[objectName] = false;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn result;\n\t\t},\n\t\t{},\n\t);\n\n\t// Nullify all nested objects from nullifyMap that are nullable\n\tif (joinsNotNullableMap && Object.keys(nullifyMap).length > 0) {\n\t\tfor (const [objectName, tableName] of Object.entries(nullifyMap)) {\n\t\t\tif (typeof tableName === 'string' && !joinsNotNullableMap[tableName]) {\n\t\t\t\tresult[objectName] = null;\n\t\t\t}\n\t\t}\n\t}\n\n\treturn result as TResult;\n}\n\n/** @internal */\nexport function orderSelectedFields<TColumn extends AnyColumn>(\n\tfields: Record<string, unknown>,\n\tpathPrefix?: string[],\n): SelectedFieldsOrdered<TColumn> {\n\treturn Object.entries(fields).reduce<SelectedFieldsOrdered<AnyColumn>>((result, [name, field]) => {\n\t\tif (typeof name !== 'string') {\n\t\t\treturn result;\n\t\t}\n\n\t\tconst newPath = pathPrefix ? [...pathPrefix, name] : [name];\n\t\tif (is(field, Column) || is(field, SQL) || is(field, SQL.Aliased)) {\n\t\t\tresult.push({ path: newPath, field });\n\t\t} else if (is(field, Table)) {\n\t\t\tresult.push(...orderSelectedFields(field[Table.Symbol.Columns], newPath));\n\t\t} else {\n\t\t\tresult.push(...orderSelectedFields(field as Record<string, unknown>, newPath));\n\t\t}\n\t\treturn result;\n\t}, []) as SelectedFieldsOrdered<TColumn>;\n}\n\nexport function haveSameKeys(left: Record<string, unknown>, right: Record<string, unknown>) {\n\tconst leftKeys = Object.keys(left);\n\tconst rightKeys = Object.keys(right);\n\n\tif (leftKeys.length !== rightKeys.length) {\n\t\treturn false;\n\t}\n\n\tfor (const [index, key] of leftKeys.entries()) {\n\t\tif (key !== rightKeys[index]) {\n\t\t\treturn false;\n\t\t}\n\t}\n\n\treturn true;\n}\n\n/** @internal */\nexport function mapUpdateSet(table: Table, values: Record<string, unknown>): UpdateSet {\n\tconst entries: [string, UpdateSet[string]][] = Object.entries(values)\n\t\t.filter(([, value]) => value !== undefined)\n\t\t.map(([key, value]) => {\n\t\t\t// eslint-disable-next-line unicorn/prefer-ternary\n\t\t\tif (is(value, SQL) || is(value, Column)) {\n\t\t\t\treturn [key, value];\n\t\t\t} else {\n\t\t\t\treturn [key, new Param(value, table[Table.Symbol.Columns][key])];\n\t\t\t}\n\t\t});\n\n\tif (entries.length === 0) {\n\t\tthrow new Error('No values to set');\n\t}\n\n\treturn Object.fromEntries(entries);\n}\n\nexport type UpdateSet = Record<string, SQL | Param | AnyColumn | null | undefined>;\n\nexport type OneOrMany<T> = T | T[];\n\nexport type Update<T, TUpdate> =\n\t& {\n\t\t[K in Exclude<keyof T, keyof TUpdate>]: T[K];\n\t}\n\t& TUpdate;\n\nexport type Simplify<T> =\n\t& {\n\t\t// @ts-ignore - \"Type parameter 'K' has a circular constraint\", not sure why\n\t\t[K in keyof T]: T[K];\n\t}\n\t& {};\n\nexport type SimplifyMappedType<T> = [T] extends [unknown] ? T : never;\n\nexport type ShallowRecord<K extends keyof any, T> = SimplifyMappedType<{ [P in K]: T }>;\n\nexport type Assume<T, U> = T extends U ? T : U;\n\nexport type Equal<X, Y> = (<T>() => T extends X ? 1 : 2) extends (<T>() => T extends Y ? 1 : 2) ? true : false;\n\nexport interface DrizzleTypeError<T extends string> {\n\t$drizzleTypeError: T;\n}\n\nexport type ValueOrArray<T> = T | T[];\n\n/** @internal */\nexport function applyMixins(baseClass: any, extendedClasses: any[]) {\n\tfor (const extendedClass of extendedClasses) {\n\t\tfor (const name of Object.getOwnPropertyNames(extendedClass.prototype)) {\n\t\t\tif (name === 'constructor') continue;\n\n\t\t\tObject.defineProperty(\n\t\t\t\tbaseClass.prototype,\n\t\t\t\tname,\n\t\t\t\tObject.getOwnPropertyDescriptor(extendedClass.prototype, name) || Object.create(null),\n\t\t\t);\n\t\t}\n\t}\n}\n\nexport type Or<T1, T2> = T1 extends true ? true : T2 extends true ? true : false;\n\nexport type IfThenElse<If, Then, Else> = If extends true ? Then : Else;\n\nexport type PromiseOf<T> = T extends Promise<infer U> ? U : T;\n\nexport type Writable<T> = {\n\t-readonly [P in keyof T]: T[P];\n};\n\nexport function getTableColumns<T extends Table>(table: T): T['_']['columns'] {\n\treturn table[Table.Symbol.Columns];\n}\n\nexport function getViewSelectedFields<T extends View>(view: T): T['_']['selectedFields'] {\n\treturn view[ViewBaseConfig].selectedFields;\n}\n\n/** @internal */\nexport function getTableLikeName(table: TableLike): string | undefined {\n\treturn is(table, Subquery)\n\t\t? table._.alias\n\t\t: is(table, View)\n\t\t? table[ViewBaseConfig].name\n\t\t: is(table, SQL)\n\t\t? undefined\n\t\t: table[Table.Symbol.IsAlias]\n\t\t? table[Table.Symbol.Name]\n\t\t: table[Table.Symbol.BaseName];\n}\n\nexport type ColumnsWithTable<\n\tTTableName extends string,\n\tTForeignTableName extends string,\n\tTColumns extends AnyColumn<{ tableName: TTableName }>[],\n> = { [Key in keyof TColumns]: AnyColumn<{ tableName: TForeignTableName }> };\n\nexport type Casing = 'snake_case' | 'camelCase';\n\nexport interface DrizzleConfig<TSchema extends Record<string, unknown> = Record<string, never>> {\n\tlogger?: boolean | Logger;\n\tschema?: TSchema;\n\tcasing?: Casing;\n}\nexport type ValidateShape<T, ValidShape, TResult = T> = T extends ValidShape\n\t? Exclude<keyof T, keyof ValidShape> extends never ? TResult\n\t: DrizzleTypeError<\n\t\t`Invalid key(s): ${Exclude<(keyof T) & (string | number | bigint | boolean | null | undefined), keyof ValidShape>}`\n\t>\n\t: never;\n\nexport type KnownKeysOnly<T, U> = {\n\t[K in keyof T]: K extends keyof U ? T[K] : never;\n};\n\nexport type IsAny<T> = 0 extends (1 & T) ? true : false;\n\n/** @internal */\nexport function getColumnNameAndConfig<\n\tTConfig extends Record<string, any> | undefined,\n>(a: string | TConfig | undefined, b: TConfig | undefined) {\n\treturn {\n\t\tname: typeof a === 'string' && a.length > 0 ? a : '' as string,\n\t\tconfig: typeof a === 'object' ? a : b as TConfig,\n\t};\n}\n\nexport type IfNotImported<T, Y, N> = unknown extends T ? Y : N;\n\nexport type ImportTypeError<TPackageName extends string> =\n\t`Please install \\`${TPackageName}\\` to allow Drizzle ORM to connect to the database`;\n\nexport type RequireAtLeastOne<T, Keys extends keyof T = keyof T> = Keys extends any\n\t? Required<Pick<T, Keys>> & Partial<Omit<T, Keys>>\n\t: never;\n\ntype ExpectedConfigShape = {\n\tlogger?: boolean | {\n\t\tlogQuery(query: string, params: unknown[]): void;\n\t};\n\tschema?: Record<string, never>;\n\tcasing?: 'snake_case' | 'camelCase';\n};\n\n// If this errors, you must update config shape checker function with new config specs\nconst _: DrizzleConfig = {} as ExpectedConfigShape;\nconst __: ExpectedConfigShape = {} as DrizzleConfig;\n\nexport function isConfig(data: any): boolean {\n\tif (typeof data !== 'object' || data === null) return false;\n\n\tif (data.constructor.name !== 'Object') return false;\n\n\tif ('logger' in data) {\n\t\tconst type = typeof data['logger'];\n\t\tif (\n\t\t\ttype !== 'boolean' && (type !== 'object' || typeof data['logger']['logQuery'] !== 'function')\n\t\t\t&& type !== 'undefined'\n\t\t) return false;\n\n\t\treturn true;\n\t}\n\n\tif ('schema' in data) {\n\t\tconst type = typeof data['logger'];\n\t\tif (type !== 'object' && type !== 'undefined') return false;\n\n\t\treturn true;\n\t}\n\n\tif ('casing' in data) {\n\t\tconst type = typeof data['logger'];\n\t\tif (type !== 'string' && type !== 'undefined') return false;\n\n\t\treturn true;\n\t}\n\n\tif ('mode' in data) {\n\t\tif (data['mode'] !== 'default' || data['mode'] !== 'planetscale' || data['mode'] !== undefined) return false;\n\n\t\treturn true;\n\t}\n\n\tif ('connection' in data) {\n\t\tconst type = typeof data['connection'];\n\t\tif (type !== 'string' && type !== 'object' && type !== 'undefined') return false;\n\n\t\treturn true;\n\t}\n\n\tif ('client' in data) {\n\t\tconst type = typeof data['client'];\n\t\tif (type !== 'object' && type !== 'function' && type !== 'undefined') return false;\n\n\t\treturn true;\n\t}\n\n\tif (Object.keys(data).length === 0) return true;\n\n\treturn false;\n}\n\nexport type NeonAuthToken = string | (() => string | Promise<string>);\n", "import type { ColumnBuilderBaseConfig, ColumnDataType, GeneratedIdentityConfig, IsIdentity } from '~/column-builder.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { PgSequenceOptions } from '../sequence.ts';\nimport { PgColumnBuilder } from './common.ts';\n\nexport abstract class PgIntColumnBaseBuilder<\n\tT extends ColumnBuilderBaseConfig<ColumnDataType, string>,\n> extends PgColumnBuilder<\n\tT,\n\t{ generatedIdentity: GeneratedIdentityConfig }\n> {\n\tstatic override readonly [entityKind]: string = 'PgIntColumnBaseBuilder';\n\n\tgeneratedAlwaysAsIdentity(\n\t\tsequence?: PgSequenceOptions & { name?: string },\n\t): IsIdentity<this, 'always'> {\n\t\tif (sequence) {\n\t\t\tconst { name, ...options } = sequence;\n\t\t\tthis.config.generatedIdentity = {\n\t\t\t\ttype: 'always',\n\t\t\t\tsequenceName: name,\n\t\t\t\tsequenceOptions: options,\n\t\t\t};\n\t\t} else {\n\t\t\tthis.config.generatedIdentity = {\n\t\t\t\ttype: 'always',\n\t\t\t};\n\t\t}\n\n\t\tthis.config.hasDefault = true;\n\t\tthis.config.notNull = true;\n\n\t\treturn this as IsIdentity<this, 'always'>;\n\t}\n\n\tgeneratedByDefaultAsIdentity(\n\t\tsequence?: PgSequenceOptions & { name?: string },\n\t): IsIdentity<this, 'byDefault'> {\n\t\tif (sequence) {\n\t\t\tconst { name, ...options } = sequence;\n\t\t\tthis.config.generatedIdentity = {\n\t\t\t\ttype: 'byDefault',\n\t\t\t\tsequenceName: name,\n\t\t\t\tsequenceOptions: options,\n\t\t\t};\n\t\t} else {\n\t\t\tthis.config.generatedIdentity = {\n\t\t\t\ttype: 'byDefault',\n\t\t\t};\n\t\t}\n\n\t\tthis.config.hasDefault = true;\n\t\tthis.config.notNull = true;\n\n\t\treturn this as IsIdentity<this, 'byDefault'>;\n\t}\n}\n", "import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyPgTable } from '~/pg-core/table.ts';\n\nimport { getColumnNameAndConfig } from '~/utils.ts';\nimport { PgColumn } from './common.ts';\nimport { PgIntColumnBaseBuilder } from './int.common.ts';\n\nexport type PgBigInt53BuilderInitial<TName extends string> = PgBigInt53Builder<{\n\tname: TName;\n\tdataType: 'number';\n\tcolumnType: 'PgBigInt53';\n\tdata: number;\n\tdriverParam: number | string;\n\tenumValues: undefined;\n}>;\n\nexport class PgBigInt53Builder<T extends ColumnBuilderBaseConfig<'number', 'PgBigInt53'>>\n\textends PgIntColumnBaseBuilder<T>\n{\n\tstatic override readonly [entityKind]: string = 'PgBigInt53Builder';\n\n\tconstructor(name: T['name']) {\n\t\tsuper(name, 'number', 'PgBigInt53');\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyPgTable<{ name: TTableName }>,\n\t): PgBigInt53<MakeColumnConfig<T, TTableName>> {\n\t\treturn new PgBigInt53<MakeColumnConfig<T, TTableName>>(table, this.config as ColumnBuilderRuntimeConfig<any, any>);\n\t}\n}\n\nexport class PgBigInt53<T extends ColumnBaseConfig<'number', 'PgBigInt53'>> extends PgColumn<T> {\n\tstatic override readonly [entityKind]: string = 'PgBigInt53';\n\n\tgetSQLType(): string {\n\t\treturn 'bigint';\n\t}\n\n\toverride mapFromDriverValue(value: number | string): number {\n\t\tif (typeof value === 'number') {\n\t\t\treturn value;\n\t\t}\n\t\treturn Number(value);\n\t}\n}\n\nexport type PgBigInt64BuilderInitial<TName extends string> = PgBigInt64Builder<{\n\tname: TName;\n\tdataType: 'bigint';\n\tcolumnType: 'PgBigInt64';\n\tdata: bigint;\n\tdriverParam: string;\n\tenumValues: undefined;\n}>;\n\nexport class PgBigInt64Builder<T extends ColumnBuilderBaseConfig<'bigint', 'PgBigInt64'>>\n\textends PgIntColumnBaseBuilder<T>\n{\n\tstatic override readonly [entityKind]: string = 'PgBigInt64Builder';\n\n\tconstructor(name: T['name']) {\n\t\tsuper(name, 'bigint', 'PgBigInt64');\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyPgTable<{ name: TTableName }>,\n\t): PgBigInt64<MakeColumnConfig<T, TTableName>> {\n\t\treturn new PgBigInt64<MakeColumnConfig<T, TTableName>>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n}\n\nexport class PgBigInt64<T extends ColumnBaseConfig<'bigint', 'PgBigInt64'>> extends PgColumn<T> {\n\tstatic override readonly [entityKind]: string = 'PgBigInt64';\n\n\tgetSQLType(): string {\n\t\treturn 'bigint';\n\t}\n\n\t// eslint-disable-next-line unicorn/prefer-native-coercion-functions\n\toverride mapFromDriverValue(value: string): bigint {\n\t\treturn BigInt(value);\n\t}\n}\n\nexport interface PgBigIntConfig<T extends 'number' | 'bigint' = 'number' | 'bigint'> {\n\tmode: T;\n}\n\nexport function bigint<TMode extends PgBigIntConfig['mode']>(\n\tconfig: PgBigIntConfig<TMode>,\n): TMode extends 'number' ? PgBigInt53BuilderInitial<''> : PgBigInt64BuilderInitial<''>;\nexport function bigint<TName extends string, TMode extends PgBigIntConfig['mode']>(\n\tname: TName,\n\tconfig: PgBigIntConfig<TMode>,\n): TMode extends 'number' ? PgBigInt53BuilderInitial<TName> : PgBigInt64BuilderInitial<TName>;\nexport function bigint(a: string | PgBigIntConfig, b?: PgBigIntConfig) {\n\tconst { name, config } = getColumnNameAndConfig<PgBigIntConfig>(a, b);\n\tif (config.mode === 'number') {\n\t\treturn new PgBigInt53Builder(name);\n\t}\n\treturn new PgBigInt64Builder(name);\n}\n", "import type {\n\tColumnBuilderBaseConfig,\n\tColumnBuilderRuntimeConfig,\n\t<PERSON><PERSON><PERSON><PERSON>,\n\tMakeColumnConfig,\n\tNotNull,\n} from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport { getColumnNameAndConfig } from '~/utils.ts';\nimport type { AnyPgTable } from '../table.ts';\nimport { PgColumn, PgColumnBuilder } from './common.ts';\n\nexport type PgBigSerial53BuilderInitial<TName extends string> = NotNull<\n\tHasDefault<\n\t\tPgBigSerial53Builder<{\n\t\t\tname: TName;\n\t\t\tdataType: 'number';\n\t\t\tcolumnType: 'PgBigSerial53';\n\t\t\tdata: number;\n\t\t\tdriverParam: number;\n\t\t\tenumValues: undefined;\n\t\t}>\n\t>\n>;\n\nexport class PgBigSerial53Builder<T extends ColumnBuilderBaseConfig<'number', 'PgBigSerial53'>>\n\textends PgColumnBuilder<T>\n{\n\tstatic override readonly [entityKind]: string = 'PgBigSerial53Builder';\n\n\tconstructor(name: string) {\n\t\tsuper(name, 'number', 'PgBigSerial53');\n\t\tthis.config.hasDefault = true;\n\t\tthis.config.notNull = true;\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyPgTable<{ name: TTableName }>,\n\t): PgBigSerial53<MakeColumnConfig<T, TTableName>> {\n\t\treturn new PgBigSerial53<MakeColumnConfig<T, TTableName>>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n}\n\nexport class PgBigSerial53<T extends ColumnBaseConfig<'number', 'PgBigSerial53'>> extends PgColumn<T> {\n\tstatic override readonly [entityKind]: string = 'PgBigSerial53';\n\n\tgetSQLType(): string {\n\t\treturn 'bigserial';\n\t}\n\n\toverride mapFromDriverValue(value: number): number {\n\t\tif (typeof value === 'number') {\n\t\t\treturn value;\n\t\t}\n\t\treturn Number(value);\n\t}\n}\n\nexport type PgBigSerial64BuilderInitial<TName extends string> = NotNull<\n\tHasDefault<\n\t\tPgBigSerial64Builder<{\n\t\t\tname: TName;\n\t\t\tdataType: 'bigint';\n\t\t\tcolumnType: 'PgBigSerial64';\n\t\t\tdata: bigint;\n\t\t\tdriverParam: string;\n\t\t\tenumValues: undefined;\n\t\t}>\n\t>\n>;\n\nexport class PgBigSerial64Builder<T extends ColumnBuilderBaseConfig<'bigint', 'PgBigSerial64'>>\n\textends PgColumnBuilder<T>\n{\n\tstatic override readonly [entityKind]: string = 'PgBigSerial64Builder';\n\n\tconstructor(name: string) {\n\t\tsuper(name, 'bigint', 'PgBigSerial64');\n\t\tthis.config.hasDefault = true;\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyPgTable<{ name: TTableName }>,\n\t): PgBigSerial64<MakeColumnConfig<T, TTableName>> {\n\t\treturn new PgBigSerial64<MakeColumnConfig<T, TTableName>>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n}\n\nexport class PgBigSerial64<T extends ColumnBaseConfig<'bigint', 'PgBigSerial64'>> extends PgColumn<T> {\n\tstatic override readonly [entityKind]: string = 'PgBigSerial64';\n\n\tgetSQLType(): string {\n\t\treturn 'bigserial';\n\t}\n\n\t// eslint-disable-next-line unicorn/prefer-native-coercion-functions\n\toverride mapFromDriverValue(value: string): bigint {\n\t\treturn BigInt(value);\n\t}\n}\n\nexport interface PgBigSerialConfig<T extends 'number' | 'bigint' = 'number' | 'bigint'> {\n\tmode: T;\n}\n\nexport function bigserial<TMode extends PgBigSerialConfig['mode']>(\n\tconfig: PgBigSerialConfig<TMode>,\n): TMode extends 'number' ? PgBigSerial53BuilderInitial<''> : PgBigSerial64BuilderInitial<''>;\nexport function bigserial<TName extends string, TMode extends PgBigSerialConfig['mode']>(\n\tname: TName,\n\tconfig: PgBigSerialConfig<TMode>,\n): TMode extends 'number' ? PgBigSerial53BuilderInitial<TName> : PgBigSerial64BuilderInitial<TName>;\nexport function bigserial(a: string | PgBigSerialConfig, b?: PgBigSerialConfig) {\n\tconst { name, config } = getColumnNameAndConfig<PgBigSerialConfig>(a, b);\n\tif (config.mode === 'number') {\n\t\treturn new PgBigSerial53Builder(name);\n\t}\n\treturn new PgBigSerial64Builder(name);\n}\n", "import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyPgTable } from '~/pg-core/table.ts';\nimport { PgColumn, PgColumnBuilder } from './common.ts';\n\nexport type PgBooleanBuilderInitial<TName extends string> = PgBooleanBuilder<{\n\tname: TName;\n\tdataType: 'boolean';\n\tcolumnType: 'PgBoolean';\n\tdata: boolean;\n\tdriverParam: boolean;\n\tenumValues: undefined;\n}>;\n\nexport class PgBooleanBuilder<T extends ColumnBuilderBaseConfig<'boolean', 'PgBoolean'>> extends PgColumnBuilder<T> {\n\tstatic override readonly [entityKind]: string = 'PgBooleanBuilder';\n\n\tconstructor(name: T['name']) {\n\t\tsuper(name, 'boolean', 'PgBoolean');\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyPgTable<{ name: TTableName }>,\n\t): PgBoolean<MakeColumnConfig<T, TTableName>> {\n\t\treturn new PgBoolean<MakeColumnConfig<T, TTableName>>(table, this.config as ColumnBuilderRuntimeConfig<any, any>);\n\t}\n}\n\nexport class PgBoolean<T extends ColumnBaseConfig<'boolean', 'PgBoolean'>> extends PgColumn<T> {\n\tstatic override readonly [entityKind]: string = 'PgBoolean';\n\n\tgetSQLType(): string {\n\t\treturn 'boolean';\n\t}\n}\n\nexport function boolean(): PgBooleanBuilderInitial<''>;\nexport function boolean<TName extends string>(name: TName): PgBooleanBuilderInitial<TName>;\nexport function boolean(name?: string) {\n\treturn new PgBooleanBuilder(name ?? '');\n}\n", "import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyPgTable } from '~/pg-core/table.ts';\nimport { getColumnNameAndConfig, type Writable } from '~/utils.ts';\nimport { PgColumn, PgColumnBuilder } from './common.ts';\n\nexport type PgCharBuilderInitial<\n\tTName extends string,\n\tTEnum extends [string, ...string[]],\n\tTLength extends number | undefined,\n> = PgCharBuilder<{\n\tname: TName;\n\tdataType: 'string';\n\tcolumnType: 'PgChar';\n\tdata: TEnum[number];\n\tenumValues: TEnum;\n\tdriverParam: string;\n\tlength: TLength;\n}>;\n\nexport class PgCharBuilder<T extends ColumnBuilderBaseConfig<'string', 'PgChar'> & { length?: number | undefined }>\n\textends PgColumnBuilder<\n\t\tT,\n\t\t{ length: T['length']; enumValues: T['enumValues'] },\n\t\t{ length: T['length'] }\n\t>\n{\n\tstatic override readonly [entityKind]: string = 'PgCharBuilder';\n\n\tconstructor(name: T['name'], config: PgCharConfig<T['enumValues'], T['length']>) {\n\t\tsuper(name, 'string', 'PgChar');\n\t\tthis.config.length = config.length;\n\t\tthis.config.enumValues = config.enum;\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyPgTable<{ name: TTableName }>,\n\t): PgChar<MakeColumnConfig<T, TTableName> & { length: T['length'] }> {\n\t\treturn new PgChar<MakeColumnConfig<T, TTableName> & { length: T['length'] }>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n}\n\nexport class PgChar<T extends ColumnBaseConfig<'string', 'PgChar'> & { length?: number | undefined }>\n\textends PgColumn<T, { length: T['length']; enumValues: T['enumValues'] }, { length: T['length'] }>\n{\n\tstatic override readonly [entityKind]: string = 'PgChar';\n\n\treadonly length = this.config.length;\n\toverride readonly enumValues = this.config.enumValues;\n\n\tgetSQLType(): string {\n\t\treturn this.length === undefined ? `char` : `char(${this.length})`;\n\t}\n}\n\nexport interface PgCharConfig<\n\tTEnum extends readonly string[] | string[] | undefined = readonly string[] | string[] | undefined,\n\tTLength extends number | undefined = number | undefined,\n> {\n\tenum?: TEnum;\n\tlength?: TLength;\n}\n\nexport function char(): PgCharBuilderInitial<'', [string, ...string[]], undefined>;\nexport function char<U extends string, T extends Readonly<[U, ...U[]]>, L extends number | undefined>(\n\tconfig?: PgCharConfig<T | Writable<T>, L>,\n): PgCharBuilderInitial<'', Writable<T>, L>;\nexport function char<\n\tTName extends string,\n\tU extends string,\n\tT extends Readonly<[U, ...U[]]>,\n\tL extends number | undefined,\n>(\n\tname: TName,\n\tconfig?: PgCharConfig<T | Writable<T>, L>,\n): PgCharBuilderInitial<TName, Writable<T>, L>;\nexport function char(a?: string | PgCharConfig, b: PgCharConfig = {}): any {\n\tconst { name, config } = getColumnNameAndConfig<PgCharConfig>(a, b);\n\treturn new PgCharBuilder(name, config as any);\n}\n", "import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyPgTable } from '../table.ts';\nimport { PgColumn, PgColumnBuilder } from './common.ts';\n\nexport type PgCidrBuilderInitial<TName extends string> = PgCidrBuilder<{\n\tname: TName;\n\tdataType: 'string';\n\tcolumnType: 'PgCidr';\n\tdata: string;\n\tdriverParam: string;\n\tenumValues: undefined;\n}>;\n\nexport class PgCidrBuilder<T extends ColumnBuilderBaseConfig<'string', 'PgCidr'>> extends PgColumnBuilder<T> {\n\tstatic override readonly [entityKind]: string = 'PgCidrBuilder';\n\n\tconstructor(name: T['name']) {\n\t\tsuper(name, 'string', 'PgCidr');\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyPgTable<{ name: TTableName }>,\n\t): PgCidr<MakeColumnConfig<T, TTableName>> {\n\t\treturn new PgCidr<MakeColumnConfig<T, TTableName>>(table, this.config as ColumnBuilderRuntimeConfig<any, any>);\n\t}\n}\n\nexport class PgCidr<T extends ColumnBaseConfig<'string', 'PgCidr'>> extends PgColumn<T> {\n\tstatic override readonly [entityKind]: string = 'PgCidr';\n\n\tgetSQLType(): string {\n\t\treturn 'cidr';\n\t}\n}\n\nexport function cidr(): PgCidrBuilderInitial<''>;\nexport function cidr<TName extends string>(name: TName): PgCidrBuilderInitial<TName>;\nexport function cidr(name?: string) {\n\treturn new PgCidrBuilder(name ?? '');\n}\n", "import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyPgTable } from '~/pg-core/table.ts';\nimport type { SQL } from '~/sql/sql.ts';\nimport { type Equal, getColumnNameAndConfig } from '~/utils.ts';\nimport { PgColumn, PgColumnBuilder } from './common.ts';\n\nexport type ConvertCustomConfig<TName extends string, T extends Partial<CustomTypeValues>> =\n\t& {\n\t\tname: TName;\n\t\tdataType: 'custom';\n\t\tcolumnType: 'PgCustomColumn';\n\t\tdata: T['data'];\n\t\tdriverParam: T['driverData'];\n\t\tenumValues: undefined;\n\t}\n\t& (T['notNull'] extends true ? { notNull: true } : {})\n\t& (T['default'] extends true ? { hasDefault: true } : {});\n\nexport interface PgCustomColumnInnerConfig {\n\tcustomTypeValues: CustomTypeValues;\n}\n\nexport class PgCustomColumnBuilder<T extends ColumnBuilderBaseConfig<'custom', 'PgCustomColumn'>>\n\textends PgColumnBuilder<\n\t\tT,\n\t\t{\n\t\t\tfieldConfig: CustomTypeValues['config'];\n\t\t\tcustomTypeParams: CustomTypeParams<any>;\n\t\t},\n\t\t{\n\t\t\tpgColumnBuilderBrand: 'PgCustomColumnBuilderBrand';\n\t\t}\n\t>\n{\n\tstatic override readonly [entityKind]: string = 'PgCustomColumnBuilder';\n\n\tconstructor(\n\t\tname: T['name'],\n\t\tfieldConfig: CustomTypeValues['config'],\n\t\tcustomTypeParams: CustomTypeParams<any>,\n\t) {\n\t\tsuper(name, 'custom', 'PgCustomColumn');\n\t\tthis.config.fieldConfig = fieldConfig;\n\t\tthis.config.customTypeParams = customTypeParams;\n\t}\n\n\t/** @internal */\n\tbuild<TTableName extends string>(\n\t\ttable: AnyPgTable<{ name: TTableName }>,\n\t): PgCustomColumn<MakeColumnConfig<T, TTableName>> {\n\t\treturn new PgCustomColumn<MakeColumnConfig<T, TTableName>>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n}\n\nexport class PgCustomColumn<T extends ColumnBaseConfig<'custom', 'PgCustomColumn'>> extends PgColumn<T> {\n\tstatic override readonly [entityKind]: string = 'PgCustomColumn';\n\n\tprivate sqlName: string;\n\tprivate mapTo?: (value: T['data']) => T['driverParam'];\n\tprivate mapFrom?: (value: T['driverParam']) => T['data'];\n\n\tconstructor(\n\t\ttable: AnyPgTable<{ name: T['tableName'] }>,\n\t\tconfig: PgCustomColumnBuilder<T>['config'],\n\t) {\n\t\tsuper(table, config);\n\t\tthis.sqlName = config.customTypeParams.dataType(config.fieldConfig);\n\t\tthis.mapTo = config.customTypeParams.toDriver;\n\t\tthis.mapFrom = config.customTypeParams.fromDriver;\n\t}\n\n\tgetSQLType(): string {\n\t\treturn this.sqlName;\n\t}\n\n\toverride mapFromDriverValue(value: T['driverParam']): T['data'] {\n\t\treturn typeof this.mapFrom === 'function' ? this.mapFrom(value) : value as T['data'];\n\t}\n\n\toverride mapToDriverValue(value: T['data']): T['driverParam'] {\n\t\treturn typeof this.mapTo === 'function' ? this.mapTo(value) : value as T['data'];\n\t}\n}\n\nexport type CustomTypeValues = {\n\t/**\n\t * Required type for custom column, that will infer proper type model\n\t *\n\t * Examples:\n\t *\n\t * If you want your column to be `string` type after selecting/or on inserting - use `data: string`. Like `text`, `varchar`\n\t *\n\t * If you want your column to be `number` type after selecting/or on inserting - use `data: number`. Like `integer`\n\t */\n\tdata: unknown;\n\n\t/**\n\t * Type helper, that represents what type database driver is accepting for specific database data type\n\t */\n\tdriverData?: unknown;\n\n\t/**\n\t * What config type should be used for {@link CustomTypeParams} `dataType` generation\n\t */\n\tconfig?: Record<string, any>;\n\n\t/**\n\t * Whether the config argument should be required or not\n\t * @default false\n\t */\n\tconfigRequired?: boolean;\n\n\t/**\n\t * If your custom data type should be notNull by default you can use `notNull: true`\n\t *\n\t * @example\n\t * const customSerial = customType<{ data: number, notNull: true, default: true }>({\n\t * \t  dataType() {\n\t * \t    return 'serial';\n\t *    },\n\t * });\n\t */\n\tnotNull?: boolean;\n\n\t/**\n\t * If your custom data type has default you can use `default: true`\n\t *\n\t * @example\n\t * const customSerial = customType<{ data: number, notNull: true, default: true }>({\n\t * \t  dataType() {\n\t * \t    return 'serial';\n\t *    },\n\t * });\n\t */\n\tdefault?: boolean;\n};\n\nexport interface CustomTypeParams<T extends CustomTypeValues> {\n\t/**\n\t * Database data type string representation, that is used for migrations\n\t * @example\n\t * ```\n\t * `jsonb`, `text`\n\t * ```\n\t *\n\t * If database data type needs additional params you can use them from `config` param\n\t * @example\n\t * ```\n\t * `varchar(256)`, `numeric(2,3)`\n\t * ```\n\t *\n\t * To make `config` be of specific type please use config generic in {@link CustomTypeValues}\n\t *\n\t * @example\n\t * Usage example\n\t * ```\n\t *   dataType() {\n\t *     return 'boolean';\n\t *   },\n\t * ```\n\t * Or\n\t * ```\n\t *   dataType(config) {\n\t * \t   return typeof config.length !== 'undefined' ? `varchar(${config.length})` : `varchar`;\n\t * \t }\n\t * ```\n\t */\n\tdataType: (config: T['config'] | (Equal<T['configRequired'], true> extends true ? never : undefined)) => string;\n\n\t/**\n\t * Optional mapping function, between user input and driver\n\t * @example\n\t * For example, when using jsonb we need to map JS/TS object to string before writing to database\n\t * ```\n\t * toDriver(value: TData): string {\n\t * \t return JSON.stringify(value);\n\t * }\n\t * ```\n\t */\n\ttoDriver?: (value: T['data']) => T['driverData'] | SQL;\n\n\t/**\n\t * Optional mapping function, that is responsible for data mapping from database to JS/TS code\n\t * @example\n\t * For example, when using timestamp we need to map string Date representation to JS Date\n\t * ```\n\t * fromDriver(value: string): Date {\n\t * \treturn new Date(value);\n\t * },\n\t * ```\n\t */\n\tfromDriver?: (value: T['driverData']) => T['data'];\n}\n\n/**\n * Custom pg database data type generator\n */\nexport function customType<T extends CustomTypeValues = CustomTypeValues>(\n\tcustomTypeParams: CustomTypeParams<T>,\n): Equal<T['configRequired'], true> extends true ? {\n\t\t<TConfig extends Record<string, any> & T['config']>(\n\t\t\tfieldConfig: TConfig,\n\t\t): PgCustomColumnBuilder<ConvertCustomConfig<'', T>>;\n\t\t<TName extends string>(\n\t\t\tdbName: TName,\n\t\t\tfieldConfig: T['config'],\n\t\t): PgCustomColumnBuilder<ConvertCustomConfig<TName, T>>;\n\t}\n\t: {\n\t\t(): PgCustomColumnBuilder<ConvertCustomConfig<'', T>>;\n\t\t<TConfig extends Record<string, any> & T['config']>(\n\t\t\tfieldConfig?: TConfig,\n\t\t): PgCustomColumnBuilder<ConvertCustomConfig<'', T>>;\n\t\t<TName extends string>(\n\t\t\tdbName: TName,\n\t\t\tfieldConfig?: T['config'],\n\t\t): PgCustomColumnBuilder<ConvertCustomConfig<TName, T>>;\n\t}\n{\n\treturn <TName extends string>(\n\t\ta?: TName | T['config'],\n\t\tb?: T['config'],\n\t): PgCustomColumnBuilder<ConvertCustomConfig<TName, T>> => {\n\t\tconst { name, config } = getColumnNameAndConfig<T['config']>(a, b);\n\t\treturn new PgCustomColumnBuilder(name as ConvertCustomConfig<TName, T>['name'], config, customTypeParams);\n\t};\n}\n", "import type { ColumnBuilderBaseConfig, ColumnDataType } from '~/column-builder.ts';\nimport { entityKind } from '~/entity.ts';\nimport { sql } from '~/sql/sql.ts';\nimport { PgColumnBuilder } from './common.ts';\n\nexport abstract class PgDateColumnBaseBuilder<\n\tT extends ColumnBuilderBaseConfig<ColumnDataType, string>,\n\tTRuntimeConfig extends object = object,\n> extends PgColumnBuilder<T, TRuntimeConfig> {\n\tstatic override readonly [entityKind]: string = 'PgDateColumnBaseBuilder';\n\n\tdefaultNow() {\n\t\treturn this.default(sql`now()`);\n\t}\n}\n", "import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyPgTable } from '~/pg-core/table.ts';\nimport { type Equal, getColumnNameAndConfig } from '~/utils.ts';\nimport { PgColumn } from './common.ts';\nimport { PgDateColumnBaseBuilder } from './date.common.ts';\n\nexport type PgDateBuilderInitial<TName extends string> = PgDateBuilder<{\n\tname: TName;\n\tdataType: 'date';\n\tcolumnType: 'PgDate';\n\tdata: Date;\n\tdriverParam: string;\n\tenumValues: undefined;\n}>;\n\nexport class PgDateBuilder<T extends ColumnBuilderBaseConfig<'date', 'PgDate'>> extends PgDateColumnBaseBuilder<T> {\n\tstatic override readonly [entityKind]: string = 'PgDateBuilder';\n\n\tconstructor(name: T['name']) {\n\t\tsuper(name, 'date', 'PgDate');\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyPgTable<{ name: TTableName }>,\n\t): PgDate<MakeColumnConfig<T, TTableName>> {\n\t\treturn new PgDate<MakeColumnConfig<T, TTableName>>(table, this.config as ColumnBuilderRuntimeConfig<any, any>);\n\t}\n}\n\nexport class PgDate<T extends ColumnBaseConfig<'date', 'PgDate'>> extends PgColumn<T> {\n\tstatic override readonly [entityKind]: string = 'PgDate';\n\n\tgetSQLType(): string {\n\t\treturn 'date';\n\t}\n\n\toverride mapFromDriverValue(value: string): Date {\n\t\treturn new Date(value);\n\t}\n\n\toverride mapToDriverValue(value: Date): string {\n\t\treturn value.toISOString();\n\t}\n}\n\nexport type PgDateStringBuilderInitial<TName extends string> = PgDateStringBuilder<{\n\tname: TName;\n\tdataType: 'string';\n\tcolumnType: 'PgDateString';\n\tdata: string;\n\tdriverParam: string;\n\tenumValues: undefined;\n}>;\n\nexport class PgDateStringBuilder<T extends ColumnBuilderBaseConfig<'string', 'PgDateString'>>\n\textends PgDateColumnBaseBuilder<T>\n{\n\tstatic override readonly [entityKind]: string = 'PgDateStringBuilder';\n\n\tconstructor(name: T['name']) {\n\t\tsuper(name, 'string', 'PgDateString');\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyPgTable<{ name: TTableName }>,\n\t): PgDateString<MakeColumnConfig<T, TTableName>> {\n\t\treturn new PgDateString<MakeColumnConfig<T, TTableName>>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n}\n\nexport class PgDateString<T extends ColumnBaseConfig<'string', 'PgDateString'>> extends PgColumn<T> {\n\tstatic override readonly [entityKind]: string = 'PgDateString';\n\n\tgetSQLType(): string {\n\t\treturn 'date';\n\t}\n}\n\nexport interface PgDateConfig<T extends 'date' | 'string' = 'date' | 'string'> {\n\tmode: T;\n}\n\nexport function date(): PgDateStringBuilderInitial<''>;\nexport function date<TMode extends PgDateConfig['mode'] & {}>(\n\tconfig?: PgDateConfig<TMode>,\n): Equal<TMode, 'date'> extends true ? PgDateBuilderInitial<''> : PgDateStringBuilderInitial<''>;\nexport function date<TName extends string, TMode extends PgDateConfig['mode'] & {}>(\n\tname: TName,\n\tconfig?: PgDateConfig<TMode>,\n): Equal<TMode, 'date'> extends true ? PgDateBuilderInitial<TName> : PgDateStringBuilderInitial<TName>;\nexport function date(a?: string | PgDateConfig, b?: PgDateConfig) {\n\tconst { name, config } = getColumnNameAndConfig<PgDateConfig>(a, b);\n\tif (config?.mode === 'date') {\n\t\treturn new PgDateBuilder(name);\n\t}\n\treturn new PgDateStringBuilder(name);\n}\n", "import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyPgTable } from '~/pg-core/table.ts';\nimport { PgColumn, PgColumnBuilder } from './common.ts';\n\nexport type PgDoublePrecisionBuilderInitial<TName extends string> = PgDoublePrecisionBuilder<{\n\tname: TName;\n\tdataType: 'number';\n\tcolumnType: 'PgDoublePrecision';\n\tdata: number;\n\tdriverParam: string | number;\n\tenumValues: undefined;\n}>;\n\nexport class PgDoublePrecisionBuilder<T extends ColumnBuilderBaseConfig<'number', 'PgDoublePrecision'>>\n\textends PgColumnBuilder<T>\n{\n\tstatic override readonly [entityKind]: string = 'PgDoublePrecisionBuilder';\n\n\tconstructor(name: T['name']) {\n\t\tsuper(name, 'number', 'PgDoublePrecision');\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyPgTable<{ name: TTableName }>,\n\t): PgDoublePrecision<MakeColumnConfig<T, TTableName>> {\n\t\treturn new PgDoublePrecision<MakeColumnConfig<T, TTableName>>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n}\n\nexport class PgDoublePrecision<T extends ColumnBaseConfig<'number', 'PgDoublePrecision'>> extends PgColumn<T> {\n\tstatic override readonly [entityKind]: string = 'PgDoublePrecision';\n\n\tgetSQLType(): string {\n\t\treturn 'double precision';\n\t}\n\n\toverride mapFromDriverValue(value: string | number): number {\n\t\tif (typeof value === 'string') {\n\t\t\treturn Number.parseFloat(value);\n\t\t}\n\t\treturn value;\n\t}\n}\n\nexport function doublePrecision(): PgDoublePrecisionBuilderInitial<''>;\nexport function doublePrecision<TName extends string>(name: TName): PgDoublePrecisionBuilderInitial<TName>;\nexport function doublePrecision(name?: string) {\n\treturn new PgDoublePrecisionBuilder(name ?? '');\n}\n", "import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyPgTable } from '../table.ts';\nimport { PgColumn, PgColumnBuilder } from './common.ts';\n\nexport type PgInetBuilderInitial<TName extends string> = PgInetBuilder<{\n\tname: TName;\n\tdataType: 'string';\n\tcolumnType: 'PgInet';\n\tdata: string;\n\tdriverParam: string;\n\tenumValues: undefined;\n}>;\n\nexport class PgInetBuilder<T extends ColumnBuilderBaseConfig<'string', 'PgInet'>> extends PgColumnBuilder<T> {\n\tstatic override readonly [entityKind]: string = 'PgInetBuilder';\n\n\tconstructor(name: T['name']) {\n\t\tsuper(name, 'string', 'PgInet');\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyPgTable<{ name: TTableName }>,\n\t): PgInet<MakeColumnConfig<T, TTableName>> {\n\t\treturn new PgInet<MakeColumnConfig<T, TTableName>>(table, this.config as ColumnBuilderRuntimeConfig<any, any>);\n\t}\n}\n\nexport class PgInet<T extends ColumnBaseConfig<'string', 'PgInet'>> extends PgColumn<T> {\n\tstatic override readonly [entityKind]: string = 'PgInet';\n\n\tgetSQLType(): string {\n\t\treturn 'inet';\n\t}\n}\n\nexport function inet(): PgInetBuilderInitial<''>;\nexport function inet<TName extends string>(name: TName): PgInetBuilderInitial<TName>;\nexport function inet(name?: string) {\n\treturn new PgInetBuilder(name ?? '');\n}\n", "import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyPgTable } from '../table.ts';\nimport { PgColumn } from './common.ts';\nimport { PgIntColumnBaseBuilder } from './int.common.ts';\n\nexport type PgIntegerBuilderInitial<TName extends string> = PgIntegerBuilder<{\n\tname: TName;\n\tdataType: 'number';\n\tcolumnType: 'PgInteger';\n\tdata: number;\n\tdriverParam: number | string;\n\tenumValues: undefined;\n}>;\n\nexport class PgIntegerBuilder<T extends ColumnBuilderBaseConfig<'number', 'PgInteger'>>\n\textends PgIntColumnBaseBuilder<T>\n{\n\tstatic override readonly [entityKind]: string = 'PgIntegerBuilder';\n\n\tconstructor(name: T['name']) {\n\t\tsuper(name, 'number', 'PgInteger');\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyPgTable<{ name: TTableName }>,\n\t): PgInteger<MakeColumnConfig<T, TTableName>> {\n\t\treturn new PgInteger<MakeColumnConfig<T, TTableName>>(table, this.config as ColumnBuilderRuntimeConfig<any, any>);\n\t}\n}\n\nexport class PgInteger<T extends ColumnBaseConfig<'number', 'PgInteger'>> extends PgColumn<T> {\n\tstatic override readonly [entityKind]: string = 'PgInteger';\n\n\tgetSQLType(): string {\n\t\treturn 'integer';\n\t}\n\n\toverride mapFromDriverValue(value: number | string): number {\n\t\tif (typeof value === 'string') {\n\t\t\treturn Number.parseInt(value);\n\t\t}\n\t\treturn value;\n\t}\n}\n\nexport function integer(): PgIntegerBuilderInitial<''>;\nexport function integer<TName extends string>(name: TName): PgIntegerBuilderInitial<TName>;\nexport function integer(name?: string) {\n\treturn new PgIntegerBuilder(name ?? '');\n}\n", "import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyPgTable } from '~/pg-core/table.ts';\nimport { getColumnNameAndConfig } from '~/utils.ts';\nimport { PgColumn, PgColumnBuilder } from './common.ts';\nimport type { Precision } from './timestamp.ts';\n\nexport type PgIntervalBuilderInitial<TName extends string> = PgIntervalBuilder<{\n\tname: TName;\n\tdataType: 'string';\n\tcolumnType: 'PgInterval';\n\tdata: string;\n\tdriverParam: string;\n\tenumValues: undefined;\n}>;\n\nexport class PgIntervalBuilder<T extends ColumnBuilderBaseConfig<'string', 'PgInterval'>>\n\textends PgColumnBuilder<T, { intervalConfig: IntervalConfig }>\n{\n\tstatic override readonly [entityKind]: string = 'PgIntervalBuilder';\n\n\tconstructor(\n\t\tname: T['name'],\n\t\tintervalConfig: IntervalConfig,\n\t) {\n\t\tsuper(name, 'string', 'PgInterval');\n\t\tthis.config.intervalConfig = intervalConfig;\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyPgTable<{ name: TTableName }>,\n\t): PgInterval<MakeColumnConfig<T, TTableName>> {\n\t\treturn new PgInterval<MakeColumnConfig<T, TTableName>>(table, this.config as ColumnBuilderRuntimeConfig<any, any>);\n\t}\n}\n\nexport class PgInterval<T extends ColumnBaseConfig<'string', 'PgInterval'>>\n\textends PgColumn<T, { intervalConfig: IntervalConfig }>\n{\n\tstatic override readonly [entityKind]: string = 'PgInterval';\n\n\treadonly fields: IntervalConfig['fields'] = this.config.intervalConfig.fields;\n\treadonly precision: IntervalConfig['precision'] = this.config.intervalConfig.precision;\n\n\tgetSQLType(): string {\n\t\tconst fields = this.fields ? ` ${this.fields}` : '';\n\t\tconst precision = this.precision ? `(${this.precision})` : '';\n\t\treturn `interval${fields}${precision}`;\n\t}\n}\n\nexport interface IntervalConfig {\n\tfields?:\n\t\t| 'year'\n\t\t| 'month'\n\t\t| 'day'\n\t\t| 'hour'\n\t\t| 'minute'\n\t\t| 'second'\n\t\t| 'year to month'\n\t\t| 'day to hour'\n\t\t| 'day to minute'\n\t\t| 'day to second'\n\t\t| 'hour to minute'\n\t\t| 'hour to second'\n\t\t| 'minute to second';\n\tprecision?: Precision;\n}\n\nexport function interval(): PgIntervalBuilderInitial<''>;\nexport function interval(\n\tconfig?: IntervalConfig,\n): PgIntervalBuilderInitial<''>;\nexport function interval<TName extends string>(\n\tname: TName,\n\tconfig?: IntervalConfig,\n): PgIntervalBuilderInitial<TName>;\nexport function interval(a?: string | IntervalConfig, b: IntervalConfig = {}) {\n\tconst { name, config } = getColumnNameAndConfig<IntervalConfig>(a, b);\n\treturn new PgIntervalBuilder(name, config);\n}\n", "import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyPgTable } from '~/pg-core/table.ts';\nimport { PgColumn, PgColumnBuilder } from './common.ts';\n\nexport type PgJsonBuilderInitial<TName extends string> = PgJsonBuilder<{\n\tname: TName;\n\tdataType: 'json';\n\tcolumnType: 'PgJson';\n\tdata: unknown;\n\tdriverParam: unknown;\n\tenumValues: undefined;\n}>;\n\nexport class PgJsonBuilder<T extends ColumnBuilderBaseConfig<'json', 'PgJson'>> extends PgColumnBuilder<\n\tT\n> {\n\tstatic override readonly [entityKind]: string = 'PgJsonBuilder';\n\n\tconstructor(name: T['name']) {\n\t\tsuper(name, 'json', 'PgJson');\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyPgTable<{ name: TTableName }>,\n\t): PgJson<MakeColumnConfig<T, TTableName>> {\n\t\treturn new PgJson<MakeColumnConfig<T, TTableName>>(table, this.config as ColumnBuilderRuntimeConfig<any, any>);\n\t}\n}\n\nexport class PgJson<T extends ColumnBaseConfig<'json', 'PgJson'>> extends PgColumn<T> {\n\tstatic override readonly [entityKind]: string = 'PgJson';\n\n\tconstructor(table: AnyPgTable<{ name: T['tableName'] }>, config: PgJsonBuilder<T>['config']) {\n\t\tsuper(table, config);\n\t}\n\n\tgetSQLType(): string {\n\t\treturn 'json';\n\t}\n\n\toverride mapToDriverValue(value: T['data']): string {\n\t\treturn JSON.stringify(value);\n\t}\n\n\toverride mapFromDriverValue(value: T['data'] | string): T['data'] {\n\t\tif (typeof value === 'string') {\n\t\t\ttry {\n\t\t\t\treturn JSON.parse(value);\n\t\t\t} catch {\n\t\t\t\treturn value as T['data'];\n\t\t\t}\n\t\t}\n\t\treturn value;\n\t}\n}\n\nexport function json(): PgJsonBuilderInitial<''>;\nexport function json<TName extends string>(name: TName): PgJsonBuilderInitial<TName>;\nexport function json(name?: string) {\n\treturn new PgJsonBuilder(name ?? '');\n}\n", "import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyPgTable } from '~/pg-core/table.ts';\nimport { PgColumn, PgColumnBuilder } from './common.ts';\n\nexport type PgJsonbBuilderInitial<TName extends string> = PgJsonbBuilder<{\n\tname: TName;\n\tdataType: 'json';\n\tcolumnType: 'PgJsonb';\n\tdata: unknown;\n\tdriverParam: unknown;\n\tenumValues: undefined;\n}>;\n\nexport class PgJsonbBuilder<T extends ColumnBuilderBaseConfig<'json', 'PgJsonb'>> extends PgColumnBuilder<T> {\n\tstatic override readonly [entityKind]: string = 'PgJsonbBuilder';\n\n\tconstructor(name: T['name']) {\n\t\tsuper(name, 'json', 'PgJsonb');\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyPgTable<{ name: TTableName }>,\n\t): PgJsonb<MakeColumnConfig<T, TTableName>> {\n\t\treturn new PgJsonb<MakeColumnConfig<T, TTableName>>(table, this.config as ColumnBuilderRuntimeConfig<any, any>);\n\t}\n}\n\nexport class PgJsonb<T extends ColumnBaseConfig<'json', 'PgJsonb'>> extends PgColumn<T> {\n\tstatic override readonly [entityKind]: string = 'PgJsonb';\n\n\tconstructor(table: AnyPgTable<{ name: T['tableName'] }>, config: PgJsonbBuilder<T>['config']) {\n\t\tsuper(table, config);\n\t}\n\n\tgetSQLType(): string {\n\t\treturn 'jsonb';\n\t}\n\n\toverride mapToDriverValue(value: T['data']): string {\n\t\treturn JSON.stringify(value);\n\t}\n\n\toverride mapFromDriverValue(value: T['data'] | string): T['data'] {\n\t\tif (typeof value === 'string') {\n\t\t\ttry {\n\t\t\t\treturn JSON.parse(value);\n\t\t\t} catch {\n\t\t\t\treturn value as T['data'];\n\t\t\t}\n\t\t}\n\t\treturn value;\n\t}\n}\n\nexport function jsonb(): PgJsonbBuilderInitial<''>;\nexport function jsonb<TName extends string>(name: TName): PgJsonbBuilderInitial<TName>;\nexport function jsonb(name?: string) {\n\treturn new PgJsonbBuilder(name ?? '');\n}\n", "import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyPgTable } from '~/pg-core/table.ts';\n\nimport { type Equal, getColumnNameAndConfig } from '~/utils.ts';\nimport { PgColumn, PgColumnBuilder } from './common.ts';\n\nexport type PgLineBuilderInitial<TName extends string> = PgLineBuilder<{\n\tname: TName;\n\tdataType: 'array';\n\tcolumnType: 'PgLine';\n\tdata: [number, number, number];\n\tdriverParam: number | string;\n\tenumValues: undefined;\n}>;\n\nexport class PgLineBuilder<T extends ColumnBuilderBaseConfig<'array', 'PgLine'>> extends PgColumnBuilder<T> {\n\tstatic override readonly [entityKind]: string = 'PgLineBuilder';\n\n\tconstructor(name: T['name']) {\n\t\tsuper(name, 'array', 'PgLine');\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyPgTable<{ name: TTableName }>,\n\t): PgLineTuple<MakeColumnConfig<T, TTableName>> {\n\t\treturn new PgLineTuple<MakeColumnConfig<T, TTableName>>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n}\n\nexport class PgLineTuple<T extends ColumnBaseConfig<'array', 'PgLine'>> extends PgColumn<T> {\n\tstatic override readonly [entityKind]: string = 'PgLine';\n\n\tgetSQLType(): string {\n\t\treturn 'line';\n\t}\n\n\toverride mapFromDriverValue(value: string): [number, number, number] {\n\t\tconst [a, b, c] = value.slice(1, -1).split(',');\n\t\treturn [Number.parseFloat(a!), Number.parseFloat(b!), Number.parseFloat(c!)];\n\t}\n\n\toverride mapToDriverValue(value: [number, number, number]): string {\n\t\treturn `{${value[0]},${value[1]},${value[2]}}`;\n\t}\n}\n\nexport type PgLineABCBuilderInitial<TName extends string> = PgLineABCBuilder<{\n\tname: TName;\n\tdataType: 'json';\n\tcolumnType: 'PgLineABC';\n\tdata: { a: number; b: number; c: number };\n\tdriverParam: string;\n\tenumValues: undefined;\n}>;\n\nexport class PgLineABCBuilder<T extends ColumnBuilderBaseConfig<'json', 'PgLineABC'>> extends PgColumnBuilder<T> {\n\tstatic override readonly [entityKind]: string = 'PgLineABCBuilder';\n\n\tconstructor(name: T['name']) {\n\t\tsuper(name, 'json', 'PgLineABC');\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyPgTable<{ name: TTableName }>,\n\t): PgLineABC<MakeColumnConfig<T, TTableName>> {\n\t\treturn new PgLineABC<MakeColumnConfig<T, TTableName>>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n}\n\nexport class PgLineABC<T extends ColumnBaseConfig<'json', 'PgLineABC'>> extends PgColumn<T> {\n\tstatic override readonly [entityKind]: string = 'PgLineABC';\n\n\tgetSQLType(): string {\n\t\treturn 'line';\n\t}\n\n\toverride mapFromDriverValue(value: string): { a: number; b: number; c: number } {\n\t\tconst [a, b, c] = value.slice(1, -1).split(',');\n\t\treturn { a: Number.parseFloat(a!), b: Number.parseFloat(b!), c: Number.parseFloat(c!) };\n\t}\n\n\toverride mapToDriverValue(value: { a: number; b: number; c: number }): string {\n\t\treturn `{${value.a},${value.b},${value.c}}`;\n\t}\n}\n\nexport interface PgLineTypeConfig<T extends 'tuple' | 'abc' = 'tuple' | 'abc'> {\n\tmode?: T;\n}\n\nexport function line(): PgLineBuilderInitial<''>;\nexport function line<TMode extends PgLineTypeConfig['mode'] & {}>(\n\tconfig?: PgLineTypeConfig<TMode>,\n): Equal<TMode, 'abc'> extends true ? PgLineABCBuilderInitial<''>\n\t: PgLineBuilderInitial<''>;\nexport function line<TName extends string, TMode extends PgLineTypeConfig['mode'] & {}>(\n\tname: TName,\n\tconfig?: PgLineTypeConfig<TMode>,\n): Equal<TMode, 'abc'> extends true ? PgLineABCBuilderInitial<TName>\n\t: PgLineBuilderInitial<TName>;\nexport function line(a?: string | PgLineTypeConfig, b?: PgLineTypeConfig) {\n\tconst { name, config } = getColumnNameAndConfig<PgLineTypeConfig>(a, b);\n\tif (!config?.mode || config.mode === 'tuple') {\n\t\treturn new PgLineBuilder(name);\n\t}\n\treturn new PgLineABCBuilder(name);\n}\n", "import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyPgTable } from '../table.ts';\nimport { PgColumn, PgColumnBuilder } from './common.ts';\n\nexport type PgMacaddrBuilderInitial<TName extends string> = PgMacaddrBuilder<{\n\tname: TName;\n\tdataType: 'string';\n\tcolumnType: 'PgMacaddr';\n\tdata: string;\n\tdriverParam: string;\n\tenumValues: undefined;\n}>;\n\nexport class PgMacaddrBuilder<T extends ColumnBuilderBaseConfig<'string', 'PgMacaddr'>> extends PgColumnBuilder<T> {\n\tstatic override readonly [entityKind]: string = 'PgMacaddrBuilder';\n\n\tconstructor(name: T['name']) {\n\t\tsuper(name, 'string', 'PgMacaddr');\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyPgTable<{ name: TTableName }>,\n\t): PgMacaddr<MakeColumnConfig<T, TTableName>> {\n\t\treturn new PgMacaddr<MakeColumnConfig<T, TTableName>>(table, this.config as ColumnBuilderRuntimeConfig<any, any>);\n\t}\n}\n\nexport class PgMacaddr<T extends ColumnBaseConfig<'string', 'PgMacaddr'>> extends PgColumn<T> {\n\tstatic override readonly [entityKind]: string = 'PgMacaddr';\n\n\tgetSQLType(): string {\n\t\treturn 'macaddr';\n\t}\n}\n\nexport function macaddr(): PgMacaddrBuilderInitial<''>;\nexport function macaddr<TName extends string>(name: TName): PgMacaddrBuilderInitial<TName>;\nexport function macaddr(name?: string) {\n\treturn new PgMacaddrBuilder(name ?? '');\n}\n", "import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyPgTable } from '../table.ts';\nimport { PgColumn, PgColumnBuilder } from './common.ts';\n\nexport type PgMacaddr8BuilderInitial<TName extends string> = PgMacaddr8Builder<{\n\tname: TName;\n\tdataType: 'string';\n\tcolumnType: 'PgMacaddr8';\n\tdata: string;\n\tdriverParam: string;\n\tenumValues: undefined;\n}>;\n\nexport class PgMacaddr8Builder<T extends ColumnBuilderBaseConfig<'string', 'PgMacaddr8'>> extends PgColumnBuilder<T> {\n\tstatic override readonly [entityKind]: string = 'PgMacaddr8Builder';\n\n\tconstructor(name: T['name']) {\n\t\tsuper(name, 'string', 'PgMacaddr8');\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyPgTable<{ name: TTableName }>,\n\t): PgMacaddr8<MakeColumnConfig<T, TTableName>> {\n\t\treturn new PgMacaddr8<MakeColumnConfig<T, TTableName>>(table, this.config as ColumnBuilderRuntimeConfig<any, any>);\n\t}\n}\n\nexport class PgMacaddr8<T extends ColumnBaseConfig<'string', 'PgMacaddr8'>> extends PgColumn<T> {\n\tstatic override readonly [entityKind]: string = 'PgMacaddr8';\n\n\tgetSQLType(): string {\n\t\treturn 'macaddr8';\n\t}\n}\n\nexport function macaddr8(): PgMacaddr8BuilderInitial<''>;\nexport function macaddr8<TName extends string>(name: TName): PgMacaddr8BuilderInitial<TName>;\nexport function macaddr8(name?: string) {\n\treturn new PgMacaddr8Builder(name ?? '');\n}\n", "import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyPgTable } from '~/pg-core/table.ts';\nimport { getColumnNameAndConfig } from '~/utils.ts';\nimport { PgColumn, PgColumnBuilder } from './common.ts';\n\nexport type PgNumericBuilderInitial<TName extends string> = PgNumericBuilder<{\n\tname: TName;\n\tdataType: 'string';\n\tcolumnType: 'PgNumeric';\n\tdata: string;\n\tdriverParam: string;\n\tenumValues: undefined;\n}>;\n\nexport class PgNumericBuilder<T extends ColumnBuilderBaseConfig<'string', 'PgNumeric'>> extends PgColumnBuilder<\n\tT,\n\t{\n\t\tprecision: number | undefined;\n\t\tscale: number | undefined;\n\t}\n> {\n\tstatic override readonly [entityKind]: string = 'PgNumericBuilder';\n\n\tconstructor(name: T['name'], precision?: number, scale?: number) {\n\t\tsuper(name, 'string', 'PgNumeric');\n\t\tthis.config.precision = precision;\n\t\tthis.config.scale = scale;\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyPgTable<{ name: TTableName }>,\n\t): PgNumeric<MakeColumnConfig<T, TTableName>> {\n\t\treturn new PgNumeric<MakeColumnConfig<T, TTableName>>(table, this.config as ColumnBuilderRuntimeConfig<any, any>);\n\t}\n}\n\nexport class PgNumeric<T extends ColumnBaseConfig<'string', 'PgNumeric'>> extends PgColumn<T> {\n\tstatic override readonly [entityKind]: string = 'PgNumeric';\n\n\treadonly precision: number | undefined;\n\treadonly scale: number | undefined;\n\n\tconstructor(table: AnyPgTable<{ name: T['tableName'] }>, config: PgNumericBuilder<T>['config']) {\n\t\tsuper(table, config);\n\t\tthis.precision = config.precision;\n\t\tthis.scale = config.scale;\n\t}\n\n\tgetSQLType(): string {\n\t\tif (this.precision !== undefined && this.scale !== undefined) {\n\t\t\treturn `numeric(${this.precision}, ${this.scale})`;\n\t\t} else if (this.precision === undefined) {\n\t\t\treturn 'numeric';\n\t\t} else {\n\t\t\treturn `numeric(${this.precision})`;\n\t\t}\n\t}\n}\n\nexport type PgNumericConfig =\n\t| { precision: number; scale?: number }\n\t| { precision?: number; scale: number }\n\t| { precision: number; scale: number };\n\nexport function numeric(): PgNumericBuilderInitial<''>;\nexport function numeric(\n\tconfig?: PgNumericConfig,\n): PgNumericBuilderInitial<''>;\nexport function numeric<TName extends string>(\n\tname: TName,\n\tconfig?: PgNumericConfig,\n): PgNumericBuilderInitial<TName>;\nexport function numeric(a?: string | PgNumericConfig, b?: PgNumericConfig) {\n\tconst { name, config } = getColumnNameAndConfig<PgNumericConfig>(a, b);\n\treturn new PgNumericBuilder(name, config?.precision, config?.scale);\n}\n\nexport const decimal = numeric;\n", "import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyPgTable } from '~/pg-core/table.ts';\n\nimport { type Equal, getColumnNameAndConfig } from '~/utils.ts';\nimport { PgColumn, PgColumnBuilder } from './common.ts';\n\nexport type PgPointTupleBuilderInitial<TName extends string> = PgPointTupleBuilder<{\n\tname: TName;\n\tdataType: 'array';\n\tcolumnType: 'PgPointTuple';\n\tdata: [number, number];\n\tdriverParam: number | string;\n\tenumValues: undefined;\n}>;\n\nexport class PgPointTupleBuilder<T extends ColumnBuilderBaseConfig<'array', 'PgPointTuple'>>\n\textends PgColumnBuilder<T>\n{\n\tstatic override readonly [entityKind]: string = 'PgPointTupleBuilder';\n\n\tconstructor(name: string) {\n\t\tsuper(name, 'array', 'PgPointTuple');\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyPgTable<{ name: TTableName }>,\n\t): PgPointTuple<MakeColumnConfig<T, TTableName>> {\n\t\treturn new PgPointTuple<MakeColumnConfig<T, TTableName>>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n}\n\nexport class PgPointTuple<T extends ColumnBaseConfig<'array', 'PgPointTuple'>> extends PgColumn<T> {\n\tstatic override readonly [entityKind]: string = 'PgPointTuple';\n\n\tgetSQLType(): string {\n\t\treturn 'point';\n\t}\n\n\toverride mapFromDriverValue(value: string | { x: number; y: number }): [number, number] {\n\t\tif (typeof value === 'string') {\n\t\t\tconst [x, y] = value.slice(1, -1).split(',');\n\t\t\treturn [Number.parseFloat(x!), Number.parseFloat(y!)];\n\t\t}\n\t\treturn [value.x, value.y];\n\t}\n\n\toverride mapToDriverValue(value: [number, number]): string {\n\t\treturn `(${value[0]},${value[1]})`;\n\t}\n}\n\nexport type PgPointObjectBuilderInitial<TName extends string> = PgPointObjectBuilder<{\n\tname: TName;\n\tdataType: 'json';\n\tcolumnType: 'PgPointObject';\n\tdata: { x: number; y: number };\n\tdriverParam: string;\n\tenumValues: undefined;\n}>;\n\nexport class PgPointObjectBuilder<T extends ColumnBuilderBaseConfig<'json', 'PgPointObject'>>\n\textends PgColumnBuilder<T>\n{\n\tstatic override readonly [entityKind]: string = 'PgPointObjectBuilder';\n\n\tconstructor(name: string) {\n\t\tsuper(name, 'json', 'PgPointObject');\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyPgTable<{ name: TTableName }>,\n\t): PgPointObject<MakeColumnConfig<T, TTableName>> {\n\t\treturn new PgPointObject<MakeColumnConfig<T, TTableName>>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n}\n\nexport class PgPointObject<T extends ColumnBaseConfig<'json', 'PgPointObject'>> extends PgColumn<T> {\n\tstatic override readonly [entityKind]: string = 'PgPointObject';\n\n\tgetSQLType(): string {\n\t\treturn 'point';\n\t}\n\n\toverride mapFromDriverValue(value: string | { x: number; y: number }): { x: number; y: number } {\n\t\tif (typeof value === 'string') {\n\t\t\tconst [x, y] = value.slice(1, -1).split(',');\n\t\t\treturn { x: Number.parseFloat(x!), y: Number.parseFloat(y!) };\n\t\t}\n\t\treturn value;\n\t}\n\n\toverride mapToDriverValue(value: { x: number; y: number }): string {\n\t\treturn `(${value.x},${value.y})`;\n\t}\n}\n\nexport interface PgPointConfig<T extends 'tuple' | 'xy' = 'tuple' | 'xy'> {\n\tmode?: T;\n}\n\nexport function point(): PgPointTupleBuilderInitial<''>;\nexport function point<TMode extends PgPointConfig['mode'] & {}>(\n\tconfig?: PgPointConfig<TMode>,\n): Equal<TMode, 'xy'> extends true ? PgPointObjectBuilderInitial<''>\n\t: PgPointTupleBuilderInitial<''>;\nexport function point<TName extends string, TMode extends PgPointConfig['mode'] & {}>(\n\tname: TName,\n\tconfig?: PgPointConfig<TMode>,\n): Equal<TMode, 'xy'> extends true ? PgPointObjectBuilderInitial<TName>\n\t: PgPointTupleBuilderInitial<TName>;\nexport function point(a?: string | PgPointConfig, b?: PgPointConfig) {\n\tconst { name, config } = getColumnNameAndConfig<PgPointConfig>(a, b);\n\tif (!config?.mode || config.mode === 'tuple') {\n\t\treturn new PgPointTupleBuilder(name);\n\t}\n\treturn new PgPointObjectBuilder(name);\n}\n", "function hexToBytes(hex: string): Uint8Array {\n\tconst bytes: number[] = [];\n\tfor (let c = 0; c < hex.length; c += 2) {\n\t\tbytes.push(Number.parseInt(hex.slice(c, c + 2), 16));\n\t}\n\treturn new Uint8Array(bytes);\n}\n\nfunction bytesToFloat64(bytes: Uint8Array, offset: number): number {\n\tconst buffer = new ArrayBuffer(8);\n\tconst view = new DataView(buffer);\n\tfor (let i = 0; i < 8; i++) {\n\t\tview.setUint8(i, bytes[offset + i]!);\n\t}\n\treturn view.getFloat64(0, true);\n}\n\nexport function parseEWKB(hex: string): [number, number] {\n\tconst bytes = hexToBytes(hex);\n\n\tlet offset = 0;\n\n\t// Byte order: 1 is little-endian, 0 is big-endian\n\tconst byteOrder = bytes[offset];\n\toffset += 1;\n\n\tconst view = new DataView(bytes.buffer);\n\tconst geomType = view.getUint32(offset, byteOrder === 1);\n\toffset += 4;\n\n\tlet _srid: number | undefined;\n\tif (geomType & 0x20000000) { // SRID flag\n\t\t_srid = view.getUint32(offset, byteOrder === 1);\n\t\toffset += 4;\n\t}\n\n\tif ((geomType & 0xFFFF) === 1) {\n\t\tconst x = bytesToFloat64(bytes, offset);\n\t\toffset += 8;\n\t\tconst y = bytesToFloat64(bytes, offset);\n\t\toffset += 8;\n\n\t\treturn [x, y];\n\t}\n\n\tthrow new Error('Unsupported geometry type');\n}\n", "import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyPgTable } from '~/pg-core/table.ts';\n\nimport { type Equal, getColumnNameAndConfig } from '~/utils.ts';\nimport { PgColumn, PgColumnBuilder } from '../common.ts';\nimport { parseEWKB } from './utils.ts';\n\nexport type PgGeometryBuilderInitial<TName extends string> = PgGeometryBuilder<{\n\tname: TName;\n\tdataType: 'array';\n\tcolumnType: 'PgGeometry';\n\tdata: [number, number];\n\tdriverParam: string;\n\tenumValues: undefined;\n}>;\n\nexport class PgGeometryBuilder<T extends ColumnBuilderBaseConfig<'array', 'PgGeometry'>> extends PgColumnBuilder<T> {\n\tstatic override readonly [entityKind]: string = 'PgGeometryBuilder';\n\n\tconstructor(name: T['name']) {\n\t\tsuper(name, 'array', 'PgGeometry');\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyPgTable<{ name: TTableName }>,\n\t): PgGeometry<MakeColumnConfig<T, TTableName>> {\n\t\treturn new PgGeometry<MakeColumnConfig<T, TTableName>>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n}\n\nexport class PgGeometry<T extends ColumnBaseConfig<'array', 'PgGeometry'>> extends PgColumn<T> {\n\tstatic override readonly [entityKind]: string = 'PgGeometry';\n\n\tgetSQLType(): string {\n\t\treturn 'geometry(point)';\n\t}\n\n\toverride mapFromDriverValue(value: string): [number, number] {\n\t\treturn parseEWKB(value);\n\t}\n\n\toverride mapToDriverValue(value: [number, number]): string {\n\t\treturn `point(${value[0]} ${value[1]})`;\n\t}\n}\n\nexport type PgGeometryObjectBuilderInitial<TName extends string> = PgGeometryObjectBuilder<{\n\tname: TName;\n\tdataType: 'json';\n\tcolumnType: 'PgGeometryObject';\n\tdata: { x: number; y: number };\n\tdriverParam: string;\n\tenumValues: undefined;\n}>;\n\nexport class PgGeometryObjectBuilder<T extends ColumnBuilderBaseConfig<'json', 'PgGeometryObject'>>\n\textends PgColumnBuilder<T>\n{\n\tstatic override readonly [entityKind]: string = 'PgGeometryObjectBuilder';\n\n\tconstructor(name: T['name']) {\n\t\tsuper(name, 'json', 'PgGeometryObject');\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyPgTable<{ name: TTableName }>,\n\t): PgGeometryObject<MakeColumnConfig<T, TTableName>> {\n\t\treturn new PgGeometryObject<MakeColumnConfig<T, TTableName>>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n}\n\nexport class PgGeometryObject<T extends ColumnBaseConfig<'json', 'PgGeometryObject'>> extends PgColumn<T> {\n\tstatic override readonly [entityKind]: string = 'PgGeometryObject';\n\n\tgetSQLType(): string {\n\t\treturn 'geometry(point)';\n\t}\n\n\toverride mapFromDriverValue(value: string): { x: number; y: number } {\n\t\tconst parsed = parseEWKB(value);\n\t\treturn { x: parsed[0], y: parsed[1] };\n\t}\n\n\toverride mapToDriverValue(value: { x: number; y: number }): string {\n\t\treturn `point(${value.x} ${value.y})`;\n\t}\n}\n\nexport interface PgGeometryConfig<T extends 'tuple' | 'xy' = 'tuple' | 'xy'> {\n\tmode?: T;\n\ttype?: 'point' | (string & {});\n\tsrid?: number;\n}\n\nexport function geometry(): PgGeometryBuilderInitial<''>;\nexport function geometry<TMode extends PgGeometryConfig['mode'] & {}>(\n\tconfig?: PgGeometryConfig<TMode>,\n): Equal<TMode, 'xy'> extends true ? PgGeometryObjectBuilderInitial<''> : PgGeometryBuilderInitial<''>;\nexport function geometry<TName extends string, TMode extends PgGeometryConfig['mode'] & {}>(\n\tname: TName,\n\tconfig?: PgGeometryConfig<TMode>,\n): Equal<TMode, 'xy'> extends true ? PgGeometryObjectBuilderInitial<TName> : PgGeometryBuilderInitial<TName>;\nexport function geometry(a?: string | PgGeometryConfig, b?: PgGeometryConfig) {\n\tconst { name, config } = getColumnNameAndConfig<PgGeometryConfig>(a, b);\n\tif (!config?.mode || config.mode === 'tuple') {\n\t\treturn new PgGeometryBuilder(name);\n\t}\n\treturn new PgGeometryObjectBuilder(name);\n}\n", "import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyPgTable } from '~/pg-core/table.ts';\nimport { PgColumn, PgColumnBuilder } from './common.ts';\n\nexport type PgRealBuilderInitial<TName extends string> = PgRealBuilder<{\n\tname: TName;\n\tdataType: 'number';\n\tcolumnType: 'PgReal';\n\tdata: number;\n\tdriverParam: string | number;\n\tenumValues: undefined;\n}>;\n\nexport class PgRealBuilder<T extends ColumnBuilderBaseConfig<'number', 'PgReal'>> extends PgColumnBuilder<\n\tT,\n\t{ length: number | undefined }\n> {\n\tstatic override readonly [entityKind]: string = 'PgRealBuilder';\n\n\tconstructor(name: T['name'], length?: number) {\n\t\tsuper(name, 'number', 'PgReal');\n\t\tthis.config.length = length;\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyPgTable<{ name: TTableName }>,\n\t): PgReal<MakeColumnConfig<T, TTableName>> {\n\t\treturn new PgReal<MakeColumnConfig<T, TTableName>>(table, this.config as ColumnBuilderRuntimeConfig<any, any>);\n\t}\n}\n\nexport class PgReal<T extends ColumnBaseConfig<'number', 'PgReal'>> extends PgColumn<T> {\n\tstatic override readonly [entityKind]: string = 'PgReal';\n\n\tconstructor(table: AnyPgTable<{ name: T['tableName'] }>, config: PgRealBuilder<T>['config']) {\n\t\tsuper(table, config);\n\t}\n\n\tgetSQLType(): string {\n\t\treturn 'real';\n\t}\n\n\toverride mapFromDriverValue = (value: string | number): number => {\n\t\tif (typeof value === 'string') {\n\t\t\treturn Number.parseFloat(value);\n\t\t}\n\t\treturn value;\n\t};\n}\n\nexport function real(): PgRealBuilderInitial<''>;\nexport function real<TName extends string>(name: TName): PgRealBuilderInitial<TName>;\nexport function real(name?: string) {\n\treturn new PgRealBuilder(name ?? '');\n}\n", "import type {\n\tColumnBuilderBaseConfig,\n\tColumnBuilderRuntimeConfig,\n\tHas<PERSON><PERSON><PERSON>,\n\tMakeColumnConfig,\n\tNotNull,\n} from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyPgTable } from '~/pg-core/table.ts';\nimport { PgColumn, PgColumnBuilder } from './common.ts';\n\nexport type PgSerialBuilderInitial<TName extends string> = NotNull<\n\tHasDefault<\n\t\tPgSerialBuilder<{\n\t\t\tname: TName;\n\t\t\tdataType: 'number';\n\t\t\tcolumnType: 'PgSerial';\n\t\t\tdata: number;\n\t\t\tdriverParam: number;\n\t\t\tenumValues: undefined;\n\t\t}>\n\t>\n>;\n\nexport class PgSerialBuilder<T extends ColumnBuilderBaseConfig<'number', 'PgSerial'>> extends PgColumnBuilder<T> {\n\tstatic override readonly [entityKind]: string = 'PgSerialBuilder';\n\n\tconstructor(name: T['name']) {\n\t\tsuper(name, 'number', 'PgSerial');\n\t\tthis.config.hasDefault = true;\n\t\tthis.config.notNull = true;\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyPgTable<{ name: TTableName }>,\n\t): PgSerial<MakeColumnConfig<T, TTableName>> {\n\t\treturn new PgSerial<MakeColumnConfig<T, TTableName>>(table, this.config as ColumnBuilderRuntimeConfig<any, any>);\n\t}\n}\n\nexport class PgSerial<T extends ColumnBaseConfig<'number', 'PgSerial'>> extends PgColumn<T> {\n\tstatic override readonly [entityKind]: string = 'PgSerial';\n\n\tgetSQLType(): string {\n\t\treturn 'serial';\n\t}\n}\n\nexport function serial(): PgSerialBuilderInitial<''>;\nexport function serial<TName extends string>(name: TName): PgSerialBuilderInitial<TName>;\nexport function serial(name?: string) {\n\treturn new PgSerialBuilder(name ?? '');\n}\n", "import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyPgTable } from '~/pg-core/table.ts';\nimport { PgColumn } from './common.ts';\nimport { PgIntColumnBaseBuilder } from './int.common.ts';\n\nexport type PgSmallIntBuilderInitial<TName extends string> = PgSmallIntBuilder<{\n\tname: TName;\n\tdataType: 'number';\n\tcolumnType: 'PgSmallInt';\n\tdata: number;\n\tdriverParam: number | string;\n\tenumValues: undefined;\n}>;\n\nexport class PgSmallIntBuilder<T extends ColumnBuilderBaseConfig<'number', 'PgSmallInt'>>\n\textends PgIntColumnBaseBuilder<T>\n{\n\tstatic override readonly [entityKind]: string = 'PgSmallIntBuilder';\n\n\tconstructor(name: T['name']) {\n\t\tsuper(name, 'number', 'PgSmallInt');\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyPgTable<{ name: TTableName }>,\n\t): PgSmallInt<MakeColumnConfig<T, TTableName>> {\n\t\treturn new PgSmallInt<MakeColumnConfig<T, TTableName>>(table, this.config as ColumnBuilderRuntimeConfig<any, any>);\n\t}\n}\n\nexport class PgSmallInt<T extends ColumnBaseConfig<'number', 'PgSmallInt'>> extends PgColumn<T> {\n\tstatic override readonly [entityKind]: string = 'PgSmallInt';\n\n\tgetSQLType(): string {\n\t\treturn 'smallint';\n\t}\n\n\toverride mapFromDriverValue = (value: number | string): number => {\n\t\tif (typeof value === 'string') {\n\t\t\treturn Number(value);\n\t\t}\n\t\treturn value;\n\t};\n}\n\nexport function smallint(): PgSmallIntBuilderInitial<''>;\nexport function smallint<TName extends string>(name: TName): PgSmallIntBuilderInitial<TName>;\nexport function smallint(name?: string) {\n\treturn new PgSmallIntBuilder(name ?? '');\n}\n", "import type {\n\tColumnBuilderBaseConfig,\n\tColumnBuilderRuntimeConfig,\n\tHas<PERSON><PERSON><PERSON>,\n\tMakeColumnConfig,\n\tNotNull,\n} from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyPgTable } from '~/pg-core/table.ts';\nimport { PgColumn, PgColumnBuilder } from './common.ts';\n\nexport type PgSmallSerialBuilderInitial<TName extends string> = NotNull<\n\tHasDefault<\n\t\tPgSmallSerialBuilder<{\n\t\t\tname: TName;\n\t\t\tdataType: 'number';\n\t\t\tcolumnType: 'PgSmallSerial';\n\t\t\tdata: number;\n\t\t\tdriverParam: number;\n\t\t\tenumValues: undefined;\n\t\t}>\n\t>\n>;\n\nexport class PgSmallSerialBuilder<T extends ColumnBuilderBaseConfig<'number', 'PgSmallSerial'>>\n\textends PgColumnBuilder<T>\n{\n\tstatic override readonly [entityKind]: string = 'PgSmallSerialBuilder';\n\n\tconstructor(name: T['name']) {\n\t\tsuper(name, 'number', 'PgSmallSerial');\n\t\tthis.config.hasDefault = true;\n\t\tthis.config.notNull = true;\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyPgTable<{ name: TTableName }>,\n\t): PgSmallSerial<MakeColumnConfig<T, TTableName>> {\n\t\treturn new PgSmallSerial<MakeColumnConfig<T, TTableName>>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n}\n\nexport class PgSmallSerial<T extends ColumnBaseConfig<'number', 'PgSmallSerial'>> extends PgColumn<T> {\n\tstatic override readonly [entityKind]: string = 'PgSmallSerial';\n\n\tgetSQLType(): string {\n\t\treturn 'smallserial';\n\t}\n}\n\nexport function smallserial(): PgSmallSerialBuilderInitial<''>;\nexport function smallserial<TName extends string>(name: TName): PgSmallSerialBuilderInitial<TName>;\nexport function smallserial(name?: string) {\n\treturn new PgSmallSerialBuilder(name ?? '');\n}\n", "import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyPgTable } from '~/pg-core/table.ts';\nimport { getColumnNameAndConfig, type Writable } from '~/utils.ts';\nimport { PgColumn, PgColumnBuilder } from './common.ts';\n\ntype PgTextBuilderInitial<TName extends string, TEnum extends [string, ...string[]]> = PgTextBuilder<{\n\tname: TName;\n\tdataType: 'string';\n\tcolumnType: 'PgText';\n\tdata: TEnum[number];\n\tenumValues: TEnum;\n\tdriverParam: string;\n}>;\n\nexport class PgTextBuilder<\n\tT extends ColumnBuilderBaseConfig<'string', 'PgText'>,\n> extends PgColumnBuilder<T, { enumValues: T['enumValues'] }> {\n\tstatic override readonly [entityKind]: string = 'PgTextBuilder';\n\n\tconstructor(\n\t\tname: T['name'],\n\t\tconfig: PgTextConfig<T['enumValues']>,\n\t) {\n\t\tsuper(name, 'string', 'PgText');\n\t\tthis.config.enumValues = config.enum;\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyPgTable<{ name: TTableName }>,\n\t): PgText<MakeColumnConfig<T, TTableName>> {\n\t\treturn new PgText<MakeColumnConfig<T, TTableName>>(table, this.config as ColumnBuilderRuntimeConfig<any, any>);\n\t}\n}\n\nexport class PgText<T extends ColumnBaseConfig<'string', 'PgText'>>\n\textends PgColumn<T, { enumValues: T['enumValues'] }>\n{\n\tstatic override readonly [entityKind]: string = 'PgText';\n\n\toverride readonly enumValues = this.config.enumValues;\n\n\tgetSQLType(): string {\n\t\treturn 'text';\n\t}\n}\n\nexport interface PgTextConfig<\n\tTEnum extends readonly string[] | string[] | undefined = readonly string[] | string[] | undefined,\n> {\n\tenum?: TEnum;\n}\n\nexport function text(): PgTextBuilderInitial<'', [string, ...string[]]>;\nexport function text<U extends string, T extends Readonly<[U, ...U[]]>>(\n\tconfig?: PgTextConfig<T | Writable<T>>,\n): PgTextBuilderInitial<'', Writable<T>>;\nexport function text<TName extends string, U extends string, T extends Readonly<[U, ...U[]]>>(\n\tname: TName,\n\tconfig?: PgTextConfig<T | Writable<T>>,\n): PgTextBuilderInitial<TName, Writable<T>>;\nexport function text(a?: string | PgTextConfig, b: PgTextConfig = {}): any {\n\tconst { name, config } = getColumnNameAndConfig<PgTextConfig>(a, b);\n\treturn new PgTextBuilder(name, config as any);\n}\n", "import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyPgTable } from '~/pg-core/table.ts';\nimport { getColumnNameAndConfig } from '~/utils.ts';\nimport { PgColumn } from './common.ts';\nimport { PgDateColumnBaseBuilder } from './date.common.ts';\nimport type { Precision } from './timestamp.ts';\n\nexport type PgTimeBuilderInitial<TName extends string> = PgTimeBuilder<{\n\tname: TName;\n\tdataType: 'string';\n\tcolumnType: 'PgTime';\n\tdata: string;\n\tdriverParam: string;\n\tenumValues: undefined;\n}>;\n\nexport class PgTimeBuilder<T extends ColumnBuilderBaseConfig<'string', 'PgTime'>> extends PgDateColumnBaseBuilder<\n\tT,\n\t{ withTimezone: boolean; precision: number | undefined }\n> {\n\tstatic override readonly [entityKind]: string = 'PgTimeBuilder';\n\n\tconstructor(\n\t\tname: T['name'],\n\t\treadonly withTimezone: boolean,\n\t\treadonly precision: number | undefined,\n\t) {\n\t\tsuper(name, 'string', 'PgTime');\n\t\tthis.config.withTimezone = withTimezone;\n\t\tthis.config.precision = precision;\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyPgTable<{ name: TTableName }>,\n\t): PgTime<MakeColumnConfig<T, TTableName>> {\n\t\treturn new PgTime<MakeColumnConfig<T, TTableName>>(table, this.config as ColumnBuilderRuntimeConfig<any, any>);\n\t}\n}\n\nexport class PgTime<T extends ColumnBaseConfig<'string', 'PgTime'>> extends PgColumn<T> {\n\tstatic override readonly [entityKind]: string = 'PgTime';\n\n\treadonly withTimezone: boolean;\n\treadonly precision: number | undefined;\n\n\tconstructor(table: AnyPgTable<{ name: T['tableName'] }>, config: PgTimeBuilder<T>['config']) {\n\t\tsuper(table, config);\n\t\tthis.withTimezone = config.withTimezone;\n\t\tthis.precision = config.precision;\n\t}\n\n\tgetSQLType(): string {\n\t\tconst precision = this.precision === undefined ? '' : `(${this.precision})`;\n\t\treturn `time${precision}${this.withTimezone ? ' with time zone' : ''}`;\n\t}\n}\n\nexport interface TimeConfig {\n\tprecision?: Precision;\n\twithTimezone?: boolean;\n}\n\nexport function time(): PgTimeBuilderInitial<''>;\nexport function time(config?: TimeConfig): PgTimeBuilderInitial<''>;\nexport function time<TName extends string>(name: TName, config?: TimeConfig): PgTimeBuilderInitial<TName>;\nexport function time(a?: string | TimeConfig, b: TimeConfig = {}) {\n\tconst { name, config } = getColumnNameAndConfig<TimeConfig>(a, b);\n\treturn new PgTimeBuilder(name, config.withTimezone ?? false, config.precision);\n}\n", "import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyPgTable } from '~/pg-core/table.ts';\nimport { type Equal, getColumnNameAndConfig } from '~/utils.ts';\nimport { PgColumn } from './common.ts';\nimport { PgDateColumnBaseBuilder } from './date.common.ts';\n\nexport type PgTimestampBuilderInitial<TName extends string> = PgTimestampBuilder<{\n\tname: TName;\n\tdataType: 'date';\n\tcolumnType: 'PgTimestamp';\n\tdata: Date;\n\tdriverParam: string;\n\tenumValues: undefined;\n}>;\n\nexport class PgTimestampBuilder<T extends ColumnBuilderBaseConfig<'date', 'PgTimestamp'>>\n\textends PgDateColumnBaseBuilder<\n\t\tT,\n\t\t{ withTimezone: boolean; precision: number | undefined }\n\t>\n{\n\tstatic override readonly [entityKind]: string = 'PgTimestampBuilder';\n\n\tconstructor(\n\t\tname: T['name'],\n\t\twithTimezone: boolean,\n\t\tprecision: number | undefined,\n\t) {\n\t\tsuper(name, 'date', 'PgTimestamp');\n\t\tthis.config.withTimezone = withTimezone;\n\t\tthis.config.precision = precision;\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyPgTable<{ name: TTableName }>,\n\t): PgTimestamp<MakeColumnConfig<T, TTableName>> {\n\t\treturn new PgTimestamp<MakeColumnConfig<T, TTableName>>(table, this.config as ColumnBuilderRuntimeConfig<any, any>);\n\t}\n}\n\nexport class PgTimestamp<T extends ColumnBaseConfig<'date', 'PgTimestamp'>> extends PgColumn<T> {\n\tstatic override readonly [entityKind]: string = 'PgTimestamp';\n\n\treadonly withTimezone: boolean;\n\treadonly precision: number | undefined;\n\n\tconstructor(table: AnyPgTable<{ name: T['tableName'] }>, config: PgTimestampBuilder<T>['config']) {\n\t\tsuper(table, config);\n\t\tthis.withTimezone = config.withTimezone;\n\t\tthis.precision = config.precision;\n\t}\n\n\tgetSQLType(): string {\n\t\tconst precision = this.precision === undefined ? '' : ` (${this.precision})`;\n\t\treturn `timestamp${precision}${this.withTimezone ? ' with time zone' : ''}`;\n\t}\n\n\toverride mapFromDriverValue = (value: string): Date | null => {\n\t\treturn new Date(this.withTimezone ? value : value + '+0000');\n\t};\n\n\toverride mapToDriverValue = (value: Date): string => {\n\t\treturn value.toISOString();\n\t};\n}\n\nexport type PgTimestampStringBuilderInitial<TName extends string> = PgTimestampStringBuilder<{\n\tname: TName;\n\tdataType: 'string';\n\tcolumnType: 'PgTimestampString';\n\tdata: string;\n\tdriverParam: string;\n\tenumValues: undefined;\n}>;\n\nexport class PgTimestampStringBuilder<T extends ColumnBuilderBaseConfig<'string', 'PgTimestampString'>>\n\textends PgDateColumnBaseBuilder<\n\t\tT,\n\t\t{ withTimezone: boolean; precision: number | undefined }\n\t>\n{\n\tstatic override readonly [entityKind]: string = 'PgTimestampStringBuilder';\n\n\tconstructor(\n\t\tname: T['name'],\n\t\twithTimezone: boolean,\n\t\tprecision: number | undefined,\n\t) {\n\t\tsuper(name, 'string', 'PgTimestampString');\n\t\tthis.config.withTimezone = withTimezone;\n\t\tthis.config.precision = precision;\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyPgTable<{ name: TTableName }>,\n\t): PgTimestampString<MakeColumnConfig<T, TTableName>> {\n\t\treturn new PgTimestampString<MakeColumnConfig<T, TTableName>>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n}\n\nexport class PgTimestampString<T extends ColumnBaseConfig<'string', 'PgTimestampString'>> extends PgColumn<T> {\n\tstatic override readonly [entityKind]: string = 'PgTimestampString';\n\n\treadonly withTimezone: boolean;\n\treadonly precision: number | undefined;\n\n\tconstructor(table: AnyPgTable<{ name: T['tableName'] }>, config: PgTimestampStringBuilder<T>['config']) {\n\t\tsuper(table, config);\n\t\tthis.withTimezone = config.withTimezone;\n\t\tthis.precision = config.precision;\n\t}\n\n\tgetSQLType(): string {\n\t\tconst precision = this.precision === undefined ? '' : `(${this.precision})`;\n\t\treturn `timestamp${precision}${this.withTimezone ? ' with time zone' : ''}`;\n\t}\n}\n\nexport type Precision = 0 | 1 | 2 | 3 | 4 | 5 | 6;\n\nexport interface PgTimestampConfig<TMode extends 'date' | 'string' = 'date' | 'string'> {\n\tmode?: TMode;\n\tprecision?: Precision;\n\twithTimezone?: boolean;\n}\n\nexport function timestamp(): PgTimestampBuilderInitial<''>;\nexport function timestamp<TMode extends PgTimestampConfig['mode'] & {}>(\n\tconfig?: PgTimestampConfig<TMode>,\n): Equal<TMode, 'string'> extends true ? PgTimestampStringBuilderInitial<''> : PgTimestampBuilderInitial<''>;\nexport function timestamp<TName extends string, TMode extends PgTimestampConfig['mode'] & {}>(\n\tname: TName,\n\tconfig?: PgTimestampConfig<TMode>,\n): Equal<TMode, 'string'> extends true ? PgTimestampStringBuilderInitial<TName> : PgTimestampBuilderInitial<TName>;\nexport function timestamp(a?: string | PgTimestampConfig, b: PgTimestampConfig = {}) {\n\tconst { name, config } = getColumnNameAndConfig<PgTimestampConfig | undefined>(a, b);\n\tif (config?.mode === 'string') {\n\t\treturn new PgTimestampStringBuilder(name, config.withTimezone ?? false, config.precision);\n\t}\n\treturn new PgTimestampBuilder(name, config?.withTimezone ?? false, config?.precision);\n}\n", "import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyPgTable } from '~/pg-core/table.ts';\nimport { sql } from '~/sql/sql.ts';\nimport { PgColumn, PgColumnBuilder } from './common.ts';\n\nexport type PgUUIDBuilderInitial<TName extends string> = PgUUIDBuilder<{\n\tname: TName;\n\tdataType: 'string';\n\tcolumnType: 'PgUUID';\n\tdata: string;\n\tdriverParam: string;\n\tenumValues: undefined;\n}>;\n\nexport class PgUUIDBuilder<T extends ColumnBuilderBaseConfig<'string', 'PgUUID'>> extends PgColumnBuilder<T> {\n\tstatic override readonly [entityKind]: string = 'PgUUIDBuilder';\n\n\tconstructor(name: T['name']) {\n\t\tsuper(name, 'string', 'PgUUID');\n\t}\n\n\t/**\n\t * Adds `default gen_random_uuid()` to the column definition.\n\t */\n\tdefaultRandom(): ReturnType<this['default']> {\n\t\treturn this.default(sql`gen_random_uuid()`) as ReturnType<this['default']>;\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyPgTable<{ name: TTableName }>,\n\t): PgUUID<MakeColumnConfig<T, TTableName>> {\n\t\treturn new PgUUID<MakeColumnConfig<T, TTableName>>(table, this.config as ColumnBuilderRuntimeConfig<any, any>);\n\t}\n}\n\nexport class PgUUID<T extends ColumnBaseConfig<'string', 'PgUUID'>> extends PgColumn<T> {\n\tstatic override readonly [entityKind]: string = 'PgUUID';\n\n\tgetSQLType(): string {\n\t\treturn 'uuid';\n\t}\n}\n\nexport function uuid(): PgUUIDBuilderInitial<''>;\nexport function uuid<TName extends string>(name: TName): PgUUIDBuilderInitial<TName>;\nexport function uuid(name?: string) {\n\treturn new PgUUIDBuilder(name ?? '');\n}\n", "import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyPgTable } from '~/pg-core/table.ts';\nimport { getColumnNameAndConfig, type Writable } from '~/utils.ts';\nimport { PgColumn, PgColumnBuilder } from './common.ts';\n\nexport type PgVarcharBuilderInitial<\n\tTName extends string,\n\tTEnum extends [string, ...string[]],\n\tTLength extends number | undefined,\n> = PgVarcharBuilder<{\n\tname: TName;\n\tdataType: 'string';\n\tcolumnType: 'PgVarchar';\n\tdata: TEnum[number];\n\tdriverParam: string;\n\tenumValues: TEnum;\n\tlength: TLength;\n}>;\n\nexport class PgVarcharBuilder<\n\tT extends ColumnBuilderBaseConfig<'string', 'PgVarchar'> & { length?: number | undefined },\n> extends PgColumnBuilder<\n\tT,\n\t{ length: T['length']; enumValues: T['enumValues'] },\n\t{ length: T['length'] }\n> {\n\tstatic override readonly [entityKind]: string = 'PgVarcharBuilder';\n\n\tconstructor(name: T['name'], config: PgVarcharConfig<T['enumValues'], T['length']>) {\n\t\tsuper(name, 'string', 'PgVarchar');\n\t\tthis.config.length = config.length;\n\t\tthis.config.enumValues = config.enum;\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyPgTable<{ name: TTableName }>,\n\t): PgVarchar<MakeColumnConfig<T, TTableName> & { length: T['length'] }> {\n\t\treturn new PgVarchar<MakeColumnConfig<T, TTableName> & { length: T['length'] }>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n}\n\nexport class PgVarchar<T extends ColumnBaseConfig<'string', 'PgVarchar'> & { length?: number | undefined }>\n\textends PgColumn<T, { length: T['length']; enumValues: T['enumValues'] }, { length: T['length'] }>\n{\n\tstatic override readonly [entityKind]: string = 'PgVarchar';\n\n\treadonly length = this.config.length;\n\toverride readonly enumValues = this.config.enumValues;\n\n\tgetSQLType(): string {\n\t\treturn this.length === undefined ? `varchar` : `varchar(${this.length})`;\n\t}\n}\n\nexport interface PgVarcharConfig<\n\tTEnum extends readonly string[] | string[] | undefined = readonly string[] | string[] | undefined,\n\tTLength extends number | undefined = number | undefined,\n> {\n\tenum?: TEnum;\n\tlength?: TLength;\n}\n\nexport function varchar(): PgVarcharBuilderInitial<'', [string, ...string[]], undefined>;\nexport function varchar<\n\tU extends string,\n\tT extends Readonly<[U, ...U[]]>,\n\tL extends number | undefined,\n>(\n\tconfig?: PgVarcharConfig<T | Writable<T>, L>,\n): PgVarcharBuilderInitial<'', Writable<T>, L>;\nexport function varchar<\n\tTName extends string,\n\tU extends string,\n\tT extends Readonly<[U, ...U[]]>,\n\tL extends number | undefined,\n>(\n\tname: TName,\n\tconfig?: PgVarcharConfig<T | Writable<T>, L>,\n): PgVarcharBuilderInitial<TName, Writable<T>, L>;\nexport function varchar(a?: string | PgVarcharConfig, b: PgVarcharConfig = {}): any {\n\tconst { name, config } = getColumnNameAndConfig<PgVarcharConfig>(a, b);\n\treturn new PgVarcharBuilder(name, config as any);\n}\n", "import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyPgTable } from '~/pg-core/table.ts';\nimport { getColumnNameAndConfig } from '~/utils.ts';\nimport { PgColumn, PgColumnBuilder } from '../common.ts';\n\nexport type PgBinaryVectorBuilderInitial<TName extends string, TDimensions extends number> = PgBinaryVectorBuilder<{\n\tname: TName;\n\tdataType: 'string';\n\tcolumnType: 'PgBinaryVector';\n\tdata: string;\n\tdriverParam: string;\n\tenumValues: undefined;\n\tdimensions: TDimensions;\n}>;\n\nexport class PgBinaryVectorBuilder<\n\tT extends ColumnBuilderBaseConfig<'string', 'PgBinaryVector'> & { dimensions: number },\n> extends PgColumnBuilder<\n\tT,\n\t{ dimensions: T['dimensions'] }\n> {\n\tstatic override readonly [entityKind]: string = 'PgBinaryVectorBuilder';\n\n\tconstructor(name: string, config: PgBinaryVectorConfig<T['dimensions']>) {\n\t\tsuper(name, 'string', 'PgBinaryVector');\n\t\tthis.config.dimensions = config.dimensions;\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyPgTable<{ name: TTableName }>,\n\t): PgBinaryVector<MakeColumnConfig<T, TTableName> & { dimensions: T['dimensions'] }> {\n\t\treturn new PgBinaryVector<MakeColumnConfig<T, TTableName> & { dimensions: T['dimensions'] }>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n}\n\nexport class PgBinaryVector<T extends ColumnBaseConfig<'string', 'PgBinaryVector'> & { dimensions: number }>\n\textends PgColumn<T, { dimensions: T['dimensions'] }, { dimensions: T['dimensions'] }>\n{\n\tstatic override readonly [entityKind]: string = 'PgBinaryVector';\n\n\treadonly dimensions = this.config.dimensions;\n\n\tgetSQLType(): string {\n\t\treturn `bit(${this.dimensions})`;\n\t}\n}\n\nexport interface PgBinaryVectorConfig<TDimensions extends number = number> {\n\tdimensions: TDimensions;\n}\n\nexport function bit<D extends number>(\n\tconfig: PgBinaryVectorConfig<D>,\n): PgBinaryVectorBuilderInitial<'', D>;\nexport function bit<TName extends string, D extends number>(\n\tname: TName,\n\tconfig: PgBinaryVectorConfig<D>,\n): PgBinaryVectorBuilderInitial<TName, D>;\nexport function bit(a: string | PgBinaryVectorConfig, b?: PgBinaryVectorConfig) {\n\tconst { name, config } = getColumnNameAndConfig<PgBinaryVectorConfig>(a, b);\n\treturn new PgBinaryVectorBuilder(name, config);\n}\n", "import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyPgTable } from '~/pg-core/table.ts';\nimport { getColumnNameAndConfig } from '~/utils.ts';\nimport { PgColumn, PgColumnBuilder } from '../common.ts';\n\nexport type PgHalfVectorBuilderInitial<TName extends string, TDimensions extends number> = PgHalfVectorBuilder<{\n\tname: TName;\n\tdataType: 'array';\n\tcolumnType: 'PgHalfVector';\n\tdata: number[];\n\tdriverParam: string;\n\tenumValues: undefined;\n\tdimensions: TDimensions;\n}>;\n\nexport class PgHalfVectorBuilder<T extends ColumnBuilderBaseConfig<'array', 'PgHalfVector'> & { dimensions: number }>\n\textends PgColumnBuilder<\n\t\tT,\n\t\t{ dimensions: T['dimensions'] },\n\t\t{ dimensions: T['dimensions'] }\n\t>\n{\n\tstatic override readonly [entityKind]: string = 'PgHalfVectorBuilder';\n\n\tconstructor(name: string, config: PgHalfVectorConfig<T['dimensions']>) {\n\t\tsuper(name, 'array', 'PgHalfVector');\n\t\tthis.config.dimensions = config.dimensions;\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyPgTable<{ name: TTableName }>,\n\t): PgHalfVector<MakeColumnConfig<T, TTableName> & { dimensions: T['dimensions'] }> {\n\t\treturn new PgHalfVector<MakeColumnConfig<T, TTableName> & { dimensions: T['dimensions'] }>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n}\n\nexport class PgHalfVector<T extends ColumnBaseConfig<'array', 'PgHalfVector'> & { dimensions: number }>\n\textends PgColumn<T, { dimensions: T['dimensions'] }, { dimensions: T['dimensions'] }>\n{\n\tstatic override readonly [entityKind]: string = 'PgHalfVector';\n\n\treadonly dimensions: T['dimensions'] = this.config.dimensions;\n\n\tgetSQLType(): string {\n\t\treturn `halfvec(${this.dimensions})`;\n\t}\n\n\toverride mapToDriverValue(value: unknown): unknown {\n\t\treturn JSON.stringify(value);\n\t}\n\n\toverride mapFromDriverValue(value: string): unknown {\n\t\treturn value\n\t\t\t.slice(1, -1)\n\t\t\t.split(',')\n\t\t\t.map((v) => Number.parseFloat(v));\n\t}\n}\n\nexport interface PgHalfVectorConfig<TDimensions extends number = number> {\n\tdimensions: TDimensions;\n}\n\nexport function halfvec<D extends number>(\n\tconfig: PgHalfVectorConfig<D>,\n): PgHalfVectorBuilderInitial<'', D>;\nexport function halfvec<TName extends string, D extends number>(\n\tname: TName,\n\tconfig: PgHalfVectorConfig,\n): PgHalfVectorBuilderInitial<TName, D>;\nexport function halfvec(a: string | PgHalfVectorConfig, b?: PgHalfVectorConfig) {\n\tconst { name, config } = getColumnNameAndConfig<PgHalfVectorConfig>(a, b);\n\treturn new PgHalfVectorBuilder(name, config);\n}\n", "import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyPgTable } from '~/pg-core/table.ts';\nimport { getColumnNameAndConfig } from '~/utils.ts';\nimport { PgColumn, PgColumnBuilder } from '../common.ts';\n\nexport type PgSparseVectorBuilderInitial<TName extends string> = PgSparseVectorBuilder<{\n\tname: TName;\n\tdataType: 'string';\n\tcolumnType: 'PgSparseVector';\n\tdata: string;\n\tdriverParam: string;\n\tenumValues: undefined;\n}>;\n\nexport class PgSparseVectorBuilder<T extends ColumnBuilderBaseConfig<'string', 'PgSparseVector'>>\n\textends PgColumnBuilder<\n\t\tT,\n\t\t{ dimensions: number | undefined }\n\t>\n{\n\tstatic override readonly [entityKind]: string = 'PgSparseVectorBuilder';\n\n\tconstructor(name: string, config: PgSparseVectorConfig) {\n\t\tsuper(name, 'string', 'PgSparseVector');\n\t\tthis.config.dimensions = config.dimensions;\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyPgTable<{ name: TTableName }>,\n\t): PgSparseVector<MakeColumnConfig<T, TTableName>> {\n\t\treturn new PgSparseVector<MakeColumnConfig<T, TTableName>>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n}\n\nexport class PgSparseVector<T extends ColumnBaseConfig<'string', 'PgSparseVector'>>\n\textends PgColumn<T, { dimensions: number | undefined }>\n{\n\tstatic override readonly [entityKind]: string = 'PgSparseVector';\n\n\treadonly dimensions = this.config.dimensions;\n\n\tgetSQLType(): string {\n\t\treturn `sparsevec(${this.dimensions})`;\n\t}\n}\n\nexport interface PgSparseVectorConfig {\n\tdimensions: number;\n}\n\nexport function sparsevec(\n\tconfig: PgSparseVectorConfig,\n): PgSparseVectorBuilderInitial<''>;\nexport function sparsevec<TName extends string>(\n\tname: TName,\n\tconfig: PgSparseVectorConfig,\n): PgSparseVectorBuilderInitial<TName>;\nexport function sparsevec(a: string | PgSparseVectorConfig, b?: PgSparseVectorConfig) {\n\tconst { name, config } = getColumnNameAndConfig<PgSparseVectorConfig>(a, b);\n\treturn new PgSparseVectorBuilder(name, config);\n}\n", "import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyPgTable } from '~/pg-core/table.ts';\nimport { getColumnNameAndConfig } from '~/utils.ts';\nimport { PgColumn, PgColumnBuilder } from '../common.ts';\n\nexport type PgVectorBuilderInitial<TName extends string, TDimensions extends number> = PgVectorBuilder<{\n\tname: TName;\n\tdataType: 'array';\n\tcolumnType: 'PgVector';\n\tdata: number[];\n\tdriverParam: string;\n\tenumValues: undefined;\n\tdimensions: TDimensions;\n}>;\n\nexport class PgVectorBuilder<T extends ColumnBuilderBaseConfig<'array', 'PgVector'> & { dimensions: number }>\n\textends PgColumnBuilder<\n\t\tT,\n\t\t{ dimensions: T['dimensions'] },\n\t\t{ dimensions: T['dimensions'] }\n\t>\n{\n\tstatic override readonly [entityKind]: string = 'PgVectorBuilder';\n\n\tconstructor(name: string, config: PgVectorConfig<T['dimensions']>) {\n\t\tsuper(name, 'array', 'PgVector');\n\t\tthis.config.dimensions = config.dimensions;\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyPgTable<{ name: TTableName }>,\n\t): PgVector<MakeColumnConfig<T, TTableName> & { dimensions: T['dimensions'] }> {\n\t\treturn new PgVector<MakeColumnConfig<T, TTableName> & { dimensions: T['dimensions'] }>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n}\n\nexport class PgVector<T extends ColumnBaseConfig<'array', 'PgVector'> & { dimensions: number | undefined }>\n\textends PgColumn<T, { dimensions: T['dimensions'] }, { dimensions: T['dimensions'] }>\n{\n\tstatic override readonly [entityKind]: string = 'PgVector';\n\n\treadonly dimensions: T['dimensions'] = this.config.dimensions;\n\n\tgetSQLType(): string {\n\t\treturn `vector(${this.dimensions})`;\n\t}\n\n\toverride mapToDriverValue(value: unknown): unknown {\n\t\treturn JSON.stringify(value);\n\t}\n\n\toverride mapFromDriverValue(value: string): unknown {\n\t\treturn value\n\t\t\t.slice(1, -1)\n\t\t\t.split(',')\n\t\t\t.map((v) => Number.parseFloat(v));\n\t}\n}\n\nexport interface PgVectorConfig<TDimensions extends number = number> {\n\tdimensions: TDimensions;\n}\n\nexport function vector<D extends number>(\n\tconfig: PgVectorConfig<D>,\n): PgVectorBuilderInitial<'', D>;\nexport function vector<TName extends string, D extends number>(\n\tname: TName,\n\tconfig: PgVectorConfig<D>,\n): PgVectorBuilderInitial<TName, D>;\nexport function vector(a: string | PgVectorConfig, b?: PgVectorConfig) {\n\tconst { name, config } = getColumnNameAndConfig<PgVectorConfig>(a, b);\n\treturn new PgVectorBuilder(name, config);\n}\n", "import { bigint } from './bigint.ts';\nimport { bigserial } from './bigserial.ts';\nimport { boolean } from './boolean.ts';\nimport { char } from './char.ts';\nimport { cidr } from './cidr.ts';\nimport { customType } from './custom.ts';\nimport { date } from './date.ts';\nimport { doublePrecision } from './double-precision.ts';\nimport { inet } from './inet.ts';\nimport { integer } from './integer.ts';\nimport { interval } from './interval.ts';\nimport { json } from './json.ts';\nimport { jsonb } from './jsonb.ts';\nimport { line } from './line.ts';\nimport { macaddr } from './macaddr.ts';\nimport { macaddr8 } from './macaddr8.ts';\nimport { numeric } from './numeric.ts';\nimport { point } from './point.ts';\nimport { geometry } from './postgis_extension/geometry.ts';\nimport { real } from './real.ts';\nimport { serial } from './serial.ts';\nimport { smallint } from './smallint.ts';\nimport { smallserial } from './smallserial.ts';\nimport { text } from './text.ts';\nimport { time } from './time.ts';\nimport { timestamp } from './timestamp.ts';\nimport { uuid } from './uuid.ts';\nimport { varchar } from './varchar.ts';\nimport { bit } from './vector_extension/bit.ts';\nimport { halfvec } from './vector_extension/halfvec.ts';\nimport { sparsevec } from './vector_extension/sparsevec.ts';\nimport { vector } from './vector_extension/vector.ts';\n\nexport function getPgColumnBuilders() {\n\treturn {\n\t\tbigint,\n\t\tbigserial,\n\t\tboolean,\n\t\tchar,\n\t\tcidr,\n\t\tcustomType,\n\t\tdate,\n\t\tdoublePrecision,\n\t\tinet,\n\t\tinteger,\n\t\tinterval,\n\t\tjson,\n\t\tjsonb,\n\t\tline,\n\t\tmacaddr,\n\t\tmacaddr8,\n\t\tnumeric,\n\t\tpoint,\n\t\tgeometry,\n\t\treal,\n\t\tserial,\n\t\tsmallint,\n\t\tsmallserial,\n\t\ttext,\n\t\ttime,\n\t\ttimestamp,\n\t\tuuid,\n\t\tvarchar,\n\t\tbit,\n\t\thalfvec,\n\t\tsparsevec,\n\t\tvector,\n\t};\n}\n\nexport type PgColumnsBuilders = ReturnType<typeof getPgColumnBuilders>;\n", "import type { BuildColumns, BuildExtraConfigColumns } from '~/column-builder.ts';\nimport { entityKind } from '~/entity.ts';\nimport { Table, type TableConfig as TableConfigBase, type UpdateTableConfig } from '~/table.ts';\nimport type { CheckBuilder } from './checks.ts';\nimport { getPgColumnBuilders, type PgColumnsBuilders } from './columns/all.ts';\nimport type { PgColumn, PgColumnBuilder, PgColumnBuilderBase } from './columns/common.ts';\nimport type { ForeignKey, ForeignKeyBuilder } from './foreign-keys.ts';\nimport type { AnyIndexBuilder } from './indexes.ts';\nimport type { PgPolicy } from './policies.ts';\nimport type { PrimaryKeyBuilder } from './primary-keys.ts';\nimport type { UniqueConstraintBuilder } from './unique-constraint.ts';\n\nexport type PgTableExtraConfigValue =\n\t| AnyIndexBuilder\n\t| CheckBuilder\n\t| ForeignKeyBuilder\n\t| PrimaryKeyBuilder\n\t| UniqueConstraintBuilder\n\t| PgPolicy;\n\nexport type PgTableExtraConfig = Record<\n\tstring,\n\tPgTableExtraConfigValue\n>;\n\nexport type TableConfig = TableConfigBase<PgColumn>;\n\n/** @internal */\nexport const InlineForeignKeys = Symbol.for('drizzle:PgInlineForeignKeys');\n/** @internal */\nexport const EnableRLS = Symbol.for('drizzle:EnableRLS');\n\nexport class PgTable<T extends TableConfig = TableConfig> extends Table<T> {\n\tstatic override readonly [entityKind]: string = 'PgTable';\n\n\t/** @internal */\n\tstatic override readonly Symbol = Object.assign({}, Table.Symbol, {\n\t\tInlineForeignKeys: InlineForeignKeys as typeof InlineForeignKeys,\n\t\tEnableRLS: EnableRLS as typeof EnableRLS,\n\t});\n\n\t/**@internal */\n\t[InlineForeignKeys]: ForeignKey[] = [];\n\n\t/** @internal */\n\t[EnableRLS]: boolean = false;\n\n\t/** @internal */\n\toverride [Table.Symbol.ExtraConfigBuilder]: ((self: Record<string, PgColumn>) => PgTableExtraConfig) | undefined =\n\t\tundefined;\n}\n\nexport type AnyPgTable<TPartial extends Partial<TableConfig> = {}> = PgTable<UpdateTableConfig<TableConfig, TPartial>>;\n\nexport type PgTableWithColumns<T extends TableConfig> =\n\t& PgTable<T>\n\t& {\n\t\t[Key in keyof T['columns']]: T['columns'][Key];\n\t}\n\t& {\n\t\tenableRLS: () => Omit<\n\t\t\tPgTableWithColumns<T>,\n\t\t\t'enableRLS'\n\t\t>;\n\t};\n\n/** @internal */\nexport function pgTableWithSchema<\n\tTTableName extends string,\n\tTSchemaName extends string | undefined,\n\tTColumnsMap extends Record<string, PgColumnBuilderBase>,\n>(\n\tname: TTableName,\n\tcolumns: TColumnsMap | ((columnTypes: PgColumnsBuilders) => TColumnsMap),\n\textraConfig:\n\t\t| ((self: BuildExtraConfigColumns<TTableName, TColumnsMap, 'pg'>) => PgTableExtraConfig | PgTableExtraConfigValue[])\n\t\t| undefined,\n\tschema: TSchemaName,\n\tbaseName = name,\n): PgTableWithColumns<{\n\tname: TTableName;\n\tschema: TSchemaName;\n\tcolumns: BuildColumns<TTableName, TColumnsMap, 'pg'>;\n\tdialect: 'pg';\n}> {\n\tconst rawTable = new PgTable<{\n\t\tname: TTableName;\n\t\tschema: TSchemaName;\n\t\tcolumns: BuildColumns<TTableName, TColumnsMap, 'pg'>;\n\t\tdialect: 'pg';\n\t}>(name, schema, baseName);\n\n\tconst parsedColumns: TColumnsMap = typeof columns === 'function' ? columns(getPgColumnBuilders()) : columns;\n\n\tconst builtColumns = Object.fromEntries(\n\t\tObject.entries(parsedColumns).map(([name, colBuilderBase]) => {\n\t\t\tconst colBuilder = colBuilderBase as PgColumnBuilder;\n\t\t\tcolBuilder.setName(name);\n\t\t\tconst column = colBuilder.build(rawTable);\n\t\t\trawTable[InlineForeignKeys].push(...colBuilder.buildForeignKeys(column, rawTable));\n\t\t\treturn [name, column];\n\t\t}),\n\t) as unknown as BuildColumns<TTableName, TColumnsMap, 'pg'>;\n\n\tconst builtColumnsForExtraConfig = Object.fromEntries(\n\t\tObject.entries(parsedColumns).map(([name, colBuilderBase]) => {\n\t\t\tconst colBuilder = colBuilderBase as PgColumnBuilder;\n\t\t\tcolBuilder.setName(name);\n\t\t\tconst column = colBuilder.buildExtraConfigColumn(rawTable);\n\t\t\treturn [name, column];\n\t\t}),\n\t) as unknown as BuildExtraConfigColumns<TTableName, TColumnsMap, 'pg'>;\n\n\tconst table = Object.assign(rawTable, builtColumns);\n\n\ttable[Table.Symbol.Columns] = builtColumns;\n\ttable[Table.Symbol.ExtraConfigColumns] = builtColumnsForExtraConfig;\n\n\tif (extraConfig) {\n\t\ttable[PgTable.Symbol.ExtraConfigBuilder] = extraConfig as any;\n\t}\n\n\treturn Object.assign(table, {\n\t\tenableRLS: () => {\n\t\t\ttable[PgTable.Symbol.EnableRLS] = true;\n\t\t\treturn table as PgTableWithColumns<{\n\t\t\t\tname: TTableName;\n\t\t\t\tschema: TSchemaName;\n\t\t\t\tcolumns: BuildColumns<TTableName, TColumnsMap, 'pg'>;\n\t\t\t\tdialect: 'pg';\n\t\t\t}>;\n\t\t},\n\t});\n}\n\nexport interface PgTableFn<TSchema extends string | undefined = undefined> {\n\t<\n\t\tTTableName extends string,\n\t\tTColumnsMap extends Record<string, PgColumnBuilderBase>,\n\t>(\n\t\tname: TTableName,\n\t\tcolumns: TColumnsMap,\n\t\textraConfig?: (\n\t\t\tself: BuildExtraConfigColumns<TTableName, TColumnsMap, 'pg'>,\n\t\t) => PgTableExtraConfigValue[],\n\t): PgTableWithColumns<{\n\t\tname: TTableName;\n\t\tschema: TSchema;\n\t\tcolumns: BuildColumns<TTableName, TColumnsMap, 'pg'>;\n\t\tdialect: 'pg';\n\t}>;\n\n\t<\n\t\tTTableName extends string,\n\t\tTColumnsMap extends Record<string, PgColumnBuilderBase>,\n\t>(\n\t\tname: TTableName,\n\t\tcolumns: (columnTypes: PgColumnsBuilders) => TColumnsMap,\n\t\textraConfig?: (self: BuildExtraConfigColumns<TTableName, TColumnsMap, 'pg'>) => PgTableExtraConfigValue[],\n\t): PgTableWithColumns<{\n\t\tname: TTableName;\n\t\tschema: TSchema;\n\t\tcolumns: BuildColumns<TTableName, TColumnsMap, 'pg'>;\n\t\tdialect: 'pg';\n\t}>;\n\t/**\n\t * @deprecated The third parameter of pgTable is changing and will only accept an array instead of an object\n\t *\n\t * @example\n\t * Deprecated version:\n\t * ```ts\n\t * export const users = pgTable(\"users\", {\n\t * \tid: integer(),\n\t * }, (t) => ({\n\t * \tidx: index('custom_name').on(t.id)\n\t * }));\n\t * ```\n\t *\n\t * New API:\n\t * ```ts\n\t * export const users = pgTable(\"users\", {\n\t * \tid: integer(),\n\t * }, (t) => [\n\t * \tindex('custom_name').on(t.id)\n\t * ]);\n\t * ```\n\t */\n\t<\n\t\tTTableName extends string,\n\t\tTColumnsMap extends Record<string, PgColumnBuilderBase>,\n\t>(\n\t\tname: TTableName,\n\t\tcolumns: TColumnsMap,\n\t\textraConfig: (\n\t\t\tself: BuildExtraConfigColumns<TTableName, TColumnsMap, 'pg'>,\n\t\t) => PgTableExtraConfig,\n\t): PgTableWithColumns<{\n\t\tname: TTableName;\n\t\tschema: TSchema;\n\t\tcolumns: BuildColumns<TTableName, TColumnsMap, 'pg'>;\n\t\tdialect: 'pg';\n\t}>;\n\n\t/**\n\t * @deprecated The third parameter of pgTable is changing and will only accept an array instead of an object\n\t *\n\t * @example\n\t * Deprecated version:\n\t * ```ts\n\t * export const users = pgTable(\"users\", {\n\t * \tid: integer(),\n\t * }, (t) => ({\n\t * \tidx: index('custom_name').on(t.id)\n\t * }));\n\t * ```\n\t *\n\t * New API:\n\t * ```ts\n\t * export const users = pgTable(\"users\", {\n\t * \tid: integer(),\n\t * }, (t) => [\n\t * \tindex('custom_name').on(t.id)\n\t * ]);\n\t * ```\n\t */\n\t<\n\t\tTTableName extends string,\n\t\tTColumnsMap extends Record<string, PgColumnBuilderBase>,\n\t>(\n\t\tname: TTableName,\n\t\tcolumns: (columnTypes: PgColumnsBuilders) => TColumnsMap,\n\t\textraConfig: (self: BuildExtraConfigColumns<TTableName, TColumnsMap, 'pg'>) => PgTableExtraConfig,\n\t): PgTableWithColumns<{\n\t\tname: TTableName;\n\t\tschema: TSchema;\n\t\tcolumns: BuildColumns<TTableName, TColumnsMap, 'pg'>;\n\t\tdialect: 'pg';\n\t}>;\n}\n\nexport const pgTable: PgTableFn = (name, columns, extraConfig) => {\n\treturn pgTableWithSchema(name, columns, extraConfig, undefined);\n};\n\nexport function pgTableCreator(customizeTableName: (name: string) => string): PgTableFn {\n\treturn (name, columns, extraConfig) => {\n\t\treturn pgTableWithSchema(customizeTableName(name) as typeof name, columns, extraConfig, undefined, name);\n\t};\n}\n", "import { entityKind } from '~/entity.ts';\nimport type { AnyPgColumn, PgColumn } from './columns/index.ts';\nimport { PgTable } from './table.ts';\n\nexport function primaryKey<\n\tTTableName extends string,\n\tTColumn extends AnyPgColumn<{ tableName: TTableName }>,\n\tTColumns extends AnyPgColumn<{ tableName: TTableName }>[],\n>(config: { name?: string; columns: [TColumn, ...TColumns] }): PrimaryKeyBuilder;\n/**\n * @deprecated: Please use primaryKey({ columns: [] }) instead of this function\n * @param columns\n */\nexport function primaryKey<\n\tTTableName extends string,\n\tTColumns extends AnyPgColumn<{ tableName: TTableName }>[],\n>(...columns: TColumns): PrimaryKeyBuilder;\nexport function primaryKey(...config: any) {\n\tif (config[0].columns) {\n\t\treturn new PrimaryKeyBuilder(config[0].columns, config[0].name);\n\t}\n\treturn new PrimaryKeyBuilder(config);\n}\n\nexport class PrimaryKeyBuilder {\n\tstatic readonly [entityKind]: string = 'PgPrimaryKeyBuilder';\n\n\t/** @internal */\n\tcolumns: PgColumn[];\n\n\t/** @internal */\n\tname?: string;\n\n\tconstructor(\n\t\tcolumns: PgColumn[],\n\t\tname?: string,\n\t) {\n\t\tthis.columns = columns;\n\t\tthis.name = name;\n\t}\n\n\t/** @internal */\n\tbuild(table: PgTable): PrimaryKey {\n\t\treturn new PrimaryKey(table, this.columns, this.name);\n\t}\n}\n\nexport class PrimaryKey {\n\tstatic readonly [entityKind]: string = 'PgPrimaryKey';\n\n\treadonly columns: AnyPgColumn<{}>[];\n\treadonly name?: string;\n\n\tconstructor(readonly table: PgTable, columns: AnyPgColumn<{}>[], name?: string) {\n\t\tthis.columns = columns;\n\t\tthis.name = name;\n\t}\n\n\tgetName(): string {\n\t\treturn this.name ?? `${this.table[PgTable.Symbol.Name]}_${this.columns.map((column) => column.name).join('_')}_pk`;\n\t}\n}\n", "import { type AnyTable, getTableUniqueName, type InferModelFromColumns, Table } from '~/table.ts';\nimport { type AnyColumn, Column } from './column.ts';\nimport { entityKind, is } from './entity.ts';\nimport { PrimaryKeyBuilder } from './pg-core/primary-keys.ts';\nimport {\n\tand,\n\tasc,\n\tbetween,\n\tdesc,\n\teq,\n\texists,\n\tgt,\n\tgte,\n\tilike,\n\tinArray,\n\tisNotNull,\n\tisNull,\n\tlike,\n\tlt,\n\tlte,\n\tne,\n\tnot,\n\tnotBetween,\n\tnotExists,\n\tnotIlike,\n\tnotInArray,\n\tnotLike,\n\tor,\n} from './sql/expressions/index.ts';\nimport { type Placeholder, SQL, sql } from './sql/sql.ts';\nimport type { Assume, ColumnsWithTable, Equal, Simplify, ValueOrArray } from './utils.ts';\n\nexport abstract class Relation<TTableName extends string = string> {\n\tstatic readonly [entityKind]: string = 'Relation';\n\n\tdeclare readonly $brand: 'Relation';\n\treadonly referencedTableName: TTableName;\n\tfieldName!: string;\n\n\tconstructor(\n\t\treadonly sourceTable: Table,\n\t\treadonly referencedTable: AnyTable<{ name: TTableName }>,\n\t\treadonly relationName: string | undefined,\n\t) {\n\t\tthis.referencedTableName = referencedTable[Table.Symbol.Name] as TTableName;\n\t}\n\n\tabstract withFieldName(fieldName: string): Relation<TTableName>;\n}\n\nexport class Relations<\n\tTTableName extends string = string,\n\tTConfig extends Record<string, Relation> = Record<string, Relation>,\n> {\n\tstatic readonly [entityKind]: string = 'Relations';\n\n\tdeclare readonly $brand: 'Relations';\n\n\tconstructor(\n\t\treadonly table: AnyTable<{ name: TTableName }>,\n\t\treadonly config: (helpers: TableRelationsHelpers<TTableName>) => TConfig,\n\t) {}\n}\n\nexport class One<\n\tTTableName extends string = string,\n\tTIsNullable extends boolean = boolean,\n> extends Relation<TTableName> {\n\tstatic override readonly [entityKind]: string = 'One';\n\n\tdeclare protected $relationBrand: 'One';\n\n\tconstructor(\n\t\tsourceTable: Table,\n\t\treferencedTable: AnyTable<{ name: TTableName }>,\n\t\treadonly config:\n\t\t\t| RelationConfig<\n\t\t\t\tTTableName,\n\t\t\t\tstring,\n\t\t\t\tAnyColumn<{ tableName: TTableName }>[]\n\t\t\t>\n\t\t\t| undefined,\n\t\treadonly isNullable: TIsNullable,\n\t) {\n\t\tsuper(sourceTable, referencedTable, config?.relationName);\n\t}\n\n\twithFieldName(fieldName: string): One<TTableName> {\n\t\tconst relation = new One(\n\t\t\tthis.sourceTable,\n\t\t\tthis.referencedTable,\n\t\t\tthis.config,\n\t\t\tthis.isNullable,\n\t\t);\n\t\trelation.fieldName = fieldName;\n\t\treturn relation;\n\t}\n}\n\nexport class Many<TTableName extends string> extends Relation<TTableName> {\n\tstatic override readonly [entityKind]: string = 'Many';\n\n\tdeclare protected $relationBrand: 'Many';\n\n\tconstructor(\n\t\tsourceTable: Table,\n\t\treferencedTable: AnyTable<{ name: TTableName }>,\n\t\treadonly config: { relationName: string } | undefined,\n\t) {\n\t\tsuper(sourceTable, referencedTable, config?.relationName);\n\t}\n\n\twithFieldName(fieldName: string): Many<TTableName> {\n\t\tconst relation = new Many(\n\t\t\tthis.sourceTable,\n\t\t\tthis.referencedTable,\n\t\t\tthis.config,\n\t\t);\n\t\trelation.fieldName = fieldName;\n\t\treturn relation;\n\t}\n}\n\nexport type TableRelationsKeysOnly<\n\tTSchema extends Record<string, unknown>,\n\tTTableName extends string,\n\tK extends keyof TSchema,\n> = TSchema[K] extends Relations<TTableName> ? K : never;\n\nexport type ExtractTableRelationsFromSchema<\n\tTSchema extends Record<string, unknown>,\n\tTTableName extends string,\n> = ExtractObjectValues<\n\t{\n\t\t[\n\t\t\tK in keyof TSchema as TableRelationsKeysOnly<\n\t\t\t\tTSchema,\n\t\t\t\tTTableName,\n\t\t\t\tK\n\t\t\t>\n\t\t]: TSchema[K] extends Relations<TTableName, infer TConfig> ? TConfig : never;\n\t}\n>;\n\nexport type ExtractObjectValues<T> = T[keyof T];\n\nexport type ExtractRelationsFromTableExtraConfigSchema<\n\tTConfig extends unknown[],\n> = ExtractObjectValues<\n\t{\n\t\t[\n\t\t\tK in keyof TConfig as TConfig[K] extends Relations<any> ? K\n\t\t\t\t: never\n\t\t]: TConfig[K] extends Relations<infer TRelationConfig> ? TRelationConfig\n\t\t\t: never;\n\t}\n>;\n\nexport function getOperators() {\n\treturn {\n\t\tand,\n\t\tbetween,\n\t\teq,\n\t\texists,\n\t\tgt,\n\t\tgte,\n\t\tilike,\n\t\tinArray,\n\t\tisNull,\n\t\tisNotNull,\n\t\tlike,\n\t\tlt,\n\t\tlte,\n\t\tne,\n\t\tnot,\n\t\tnotBetween,\n\t\tnotExists,\n\t\tnotLike,\n\t\tnotIlike,\n\t\tnotInArray,\n\t\tor,\n\t\tsql,\n\t};\n}\n\nexport type Operators = ReturnType<typeof getOperators>;\n\nexport function getOrderByOperators() {\n\treturn {\n\t\tsql,\n\t\tasc,\n\t\tdesc,\n\t};\n}\n\nexport type OrderByOperators = ReturnType<typeof getOrderByOperators>;\n\nexport type FindTableByDBName<\n\tTSchema extends TablesRelationalConfig,\n\tTTableName extends string,\n> = ExtractObjectValues<\n\t{\n\t\t[\n\t\t\tK in keyof TSchema as TSchema[K]['dbName'] extends TTableName ? K\n\t\t\t\t: never\n\t\t]: TSchema[K];\n\t}\n>;\n\nexport type DBQueryConfig<\n\tTRelationType extends 'one' | 'many' = 'one' | 'many',\n\tTIsRoot extends boolean = boolean,\n\tTSchema extends TablesRelationalConfig = TablesRelationalConfig,\n\tTTableConfig extends TableRelationalConfig = TableRelationalConfig,\n> =\n\t& {\n\t\tcolumns?:\n\t\t\t| {\n\t\t\t\t[K in keyof TTableConfig['columns']]?: boolean;\n\t\t\t}\n\t\t\t| undefined;\n\t\twith?:\n\t\t\t| {\n\t\t\t\t[K in keyof TTableConfig['relations']]?:\n\t\t\t\t\t| true\n\t\t\t\t\t| DBQueryConfig<\n\t\t\t\t\t\tTTableConfig['relations'][K] extends One ? 'one' : 'many',\n\t\t\t\t\t\tfalse,\n\t\t\t\t\t\tTSchema,\n\t\t\t\t\t\tFindTableByDBName<\n\t\t\t\t\t\t\tTSchema,\n\t\t\t\t\t\t\tTTableConfig['relations'][K]['referencedTableName']\n\t\t\t\t\t\t>\n\t\t\t\t\t>\n\t\t\t\t\t| undefined;\n\t\t\t}\n\t\t\t| undefined;\n\t\textras?:\n\t\t\t| Record<string, SQL.Aliased>\n\t\t\t| ((\n\t\t\t\tfields: Simplify<\n\t\t\t\t\t[TTableConfig['columns']] extends [never] ? {}\n\t\t\t\t\t\t: TTableConfig['columns']\n\t\t\t\t>,\n\t\t\t\toperators: { sql: Operators['sql'] },\n\t\t\t) => Record<string, SQL.Aliased>)\n\t\t\t| undefined;\n\t}\n\t& (TRelationType extends 'many' ?\n\t\t\t& {\n\t\t\t\twhere?:\n\t\t\t\t\t| SQL\n\t\t\t\t\t| undefined\n\t\t\t\t\t| ((\n\t\t\t\t\t\tfields: Simplify<\n\t\t\t\t\t\t\t[TTableConfig['columns']] extends [never] ? {}\n\t\t\t\t\t\t\t\t: TTableConfig['columns']\n\t\t\t\t\t\t>,\n\t\t\t\t\t\toperators: Operators,\n\t\t\t\t\t) => SQL | undefined);\n\t\t\t\torderBy?:\n\t\t\t\t\t| ValueOrArray<AnyColumn | SQL>\n\t\t\t\t\t| ((\n\t\t\t\t\t\tfields: Simplify<\n\t\t\t\t\t\t\t[TTableConfig['columns']] extends [never] ? {}\n\t\t\t\t\t\t\t\t: TTableConfig['columns']\n\t\t\t\t\t\t>,\n\t\t\t\t\t\toperators: OrderByOperators,\n\t\t\t\t\t) => ValueOrArray<AnyColumn | SQL>)\n\t\t\t\t\t| undefined;\n\t\t\t\tlimit?: number | Placeholder | undefined;\n\t\t\t}\n\t\t\t& (TIsRoot extends true ? {\n\t\t\t\t\toffset?: number | Placeholder | undefined;\n\t\t\t\t}\n\t\t\t\t: {})\n\t\t: {});\n\nexport interface TableRelationalConfig {\n\ttsName: string;\n\tdbName: string;\n\tcolumns: Record<string, Column>;\n\trelations: Record<string, Relation>;\n\tprimaryKey: AnyColumn[];\n\tschema?: string;\n}\n\nexport type TablesRelationalConfig = Record<string, TableRelationalConfig>;\n\nexport interface RelationalSchemaConfig<\n\tTSchema extends TablesRelationalConfig,\n> {\n\tfullSchema: Record<string, unknown>;\n\tschema: TSchema;\n\ttableNamesMap: Record<string, string>;\n}\n\nexport type ExtractTablesWithRelations<\n\tTSchema extends Record<string, unknown>,\n> = {\n\t[\n\t\tK in keyof TSchema as TSchema[K] extends Table ? K\n\t\t\t: never\n\t]: TSchema[K] extends Table ? {\n\t\t\ttsName: K & string;\n\t\t\tdbName: TSchema[K]['_']['name'];\n\t\t\tcolumns: TSchema[K]['_']['columns'];\n\t\t\trelations: ExtractTableRelationsFromSchema<\n\t\t\t\tTSchema,\n\t\t\t\tTSchema[K]['_']['name']\n\t\t\t>;\n\t\t\tprimaryKey: AnyColumn[];\n\t\t}\n\t\t: never;\n};\n\nexport type ReturnTypeOrValue<T> = T extends (...args: any[]) => infer R ? R\n\t: T;\n\nexport type BuildRelationResult<\n\tTSchema extends TablesRelationalConfig,\n\tTInclude,\n\tTRelations extends Record<string, Relation>,\n> = {\n\t[\n\t\tK in\n\t\t\t& NonUndefinedKeysOnly<TInclude>\n\t\t\t& keyof TRelations\n\t]: TRelations[K] extends infer TRel extends Relation ? BuildQueryResult<\n\t\t\tTSchema,\n\t\t\tFindTableByDBName<TSchema, TRel['referencedTableName']>,\n\t\t\tAssume<TInclude[K], true | Record<string, unknown>>\n\t\t> extends infer TResult ? TRel extends One ?\n\t\t\t\t\t| TResult\n\t\t\t\t\t| (Equal<TRel['isNullable'], false> extends true ? null : never)\n\t\t\t: TResult[]\n\t\t: never\n\t\t: never;\n};\n\nexport type NonUndefinedKeysOnly<T> =\n\t& ExtractObjectValues<\n\t\t{\n\t\t\t[K in keyof T as T[K] extends undefined ? never : K]: K;\n\t\t}\n\t>\n\t& keyof T;\n\nexport type BuildQueryResult<\n\tTSchema extends TablesRelationalConfig,\n\tTTableConfig extends TableRelationalConfig,\n\tTFullSelection extends true | Record<string, unknown>,\n> = Equal<TFullSelection, true> extends true ? InferModelFromColumns<TTableConfig['columns']>\n\t: TFullSelection extends Record<string, unknown> ? Simplify<\n\t\t\t& (TFullSelection['columns'] extends Record<string, unknown> ? InferModelFromColumns<\n\t\t\t\t\t{\n\t\t\t\t\t\t[\n\t\t\t\t\t\t\tK in Equal<\n\t\t\t\t\t\t\t\tExclude<\n\t\t\t\t\t\t\t\t\tTFullSelection['columns'][\n\t\t\t\t\t\t\t\t\t\t& keyof TFullSelection['columns']\n\t\t\t\t\t\t\t\t\t\t& keyof TTableConfig['columns']\n\t\t\t\t\t\t\t\t\t],\n\t\t\t\t\t\t\t\t\tundefined\n\t\t\t\t\t\t\t\t>,\n\t\t\t\t\t\t\t\tfalse\n\t\t\t\t\t\t\t> extends true ? Exclude<\n\t\t\t\t\t\t\t\t\tkeyof TTableConfig['columns'],\n\t\t\t\t\t\t\t\t\tNonUndefinedKeysOnly<TFullSelection['columns']>\n\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t:\n\t\t\t\t\t\t\t\t\t& {\n\t\t\t\t\t\t\t\t\t\t[K in keyof TFullSelection['columns']]: Equal<\n\t\t\t\t\t\t\t\t\t\t\tTFullSelection['columns'][K],\n\t\t\t\t\t\t\t\t\t\t\ttrue\n\t\t\t\t\t\t\t\t\t\t> extends true ? K\n\t\t\t\t\t\t\t\t\t\t\t: never;\n\t\t\t\t\t\t\t\t\t}[keyof TFullSelection['columns']]\n\t\t\t\t\t\t\t\t\t& keyof TTableConfig['columns']\n\t\t\t\t\t\t]: TTableConfig['columns'][K];\n\t\t\t\t\t}\n\t\t\t\t>\n\t\t\t\t: InferModelFromColumns<TTableConfig['columns']>)\n\t\t\t& (TFullSelection['extras'] extends\n\t\t\t\t| Record<string, unknown>\n\t\t\t\t| ((...args: any[]) => Record<string, unknown>) ? {\n\t\t\t\t\t[\n\t\t\t\t\t\tK in NonUndefinedKeysOnly<\n\t\t\t\t\t\t\tReturnTypeOrValue<TFullSelection['extras']>\n\t\t\t\t\t\t>\n\t\t\t\t\t]: Assume<\n\t\t\t\t\t\tReturnTypeOrValue<TFullSelection['extras']>[K],\n\t\t\t\t\t\tSQL.Aliased\n\t\t\t\t\t>['_']['type'];\n\t\t\t\t}\n\t\t\t\t: {})\n\t\t\t& (TFullSelection['with'] extends Record<string, unknown> ? BuildRelationResult<\n\t\t\t\t\tTSchema,\n\t\t\t\t\tTFullSelection['with'],\n\t\t\t\t\tTTableConfig['relations']\n\t\t\t\t>\n\t\t\t\t: {})\n\t\t>\n\t: never;\n\nexport interface RelationConfig<\n\tTTableName extends string,\n\tTForeignTableName extends string,\n\tTColumns extends AnyColumn<{ tableName: TTableName }>[],\n> {\n\trelationName?: string;\n\tfields: TColumns;\n\treferences: ColumnsWithTable<TTableName, TForeignTableName, TColumns>;\n}\n\nexport function extractTablesRelationalConfig<\n\tTTables extends TablesRelationalConfig,\n>(\n\tschema: Record<string, unknown>,\n\tconfigHelpers: (table: Table) => any,\n): { tables: TTables; tableNamesMap: Record<string, string> } {\n\tif (\n\t\tObject.keys(schema).length === 1\n\t\t&& 'default' in schema\n\t\t&& !is(schema['default'], Table)\n\t) {\n\t\tschema = schema['default'] as Record<string, unknown>;\n\t}\n\n\t// table DB name -> schema table key\n\tconst tableNamesMap: Record<string, string> = {};\n\t// Table relations found before their tables - need to buffer them until we know the schema table key\n\tconst relationsBuffer: Record<\n\t\tstring,\n\t\t{ relations: Record<string, Relation>; primaryKey?: AnyColumn[] }\n\t> = {};\n\tconst tablesConfig: TablesRelationalConfig = {};\n\tfor (const [key, value] of Object.entries(schema)) {\n\t\tif (is(value, Table)) {\n\t\t\tconst dbName = getTableUniqueName(value);\n\t\t\tconst bufferedRelations = relationsBuffer[dbName];\n\t\t\ttableNamesMap[dbName] = key;\n\t\t\ttablesConfig[key] = {\n\t\t\t\ttsName: key,\n\t\t\t\tdbName: value[Table.Symbol.Name],\n\t\t\t\tschema: value[Table.Symbol.Schema],\n\t\t\t\tcolumns: value[Table.Symbol.Columns],\n\t\t\t\trelations: bufferedRelations?.relations ?? {},\n\t\t\t\tprimaryKey: bufferedRelations?.primaryKey ?? [],\n\t\t\t};\n\n\t\t\t// Fill in primary keys\n\t\t\tfor (\n\t\t\t\tconst column of Object.values(\n\t\t\t\t\t(value as Table)[Table.Symbol.Columns],\n\t\t\t\t)\n\t\t\t) {\n\t\t\t\tif (column.primary) {\n\t\t\t\t\ttablesConfig[key]!.primaryKey.push(column);\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tconst extraConfig = value[Table.Symbol.ExtraConfigBuilder]?.((value as Table)[Table.Symbol.ExtraConfigColumns]);\n\t\t\tif (extraConfig) {\n\t\t\t\tfor (const configEntry of Object.values(extraConfig)) {\n\t\t\t\t\tif (is(configEntry, PrimaryKeyBuilder)) {\n\t\t\t\t\t\ttablesConfig[key]!.primaryKey.push(...configEntry.columns);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t} else if (is(value, Relations)) {\n\t\t\tconst dbName = getTableUniqueName(value.table);\n\t\t\tconst tableName = tableNamesMap[dbName];\n\t\t\tconst relations: Record<string, Relation> = value.config(\n\t\t\t\tconfigHelpers(value.table),\n\t\t\t);\n\t\t\tlet primaryKey: AnyColumn[] | undefined;\n\n\t\t\tfor (const [relationName, relation] of Object.entries(relations)) {\n\t\t\t\tif (tableName) {\n\t\t\t\t\tconst tableConfig = tablesConfig[tableName]!;\n\t\t\t\t\ttableConfig.relations[relationName] = relation;\n\t\t\t\t\tif (primaryKey) {\n\t\t\t\t\t\ttableConfig.primaryKey.push(...primaryKey);\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\tif (!(dbName in relationsBuffer)) {\n\t\t\t\t\t\trelationsBuffer[dbName] = {\n\t\t\t\t\t\t\trelations: {},\n\t\t\t\t\t\t\tprimaryKey,\n\t\t\t\t\t\t};\n\t\t\t\t\t}\n\t\t\t\t\trelationsBuffer[dbName]!.relations[relationName] = relation;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\treturn { tables: tablesConfig as TTables, tableNamesMap };\n}\n\nexport function relations<\n\tTTableName extends string,\n\tTRelations extends Record<string, Relation<any>>,\n>(\n\ttable: AnyTable<{ name: TTableName }>,\n\trelations: (helpers: TableRelationsHelpers<TTableName>) => TRelations,\n): Relations<TTableName, TRelations> {\n\treturn new Relations<TTableName, TRelations>(\n\t\ttable,\n\t\t(helpers: TableRelationsHelpers<TTableName>) =>\n\t\t\tObject.fromEntries(\n\t\t\t\tObject.entries(relations(helpers)).map(([key, value]) => [\n\t\t\t\t\tkey,\n\t\t\t\t\tvalue.withFieldName(key),\n\t\t\t\t]),\n\t\t\t) as TRelations,\n\t);\n}\n\nexport function createOne<TTableName extends string>(sourceTable: Table) {\n\treturn function one<\n\t\tTForeignTable extends Table,\n\t\tTColumns extends [\n\t\t\tAnyColumn<{ tableName: TTableName }>,\n\t\t\t...AnyColumn<{ tableName: TTableName }>[],\n\t\t],\n\t>(\n\t\ttable: TForeignTable,\n\t\tconfig?: RelationConfig<TTableName, TForeignTable['_']['name'], TColumns>,\n\t): One<\n\t\tTForeignTable['_']['name'],\n\t\tEqual<TColumns[number]['_']['notNull'], true>\n\t> {\n\t\treturn new One(\n\t\t\tsourceTable,\n\t\t\ttable,\n\t\t\tconfig,\n\t\t\t(config?.fields.reduce<boolean>((res, f) => res && f.notNull, true)\n\t\t\t\t?? false) as Equal<TColumns[number]['_']['notNull'], true>,\n\t\t);\n\t};\n}\n\nexport function createMany(sourceTable: Table) {\n\treturn function many<TForeignTable extends Table>(\n\t\treferencedTable: TForeignTable,\n\t\tconfig?: { relationName: string },\n\t): Many<TForeignTable['_']['name']> {\n\t\treturn new Many(sourceTable, referencedTable, config);\n\t};\n}\n\nexport interface NormalizedRelation {\n\tfields: AnyColumn[];\n\treferences: AnyColumn[];\n}\n\nexport function normalizeRelation(\n\tschema: TablesRelationalConfig,\n\ttableNamesMap: Record<string, string>,\n\trelation: Relation,\n): NormalizedRelation {\n\tif (is(relation, One) && relation.config) {\n\t\treturn {\n\t\t\tfields: relation.config.fields,\n\t\t\treferences: relation.config.references,\n\t\t};\n\t}\n\n\tconst referencedTableTsName = tableNamesMap[getTableUniqueName(relation.referencedTable)];\n\tif (!referencedTableTsName) {\n\t\tthrow new Error(\n\t\t\t`Table \"${relation.referencedTable[Table.Symbol.Name]}\" not found in schema`,\n\t\t);\n\t}\n\n\tconst referencedTableConfig = schema[referencedTableTsName];\n\tif (!referencedTableConfig) {\n\t\tthrow new Error(`Table \"${referencedTableTsName}\" not found in schema`);\n\t}\n\n\tconst sourceTable = relation.sourceTable;\n\tconst sourceTableTsName = tableNamesMap[getTableUniqueName(sourceTable)];\n\tif (!sourceTableTsName) {\n\t\tthrow new Error(\n\t\t\t`Table \"${sourceTable[Table.Symbol.Name]}\" not found in schema`,\n\t\t);\n\t}\n\n\tconst reverseRelations: Relation[] = [];\n\tfor (\n\t\tconst referencedTableRelation of Object.values(\n\t\t\treferencedTableConfig.relations,\n\t\t)\n\t) {\n\t\tif (\n\t\t\t(relation.relationName\n\t\t\t\t&& relation !== referencedTableRelation\n\t\t\t\t&& referencedTableRelation.relationName === relation.relationName)\n\t\t\t|| (!relation.relationName\n\t\t\t\t&& referencedTableRelation.referencedTable === relation.sourceTable)\n\t\t) {\n\t\t\treverseRelations.push(referencedTableRelation);\n\t\t}\n\t}\n\n\tif (reverseRelations.length > 1) {\n\t\tthrow relation.relationName\n\t\t\t? new Error(\n\t\t\t\t`There are multiple relations with name \"${relation.relationName}\" in table \"${referencedTableTsName}\"`,\n\t\t\t)\n\t\t\t: new Error(\n\t\t\t\t`There are multiple relations between \"${referencedTableTsName}\" and \"${\n\t\t\t\t\trelation.sourceTable[Table.Symbol.Name]\n\t\t\t\t}\". Please specify relation name`,\n\t\t\t);\n\t}\n\n\tif (\n\t\treverseRelations[0]\n\t\t&& is(reverseRelations[0], One)\n\t\t&& reverseRelations[0].config\n\t) {\n\t\treturn {\n\t\t\tfields: reverseRelations[0].config.references,\n\t\t\treferences: reverseRelations[0].config.fields,\n\t\t};\n\t}\n\n\tthrow new Error(\n\t\t`There is not enough information to infer relation \"${sourceTableTsName}.${relation.fieldName}\"`,\n\t);\n}\n\nexport function createTableRelationsHelpers<TTableName extends string>(\n\tsourceTable: AnyTable<{ name: TTableName }>,\n) {\n\treturn {\n\t\tone: createOne<TTableName>(sourceTable),\n\t\tmany: createMany(sourceTable),\n\t};\n}\n\nexport type TableRelationsHelpers<TTableName extends string> = ReturnType<\n\ttypeof createTableRelationsHelpers<TTableName>\n>;\n\nexport interface BuildRelationalQueryResult<\n\tTTable extends Table = Table,\n\tTColumn extends Column = Column,\n> {\n\ttableTsKey: string;\n\tselection: {\n\t\tdbKey: string;\n\t\ttsKey: string;\n\t\tfield: TColumn | SQL | SQL.Aliased;\n\t\trelationTableTsKey: string | undefined;\n\t\tisJson: boolean;\n\t\tisExtra?: boolean;\n\t\tselection: BuildRelationalQueryResult<TTable>['selection'];\n\t}[];\n\tsql: TTable | SQL;\n}\n\nexport function mapRelationalRow(\n\ttablesConfig: TablesRelationalConfig,\n\ttableConfig: TableRelationalConfig,\n\trow: unknown[],\n\tbuildQueryResultSelection: BuildRelationalQueryResult['selection'],\n\tmapColumnValue: (value: unknown) => unknown = (value) => value,\n): Record<string, unknown> {\n\tconst result: Record<string, unknown> = {};\n\n\tfor (\n\t\tconst [\n\t\t\tselectionItemIndex,\n\t\t\tselectionItem,\n\t\t] of buildQueryResultSelection.entries()\n\t) {\n\t\tif (selectionItem.isJson) {\n\t\t\tconst relation = tableConfig.relations[selectionItem.tsKey]!;\n\t\t\tconst rawSubRows = row[selectionItemIndex] as\n\t\t\t\t| unknown[]\n\t\t\t\t| null\n\t\t\t\t| [null]\n\t\t\t\t| string;\n\t\t\tconst subRows = typeof rawSubRows === 'string'\n\t\t\t\t? (JSON.parse(rawSubRows) as unknown[])\n\t\t\t\t: rawSubRows;\n\t\t\tresult[selectionItem.tsKey] = is(relation, One)\n\t\t\t\t? subRows\n\t\t\t\t\t&& mapRelationalRow(\n\t\t\t\t\t\ttablesConfig,\n\t\t\t\t\t\ttablesConfig[selectionItem.relationTableTsKey!]!,\n\t\t\t\t\t\tsubRows,\n\t\t\t\t\t\tselectionItem.selection,\n\t\t\t\t\t\tmapColumnValue,\n\t\t\t\t\t)\n\t\t\t\t: (subRows as unknown[][]).map((subRow) =>\n\t\t\t\t\tmapRelationalRow(\n\t\t\t\t\t\ttablesConfig,\n\t\t\t\t\t\ttablesConfig[selectionItem.relationTableTsKey!]!,\n\t\t\t\t\t\tsubRow,\n\t\t\t\t\t\tselectionItem.selection,\n\t\t\t\t\t\tmapColumnValue,\n\t\t\t\t\t)\n\t\t\t\t);\n\t\t} else {\n\t\t\tconst value = mapColumnValue(row[selectionItemIndex]);\n\t\t\tconst field = selectionItem.field!;\n\t\t\tlet decoder;\n\t\t\tif (is(field, Column)) {\n\t\t\t\tdecoder = field;\n\t\t\t} else if (is(field, SQL)) {\n\t\t\t\tdecoder = field.decoder;\n\t\t\t} else {\n\t\t\t\tdecoder = field.sql.decoder;\n\t\t\t}\n\t\t\tresult[selectionItem.tsKey] = value === null ? null : decoder.mapFromDriverValue(value);\n\t\t}\n\t}\n\n\treturn result;\n}\n", "import { type AnyColumn, Column } from '~/column.ts';\nimport { is } from '~/entity.ts';\nimport { type SQL, sql, type SQLWrapper } from '../sql.ts';\n\n/**\n * Returns the number of values in `expression`.\n *\n * ## Examples\n *\n * ```ts\n * // Number employees with null values\n * db.select({ value: count() }).from(employees)\n * // Number of employees where `name` is not null\n * db.select({ value: count(employees.name) }).from(employees)\n * ```\n *\n * @see countDistinct to get the number of non-duplicate values in `expression`\n */\nexport function count(expression?: SQLWrapper): SQL<number> {\n\treturn sql`count(${expression || sql.raw('*')})`.mapWith(Number);\n}\n\n/**\n * Returns the number of non-duplicate values in `expression`.\n *\n * ## Examples\n *\n * ```ts\n * // Number of employees where `name` is distinct\n * db.select({ value: countDistinct(employees.name) }).from(employees)\n * ```\n *\n * @see count to get the number of values in `expression`, including duplicates\n */\nexport function countDistinct(expression: SQLWrapper): SQL<number> {\n\treturn sql`count(distinct ${expression})`.mapWith(Number);\n}\n\n/**\n * Returns the average (arithmetic mean) of all non-null values in `expression`.\n *\n * ## Examples\n *\n * ```ts\n * // Average salary of an employee\n * db.select({ value: avg(employees.salary) }).from(employees)\n * ```\n *\n * @see avgDistinct to get the average of all non-null and non-duplicate values in `expression`\n */\nexport function avg(expression: SQLWrapper): SQL<string | null> {\n\treturn sql`avg(${expression})`.mapWith(String);\n}\n\n/**\n * Returns the average (arithmetic mean) of all non-null and non-duplicate values in `expression`.\n *\n * ## Examples\n *\n * ```ts\n * // Average salary of an employee where `salary` is distinct\n * db.select({ value: avgDistinct(employees.salary) }).from(employees)\n * ```\n *\n * @see avg to get the average of all non-null values in `expression`, including duplicates\n */\nexport function avgDistinct(expression: SQLWrapper): SQL<string | null> {\n\treturn sql`avg(distinct ${expression})`.mapWith(String);\n}\n\n/**\n * Returns the sum of all non-null values in `expression`.\n *\n * ## Examples\n *\n * ```ts\n * // Sum of every employee's salary\n * db.select({ value: sum(employees.salary) }).from(employees)\n * ```\n *\n * @see sumDistinct to get the sum of all non-null and non-duplicate values in `expression`\n */\nexport function sum(expression: SQLWrapper): SQL<string | null> {\n\treturn sql`sum(${expression})`.mapWith(String);\n}\n\n/**\n * Returns the sum of all non-null and non-duplicate values in `expression`.\n *\n * ## Examples\n *\n * ```ts\n * // Sum of every employee's salary where `salary` is distinct (no duplicates)\n * db.select({ value: sumDistinct(employees.salary) }).from(employees)\n * ```\n *\n * @see sum to get the sum of all non-null values in `expression`, including duplicates\n */\nexport function sumDistinct(expression: SQLWrapper): SQL<string | null> {\n\treturn sql`sum(distinct ${expression})`.mapWith(String);\n}\n\n/**\n * Returns the maximum value in `expression`.\n *\n * ## Examples\n *\n * ```ts\n * // The employee with the highest salary\n * db.select({ value: max(employees.salary) }).from(employees)\n * ```\n */\nexport function max<T extends SQLWrapper>(expression: T): SQL<(T extends AnyColumn ? T['_']['data'] : string) | null> {\n\treturn sql`max(${expression})`.mapWith(is(expression, Column) ? expression : String) as any;\n}\n\n/**\n * Returns the minimum value in `expression`.\n *\n * ## Examples\n *\n * ```ts\n * // The employee with the lowest salary\n * db.select({ value: min(employees.salary) }).from(employees)\n * ```\n */\nexport function min<T extends SQLWrapper>(expression: T): SQL<(T extends AnyColumn ? T['_']['data'] : string) | null> {\n\treturn sql`min(${expression})`.mapWith(is(expression, Column) ? expression : String) as any;\n}\n", "import type { AnyColumn } from '~/column.ts';\nimport type { TypedQueryBuilder } from '~/query-builders/query-builder.ts';\nimport { type SQL, sql, type SQLWrapper } from '../sql.ts';\n\nfunction toSql(value: number[] | string[]): string {\n\treturn JSON.stringify(value);\n}\n\n/**\n * Used in sorting and in querying, if used in sorting,\n * this specifies that the given column or expression should be sorted in an order\n * that minimizes the L2 distance to the given value.\n * If used in querying, this specifies that it should return the L2 distance\n * between the given column or expression and the given value.\n *\n * ## Examples\n *\n * ```ts\n * // Sort cars by embedding similarity\n * // to the given embedding\n * db.select().from(cars)\n *   .orderBy(l2Distance(cars.embedding, embedding));\n * ```\n *\n * ```ts\n * // Select distance of cars and embedding\n * // to the given embedding\n * db.select({distance: l2Distance(cars.embedding, embedding)}).from(cars)\n * ```\n */\nexport function l2Distance(\n\tcolumn: SQLWrapper | AnyColumn,\n\tvalue: number[] | string[] | TypedQueryBuilder<any> | string,\n): SQL {\n\tif (Array.isArray(value)) {\n\t\treturn sql`${column} <-> ${toSql(value)}`;\n\t}\n\treturn sql`${column} <-> ${value}`;\n}\n\n/**\n * L1 distance is one of the possible distance measures between two probability distribution vectors and it is\n * calculated as the sum of the absolute differences.\n * The smaller the distance between the observed probability vectors, the higher the accuracy of the synthetic data\n *\n * ## Examples\n *\n * ```ts\n * // Sort cars by embedding similarity\n * // to the given embedding\n * db.select().from(cars)\n *   .orderBy(l1Distance(cars.embedding, embedding));\n * ```\n *\n * ```ts\n * // Select distance of cars and embedding\n * // to the given embedding\n * db.select({distance: l1Distance(cars.embedding, embedding)}).from(cars)\n * ```\n */\nexport function l1Distance(\n\tcolumn: SQLWrapper | AnyColumn,\n\tvalue: number[] | string[] | TypedQueryBuilder<any> | string,\n): SQL {\n\tif (Array.isArray(value)) {\n\t\treturn sql`${column} <+> ${toSql(value)}`;\n\t}\n\treturn sql`${column} <+> ${value}`;\n}\n\n/**\n * Used in sorting and in querying, if used in sorting,\n * this specifies that the given column or expression should be sorted in an order\n * that minimizes the inner product distance to the given value.\n * If used in querying, this specifies that it should return the inner product distance\n * between the given column or expression and the given value.\n *\n * ## Examples\n *\n * ```ts\n * // Sort cars by embedding similarity\n * // to the given embedding\n * db.select().from(cars)\n *   .orderBy(innerProduct(cars.embedding, embedding));\n * ```\n *\n * ```ts\n * // Select distance of cars and embedding\n * // to the given embedding\n * db.select({ distance: innerProduct(cars.embedding, embedding) }).from(cars)\n * ```\n */\nexport function innerProduct(\n\tcolumn: SQLWrapper | AnyColumn,\n\tvalue: number[] | string[] | TypedQueryBuilder<any> | string,\n): SQL {\n\tif (Array.isArray(value)) {\n\t\treturn sql`${column} <#> ${toSql(value)}`;\n\t}\n\treturn sql`${column} <#> ${value}`;\n}\n\n/**\n * Used in sorting and in querying, if used in sorting,\n * this specifies that the given column or expression should be sorted in an order\n * that minimizes the cosine distance to the given value.\n * If used in querying, this specifies that it should return the cosine distance\n * between the given column or expression and the given value.\n *\n * ## Examples\n *\n * ```ts\n * // Sort cars by embedding similarity\n * // to the given embedding\n * db.select().from(cars)\n *   .orderBy(cosineDistance(cars.embedding, embedding));\n * ```\n *\n * ```ts\n * // Select distance of cars and embedding\n * // to the given embedding\n * db.select({distance: cosineDistance(cars.embedding, embedding)}).from(cars)\n * ```\n */\nexport function cosineDistance(\n\tcolumn: SQLWrapper | AnyColumn,\n\tvalue: number[] | string[] | TypedQueryBuilder<any> | string,\n): SQL {\n\tif (Array.isArray(value)) {\n\t\treturn sql`${column} <=> ${toSql(value)}`;\n\t}\n\treturn sql`${column} <=> ${value}`;\n}\n\n/**\n * Hamming distance between two strings or vectors of equal length is the number of positions at which the\n * corresponding symbols are different. In other words, it measures the minimum number of\n * substitutions required to change one string into the other, or equivalently,\n * the minimum number of errors that could have transformed one string into the other\n *\n * ## Examples\n *\n * ```ts\n * // Sort cars by embedding similarity\n * // to the given embedding\n * db.select().from(cars)\n *   .orderBy(hammingDistance(cars.embedding, embedding));\n * ```\n */\nexport function hammingDistance(\n\tcolumn: SQLWrapper | AnyColumn,\n\tvalue: number[] | string[] | TypedQueryBuilder<any> | string,\n): SQL {\n\tif (Array.isArray(value)) {\n\t\treturn sql`${column} <~> ${toSql(value)}`;\n\t}\n\treturn sql`${column} <~> ${value}`;\n}\n\n/**\n * ## Examples\n *\n * ```ts\n * // Sort cars by embedding similarity\n * // to the given embedding\n * db.select().from(cars)\n *   .orderBy(jaccardDistance(cars.embedding, embedding));\n * ```\n */\nexport function jaccardDistance(\n\tcolumn: SQLWrapper | AnyColumn,\n\tvalue: number[] | string[] | TypedQueryBuilder<any> | string,\n): SQL {\n\tif (Array.isArray(value)) {\n\t\treturn sql`${column} <%> ${toSql(value)}`;\n\t}\n\treturn sql`${column} <%> ${value}`;\n}\n"], "mappings": ";;;;;AAAO,IAAM,aAAa,OAAO,IAAI,oBAAoB;AAClD,IAAM,mBAAmB,OAAO,IAAI,0BAA0B;AAU9D,SAAS,GAAsC,OAAY,MAAmC;AACpG,MAAI,CAAC,SAAS,OAAO,UAAU,UAAU;AACxC,WAAO;EACR;AAEA,MAAI,iBAAiB,MAAM;AAC1B,WAAO;EACR;AAEA,MAAI,CAAC,OAAO,UAAU,eAAe,KAAK,MAAM,UAAU,GAAG;AAC5D,UAAM,IAAI;MACT,UACC,KAAK,QAAQ,WACd;IACD;EACD;AAEA,MAAI,MAAM,OAAO,eAAe,KAAK,EAAE;AACvC,MAAI,KAAK;AAER,WAAO,KAAK;AACX,UAAI,cAAc,OAAO,IAAI,UAAU,MAAM,KAAK,UAAU,GAAG;AAC9D,eAAO;MACR;AAEA,YAAM,OAAO,eAAe,GAAG;IAChC;EACD;AAEA,SAAO;AACR;;;AClCA;AA4DkB;AALX,IAAe,SAAf,MAIiE;EAwBvE,YACU,OACT,QACC;AAtBO;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEC;AAGA,SAAA,QAAA;AAGT,SAAK,SAAS;AACd,SAAK,OAAO,OAAO;AACnB,SAAK,YAAY,OAAO;AACxB,SAAK,UAAU,OAAO;AACtB,SAAK,UAAU,OAAO;AACtB,SAAK,YAAY,OAAO;AACxB,SAAK,aAAa,OAAO;AACzB,SAAK,aAAa,OAAO;AACzB,SAAK,UAAU,OAAO;AACtB,SAAK,WAAW,OAAO;AACvB,SAAK,aAAa,OAAO;AACzB,SAAK,aAAa,OAAO;AACzB,SAAK,WAAW,OAAO;AACvB,SAAK,aAAa,OAAO;AACzB,SAAK,YAAY,OAAO;AACxB,SAAK,oBAAoB,OAAO;EACjC;EAIA,mBAAmB,OAAyB;AAC3C,WAAO;EACR;EAEA,iBAAiB,OAAyB;AACzC,WAAO;EACR;;EAGA,sBAA+B;AAC9B,WAAO,KAAK,OAAO,cAAc,UAAa,KAAK,OAAO,UAAU,SAAS;EAC9E;AACD;AA3DC,cALqB,QAKJ,IAAsB;;;ACnExC,IAAAA;AAuLkBA,MAAA;AANX,IAAe,gBAAf,MAKwC;EAO9C,YAAYC,OAAiB,UAAyB,YAA6B;AAFzE;AA0EV;;;oCAAW,KAAK;AAoBhB;;;qCAAY,KAAK;AA3FhB,SAAK,SAAS;MACb,MAAAA;MACA,WAAWA,UAAS;MACpB,SAAS;MACT,SAAS;MACT,YAAY;MACZ,YAAY;MACZ,UAAU;MACV,YAAY;MACZ,YAAY;MACZ;MACA;MACA,WAAW;IACZ;EACD;;;;;;;;;;;;EAaA,QAAmC;AAClC,WAAO;EACR;;;;;;EAOA,UAAyB;AACxB,SAAK,OAAO,UAAU;AACtB,WAAO;EACR;;;;;;;;EASA,QAAQ,OAA+F;AACtG,SAAK,OAAO,UAAU;AACtB,SAAK,OAAO,aAAa;AACzB,WAAO;EACR;;;;;;;EAQA,WACC,IACsC;AACtC,SAAK,OAAO,YAAY;AACxB,SAAK,OAAO,aAAa;AACzB,WAAO;EACR;;;;;;;;EAcA,YACC,IACmB;AACnB,SAAK,OAAO,aAAa;AACzB,SAAK,OAAO,aAAa;AACzB,WAAO;EACR;;;;;;EAYA,aAEA;AACC,SAAK,OAAO,aAAa;AACzB,SAAK,OAAO,UAAU;AACtB,WAAO;EAER;;EAUA,QAAQA,OAAc;AACrB,QAAI,KAAK,OAAO,SAAS;AAAI;AAC7B,SAAK,OAAO,OAAOA;EACpB;AACD;AA9HC,cANqB,eAMJD,KAAsB;;;ACtLjC,IAAM,YAAY,OAAO,IAAI,cAAc;;;ACDlD,IAAAE;AAekBA,MAAA;AADX,IAAM,oBAAN,MAAwB;EAY9B,YACC,QAKA,SAIC;AAlBF;;AAGA;qCAA4C;AAG5C;qCAA4C;AAa3C,SAAK,YAAY,MAAM;AACtB,YAAM,EAAE,MAAAC,OAAM,SAAS,eAAe,IAAI,OAAO;AACjD,aAAO,EAAE,MAAAA,OAAM,SAAS,cAAc,eAAe,CAAC,EAAG,OAAkB,eAAe;IAC3F;AACA,QAAI,SAAS;AACZ,WAAK,YAAY,QAAQ;AACzB,WAAK,YAAY,QAAQ;IAC1B;EACD;EAEA,SAAS,QAAkC;AAC1C,SAAK,YAAY,WAAW,SAAY,cAAc;AACtD,WAAO;EACR;EAEA,SAAS,QAAkC;AAC1C,SAAK,YAAY,WAAW,SAAY,cAAc;AACtD,WAAO;EACR;;EAGA,MAAM,OAA4B;AACjC,WAAO,IAAI,WAAW,OAAO,IAAI;EAClC;AACD;AA9CC,cADY,mBACKD,KAAsB;AAfxC,IAAAA;AAkEkBA,MAAA;AADX,IAAM,aAAN,MAAiB;EAOvB,YAAqB,OAAgB,SAA4B;AAJxD;AACA;AACA;AAEY,SAAA,QAAA;AACpB,SAAK,YAAY,QAAQ;AACzB,SAAK,WAAW,QAAQ;AACxB,SAAK,WAAW,QAAQ;EACzB;EAEA,UAAkB;AACjB,UAAM,EAAE,MAAAC,OAAM,SAAS,eAAe,IAAI,KAAK,UAAU;AACzD,UAAM,cAAc,QAAQ,IAAI,CAAC,WAAW,OAAO,IAAI;AACvD,UAAM,qBAAqB,eAAe,IAAI,CAAC,WAAW,OAAO,IAAI;AACrE,UAAM,SAAS;MACd,KAAK,MAAM,SAAS;MACpB,GAAG;MACH,eAAe,CAAC,EAAG,MAAM,SAAS;MAClC,GAAG;IACJ;AACA,WAAOA,SAAQ,GAAG,OAAO,KAAK,GAAG,CAAC;EACnC;AACD;AAxBC,cADY,YACKD,KAAsB;AA+BjC,SAAS,WAKf,QAKoB;AACpB,WAAS,eAAe;AACvB,UAAM,EAAE,MAAAC,OAAM,SAAS,eAAe,IAAI;AAC1C,WAAO;MACN,MAAAA;MACA;MACA;IACD;EACD;AAEA,SAAO,IAAI,kBAAkB,YAAY;AAC1C;;;ACjHO,SAAS,OAAOC,OAA0C;AAChE,SAAO,IAAI,0BAA0BA,KAAI;AAC1C;AAEO,SAAS,cAAc,OAAgB,SAAmB;AAChE,SAAO,GAAG,MAAM,SAAS,CAAC,IAAI,QAAQ,KAAK,GAAG,CAAC;AAChD;AAXA,IAAAC;AAckBA,MAAA;AADX,IAAM,0BAAN,MAA8B;EAQpC,YACC,SACQD,OACP;AAPF;;AAEA;kDAAyB;AAIhB,SAAA,OAAAA;AAER,SAAK,UAAU;EAChB;EAEA,mBAAmB;AAClB,SAAK,yBAAyB;AAC9B,WAAO;EACR;;EAGA,MAAM,OAAkC;AACvC,WAAO,IAAI,iBAAiB,OAAO,KAAK,SAAS,KAAK,wBAAwB,KAAK,IAAI;EACxF;AACD;AAvBC,cADY,yBACKC,KAAsB;AAdxC,IAAAA;AAwCkBA,MAAA;AADX,IAAM,4BAAN,MAAgC;EAMtC,YACCD,OACC;AAJF;;AAKC,SAAK,OAAOA;EACb;EAEA,MAAM,SAAoC;AACzC,WAAO,IAAI,wBAAwB,SAAS,KAAK,IAAI;EACtD;AACD;AAdC,cADY,2BACKC,KAAsB;AAxCxC,IAAAA;AAyDkBA,MAAA;AADX,IAAM,mBAAN,MAAuB;EAO7B,YAAqB,OAAgB,SAAqB,kBAA2BD,OAAe;AAJ3F;AACA;AACA,4CAA4B;AAEhB,SAAA,QAAA;AACpB,SAAK,UAAU;AACf,SAAK,OAAOA,SAAQ,cAAc,KAAK,OAAO,KAAK,QAAQ,IAAI,CAAC,WAAW,OAAO,IAAI,CAAC;AACvF,SAAK,mBAAmB;EACzB;EAEA,UAAU;AACT,WAAO,KAAK;EACb;AACD;AAfC,cADY,kBACKC,KAAsB;;;ACzDxC,SAAS,kBAAkB,aAAqB,WAAmB,UAAqC;AACvG,WAAS,IAAI,WAAW,IAAI,YAAY,QAAQ,KAAK;AACpD,UAAMC,QAAO,YAAY,CAAC;AAE1B,QAAIA,UAAS,MAAM;AAClB;AACA;IACD;AAEA,QAAIA,UAAS,KAAK;AACjB,aAAO,CAAC,YAAY,MAAM,WAAW,CAAC,EAAE,QAAQ,OAAO,EAAE,GAAG,IAAI,CAAC;IAClE;AAEA,QAAI,UAAU;AACb;IACD;AAEA,QAAIA,UAAS,OAAOA,UAAS,KAAK;AACjC,aAAO,CAAC,YAAY,MAAM,WAAW,CAAC,EAAE,QAAQ,OAAO,EAAE,GAAG,CAAC;IAC9D;EACD;AAEA,SAAO,CAAC,YAAY,MAAM,SAAS,EAAE,QAAQ,OAAO,EAAE,GAAG,YAAY,MAAM;AAC5E;AAEO,SAAS,mBAAmB,aAAqB,YAAY,GAAoB;AACvF,QAAM,SAAgB,CAAC;AACvB,MAAI,IAAI;AACR,MAAI,kBAAkB;AAEtB,SAAO,IAAI,YAAY,QAAQ;AAC9B,UAAMA,QAAO,YAAY,CAAC;AAE1B,QAAIA,UAAS,KAAK;AACjB,UAAI,mBAAmB,MAAM,WAAW;AACvC,eAAO,KAAK,EAAE;MACf;AACA,wBAAkB;AAClB;AACA;IACD;AAEA,sBAAkB;AAElB,QAAIA,UAAS,MAAM;AAClB,WAAK;AACL;IACD;AAEA,QAAIA,UAAS,KAAK;AACjB,YAAM,CAACC,QAAOC,UAAS,IAAI,kBAAkB,aAAa,IAAI,GAAG,IAAI;AACrE,aAAO,KAAKD,MAAK;AACjB,UAAIC;AACJ;IACD;AAEA,QAAIF,UAAS,KAAK;AACjB,aAAO,CAAC,QAAQ,IAAI,CAAC;IACtB;AAEA,QAAIA,UAAS,KAAK;AACjB,YAAM,CAACC,QAAOC,UAAS,IAAI,mBAAmB,aAAa,IAAI,CAAC;AAChE,aAAO,KAAKD,MAAK;AACjB,UAAIC;AACJ;IACD;AAEA,UAAM,CAAC,OAAO,YAAY,IAAI,kBAAkB,aAAa,GAAG,KAAK;AACrE,WAAO,KAAK,KAAK;AACjB,QAAI;EACL;AAEA,SAAO,CAAC,QAAQ,CAAC;AAClB;AAEO,SAAS,aAAa,aAA4B;AACxD,QAAM,CAAC,MAAM,IAAI,mBAAmB,aAAa,CAAC;AAClD,SAAO;AACR;AAEO,SAAS,YAAY,OAAsB;AACjD,SAAO,IACN,MAAM,IAAI,CAAC,SAAS;AACnB,QAAI,MAAM,QAAQ,IAAI,GAAG;AACxB,aAAO,YAAY,IAAI;IACxB;AAEA,QAAI,OAAO,SAAS,UAAU;AAC7B,aAAO,IAAI,KAAK,QAAQ,OAAO,MAAM,EAAE,QAAQ,MAAM,KAAK,CAAC;IAC5D;AAEA,WAAO,GAAG,IAAI;EACf,CAAC,EAAE,KAAK,GAAG,CACZ;AACD;;;AC9FO,SAAS,KAA6B,OAA0B,MAAY;AAClF,SAAO,GAAG,GAAG,IAAI;AAClB;;;ACOA,IAAAC,KAAA;AA4BO,IAAe,kBAAf,eAKG,oBAKiBA,MAAA,YALjB,IAEV;EAPO;;AAQE,6CAAuC,CAAC;;EAIhD,MAAoD,MAclD;AACD,WAAO,IAAI,eAAe,KAAK,OAAO,MAAM,MAAmC,IAAW;EAC3F;EAEA,WACC,KACA,UAAsC,CAAC,GAChC;AACP,SAAK,kBAAkB,KAAK,EAAE,KAAK,QAAQ,CAAC;AAC5C,WAAO;EACR;EAEA,OACCC,OACA,QACO;AACP,SAAK,OAAO,WAAW;AACvB,SAAK,OAAO,aAAaA;AACzB,SAAK,OAAO,aAAa,iCAAQ;AACjC,WAAO;EACR;EAEA,kBAAkB,IAEf;AACF,SAAK,OAAO,YAAY;MACvB;MACA,MAAM;MACN,MAAM;IACP;AACA,WAAO;EAGR;;EAGA,iBAAiB,QAAkB,OAA8B;AAChE,WAAO,KAAK,kBAAkB,IAAI,CAAC,EAAE,KAAK,QAAQ,MAAM;AACvD,aAAO;QACN,CAACC,MAAKC,aAAY;AACjB,gBAAM,UAAU,IAAI,kBAAkB,MAAM;AAC3C,kBAAM,gBAAgBD,KAAI;AAC1B,mBAAO,EAAE,SAAS,CAAC,MAAM,GAAG,gBAAgB,CAAC,aAAa,EAAE;UAC7D,CAAC;AACD,cAAIC,SAAQ,UAAU;AACrB,oBAAQ,SAASA,SAAQ,QAAQ;UAClC;AACA,cAAIA,SAAQ,UAAU;AACrB,oBAAQ,SAASA,SAAQ,QAAQ;UAClC;AACA,iBAAO,QAAQ,MAAM,KAAK;QAC3B;QACA;QACA;MACD;IACD,CAAC;EACF;;EAQA,uBACC,OACoB;AACpB,WAAO,IAAI,kBAAkB,OAAO,KAAK,MAAM;EAChD;AACD;AArFC,cAVqB,iBAUKH,KAAsB;AAtCjD,IAAAA,KAAAI;AA8HO,IAAe,WAAf,eAIGA,MAAA,QACiBJ,MAAA,YADjBI,KAA2D;EAGpE,YACmB,OAClB,QACC;AACD,QAAI,CAAC,OAAO,YAAY;AACvB,aAAO,aAAa,cAAc,OAAO,CAAC,OAAO,IAAI,CAAC;IACvD;AACA,UAAM,OAAO,MAAM;AAND,SAAA,QAAA;EAOnB;AACD;AAXC,cALqB,UAKKJ,KAAsB;AAnIjD,IAAAA,MAAAI;AAkJO,IAAM,oBAAN,eAEGA,MAAA,UACiBJ,OAAA,YADjBI,KAAoC;EAFvC;;AASN,uCAAsC;MACrC,OAAO,KAAK,OAAO,SAAS;MAC5B,OAAO,KAAK,OAAO,SAAS;MAC5B,SAAS,KAAK,OAAO;IACtB;AACA,yCAAwC;MACvC,OAAO;MACP,OAAO;MACP,SAAS;IACV;;EAbS,aAAqB;AAC7B,WAAO,KAAK,WAAW;EACxB;EAaA,MAAkC;AACjC,SAAK,YAAY,QAAQ;AACzB,WAAO;EACR;EAEA,OAAmC;AAClC,SAAK,YAAY,QAAQ;AACzB,WAAO;EACR;EAEA,aAAqD;AACpD,SAAK,YAAY,QAAQ;AACzB,WAAO;EACR;EAEA,YAAoD;AACnD,SAAK,YAAY,QAAQ;AACzB,WAAO;EACR;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA+BA,GAAG,SAA2C;AAC7C,SAAK,YAAY,UAAU;AAC3B,WAAO;EACR;AACD;AAtEC,cAHY,mBAGcJ,MAAsB;AArJjD,IAAAA;AA8NkBA,OAAA;AADX,IAAM,gBAAN,MAAoB;EAE1B,YACCC,OACA,WACA,MACA,aACC;AAOF;AACA;AACA;AACA;AATC,SAAK,OAAOA;AACZ,SAAK,YAAY;AACjB,SAAK,OAAO;AACZ,SAAK,cAAc;EACpB;AAMD;AAjBC,cADY,eACKD,MAAsB;AA9NxC,IAAAA,MAAAI;AA0PO,IAAM,iBAAN,eAGGA,MAAA,iBAqBiBJ,OAAA,YArBjBI,KAoBR;EAGD,YACCH,OACA,aACA,MACC;AACD,UAAMA,OAAM,SAAS,SAAS;AAC9B,SAAK,OAAO,cAAc;AAC1B,SAAK,OAAO,OAAO;EACpB;;EAGS,MACR,OACuG;AACvG,UAAM,aAAa,KAAK,OAAO,YAAY,MAAM,KAAK;AACtD,WAAO,IAAI;MACV;MACA,KAAK;MACL;IACD;EACD;AACD;AAvBC,cAxBY,gBAwBcD,MAAc;AAlRzC,IAAAA,MAAAI;AA2SO,IAAM,WAAN,MAAM,kBAMHA,MAAA,UAGiBJ,OAAA,YAHjBI,KAAoE;EAK7E,YACC,OACA,QACS,YACA,OACR;AACD,UAAM,OAAO,MAAM;AAVX;AAOC,SAAA,aAAA;AACA,SAAA,QAAA;AAGT,SAAK,OAAO,OAAO;EACpB;EAEA,aAAqB;AACpB,WAAO,GAAG,KAAK,WAAW,WAAW,CAAC,IAAI,OAAO,KAAK,SAAS,WAAW,KAAK,OAAO,EAAE;EACzF;EAES,mBAAmB,OAAsC;AACjE,QAAI,OAAO,UAAU,UAAU;AAE9B,cAAQ,aAAa,KAAK;IAC3B;AACA,WAAO,MAAM,IAAI,CAAC,MAAM,KAAK,WAAW,mBAAmB,CAAC,CAAC;EAC9D;EAES,iBAAiB,OAAkB,gBAAgB,OAA2B;AACtF,UAAM,IAAI,MAAM;MAAI,CAAC,MACpB,MAAM,OACH,OACA,GAAG,KAAK,YAAY,QAAO,IAC3B,KAAK,WAAW,iBAAiB,GAAgB,IAAI,IACrD,KAAK,WAAW,iBAAiB,CAAC;IACtC;AACA,QAAI;AAAe,aAAO;AAC1B,WAAO,YAAY,CAAC;EACrB;AACD;AAnCC,cATY,UAScJ,MAAsB;AAT1C,IAAM,UAAN;;;ACnSP,IAAM,cAAc,OAAO,IAAI,kBAAkB;AAa1C,SAAS,SAAS,KAAoD;AAC5E,SAAO,CAAC,CAAC,OAAO,OAAO,QAAQ,cAAc,eAAe,OAAO,IAAI,WAAW,MAAM;AACzF;AA9BA,IAAAK,MAAAC;AAgCO,IAAM,sBAAN,eAEGA,MAAA,iBACiBD,OAAA,YADjBC,KAAsD;EAG/D,YAAYC,OAAiB,cAAuC;AACnE,UAAMA,OAAM,UAAU,cAAc;AACpC,SAAK,OAAO,OAAO;EACpB;;EAGS,MACR,OACgD;AAChD,WAAO,IAAI;MACV;MACA,KAAK;IACN;EACD;AACD;AAhBC,cAHY,qBAGcF,MAAsB;AAnCjD,IAAAA,MAAAC;AAqDO,IAAM,eAAN,eACEA,MAAA,UAEkBD,OAAA,YAFlBC,KACT;EAMC,YACC,OACA,QACC;AACD,UAAM,OAAO,MAAM;AAPX,gCAAO,KAAK,OAAO;AACV,sCAAa,KAAK,OAAO,KAAK;AAO/C,SAAK,OAAO,OAAO;EACpB;EAEA,aAAqB;AACpB,WAAO,KAAK,KAAK;EAClB;AACD;AAhBC,cAHY,cAGcD,MAAsB;AAmB1C,SAAS,OACf,UACA,QACsB;AACtB,SAAO,iBAAiB,UAAU,QAAQ,MAAS;AACpD;AAGO,SAAS,iBACf,UACA,QACA,QACsB;AACtB,QAAM,eAAoC,OAAO;IAChD,CAAuBE,UACtB,IAAI,oBAAoBA,SAAQ,IAAa,YAAY;IAC1D;MACC;MACA,YAAY;MACZ;MACA,CAAC,WAAW,GAAG;IAChB;EACD;AAEA,SAAO;AACR;;;ACtGA,IAAAC;AAekBA,OAAA;AAJX,IAAM,WAAN,MAGiB;EAWvB,YAAYC,MAAU,WAAoC,OAAe,SAAS,OAAO;AACxF,SAAK,IAAI;MACR,OAAO;MACP,KAAAA;MACA,gBAAgB;MAChB;MACA;IACD;EACD;;;;AAKD;AAvBC,cAJY,UAIKD,MAAsB;AAfxC,IAAAA,MAAAE;AAwCO,IAAM,eAAN,eAGGA,MAAA,UACiBF,OAAA,YADjBE,KAA6B;AAEvC;AADC,cAJY,cAIcF,MAAsB;;;AC5C1C,IAAM,iBAAiB,OAAO,IAAI,wBAAwB;;;ACoB1D,IAAM,SAAS,OAAO,IAAI,gBAAgB;AAG1C,IAAM,UAAU,OAAO,IAAI,iBAAiB;AAG5C,IAAM,qBAAqB,OAAO,IAAI,4BAA4B;AAGlE,IAAM,eAAe,OAAO,IAAI,sBAAsB;AAGtD,IAAM,WAAW,OAAO,IAAI,kBAAkB;AAG9C,IAAM,UAAU,OAAO,IAAI,iBAAiB;AAG5C,IAAM,qBAAqB,OAAO,IAAI,4BAA4B;AAEzE,IAAM,iBAAiB,OAAO,IAAI,wBAAwB;AAvC1D,IAAAG,MAAAC,KAAA;AAiDkB,iBA+BhB,gBAMA,mBAGA,aAGA,cAGA,yBAMA,eAGA,cAGAA,MAAA,gBAGAD,OAAA;AA9DK,IAAM,QAAN,MAAuE;EAgE7E,YAAYE,OAAc,QAA4B,UAAkB;AAhCxE;;;;wBAAC;AAMD;;;;wBAAC;AAGD;wBAAC;AAGD;wBAAC;AAGD;wBAAC;AAMD;;;;wBAAC;AAGD;wBAAC,IAAW;AAGZ;wBAACD,KAAkB;AAGnB;wBAACD;AAGA,SAAK,SAAS,IAAI,KAAK,YAAY,IAAIE;AACvC,SAAK,MAAM,IAAI;AACf,SAAK,QAAQ,IAAI;EAClB;AACD;AApEC,cADY,OACK,IAAsB;;AAgBvC,cAjBY,OAiBI,UAAS;EACxB,MAAM;EACN;EACA;EACA;EACA;EACA;EACA;EACA;AACD;AA6CM,SAAS,QAAQ,OAAgC;AACvD,SAAO,OAAO,UAAU,YAAY,UAAU,QAAQ,kBAAkB;AACzE;AAqBO,SAAS,aAA8B,OAA0B;AACvE,SAAO,MAAM,SAAS;AACvB;AAEO,SAAS,mBAAoC,OAAmD;AACtG,SAAO,GAAG,MAAM,MAAM,KAAK,QAAQ,IAAI,MAAM,SAAS,CAAC;AACxD;;;ACpJA,IAAI,UAAU;;;ACGd,IAAI;AACJ,IAAI;AAkBG,IAAM,SAAS;EACrB,gBAAoDC,OAAgB,IAAsB;AACzF,QAAI,CAAC,MAAM;AACV,aAAO,GAAG;IACX;AAEA,QAAI,CAAC,WAAW;AACf,kBAAY,KAAK,MAAM,UAAU,eAAe,OAAU;IAC3D;AAEA,WAAO;MACN,CAACC,OAAMC,eACNA,WAAU;QACTF;QACC,CAAC,SAAe;AAChB,cAAI;AACH,mBAAO,GAAG,IAAI;UACf,SAAS,GAAG;AACX,iBAAK,UAAU;cACd,MAAMC,MAAK,eAAe;cAC1B,SAAS,aAAa,QAAQ,EAAE,UAAU;;YAC3C,CAAC;AACD,kBAAM;UACP,UAAA;AACC,iBAAK,IAAI;UACV;QACD;MACD;MACD;MACA;IACD;EACD;AACD;;;ACtDA,IAAAE;AAiBkBA,OAAA;AADX,IAAM,qBAAN,MAAyB;AAEhC;AADC,cADY,oBACKA,MAAsB;AAmDjC,SAAS,aAAa,OAAqC;AACjE,SAAO,UAAU,QAAQ,UAAU,UAAa,OAAQ,MAAc,WAAW;AAClF;AAEA,SAAS,aAAa,SAA+C;AAxErE,MAAAA;AAyEC,QAAM,SAA2B,EAAE,KAAK,IAAI,QAAQ,CAAC,EAAE;AACvD,aAAW,SAAS,SAAS;AAC5B,WAAO,OAAO,MAAM;AACpB,WAAO,OAAO,KAAK,GAAG,MAAM,MAAM;AAClC,SAAIA,QAAA,MAAM,YAAN,gBAAAA,MAAe,QAAQ;AAC1B,UAAI,CAAC,OAAO,SAAS;AACpB,eAAO,UAAU,CAAC;MACnB;AACA,aAAO,QAAQ,KAAK,GAAG,MAAM,OAAO;IACrC;EACD;AACA,SAAO;AACR;AArFA,IAAAA;AAwFkBA,OAAA;AADX,IAAM,cAAN,MAAwC;EAK9C,YAAY,OAA0B;AAF7B;AAGR,SAAK,QAAQ,MAAM,QAAQ,KAAK,IAAI,QAAQ,CAAC,KAAK;EACnD;EAEA,SAAuB;AACtB,WAAO,IAAI,IAAI,CAAC,IAAI,CAAC;EACtB;AACD;AAXC,cADY,aACKA,MAAsB;AAxFxC,IAAAA;AAsGkBA,OAAA;AADX,IAAM,OAAN,MAAM,KAAuC;EAYnD,YAAqB,aAAyB;AAH9C;mCAAsC;AAC9B,8CAAqB;AAER,SAAA,cAAA;EAA0B;EAE/C,OAAO,OAAkB;AACxB,SAAK,YAAY,KAAK,GAAG,MAAM,WAAW;AAC1C,WAAO;EACR;EAEA,QAAQ,QAA4C;AACnD,WAAO,OAAO,gBAAgB,oBAAoB,CAAC,SAAS;AAC3D,YAAM,QAAQ,KAAK,2BAA2B,KAAK,aAAa,MAAM;AACtE,mCAAM,cAAc;QACnB,sBAAsB,MAAM;QAC5B,wBAAwB,KAAK,UAAU,MAAM,MAAM;MACpD;AACA,aAAO;IACR,CAAC;EACF;EAEA,2BAA2B,QAAoB,SAAkC;AAChF,UAAM,SAAS,OAAO,OAAO,CAAC,GAAG,SAAS;MACzC,cAAc,QAAQ,gBAAgB,KAAK;MAC3C,iBAAiB,QAAQ,mBAAmB,EAAE,OAAO,EAAE;IACxD,CAAC;AAED,UAAM;MACL;MACA;MACA;MACA;MACA;MACA;IACD,IAAI;AAEJ,WAAO,aAAa,OAAO,IAAI,CAAC,UAA4B;AAlJ9D,UAAAA;AAmJG,UAAI,GAAG,OAAO,WAAW,GAAG;AAC3B,eAAO,EAAE,KAAK,MAAM,MAAM,KAAK,EAAE,GAAG,QAAQ,CAAC,EAAE;MAChD;AAEA,UAAI,GAAG,OAAO,IAAI,GAAG;AACpB,eAAO,EAAE,KAAK,WAAW,MAAM,KAAK,GAAG,QAAQ,CAAC,EAAE;MACnD;AAEA,UAAI,UAAU,QAAW;AACxB,eAAO,EAAE,KAAK,IAAI,QAAQ,CAAC,EAAE;MAC9B;AAEA,UAAI,MAAM,QAAQ,KAAK,GAAG;AACzB,cAAM,SAAqB,CAAC,IAAI,YAAY,GAAG,CAAC;AAChD,mBAAW,CAAC,GAAG,CAAC,KAAK,MAAM,QAAQ,GAAG;AACrC,iBAAO,KAAK,CAAC;AACb,cAAI,IAAI,MAAM,SAAS,GAAG;AACzB,mBAAO,KAAK,IAAI,YAAY,IAAI,CAAC;UAClC;QACD;AACA,eAAO,KAAK,IAAI,YAAY,GAAG,CAAC;AAChC,eAAO,KAAK,2BAA2B,QAAQ,MAAM;MACtD;AAEA,UAAI,GAAG,OAAO,IAAG,GAAG;AACnB,eAAO,KAAK,2BAA2B,MAAM,aAAa;UACzD,GAAG;UACH,cAAc,gBAAgB,MAAM;QACrC,CAAC;MACF;AAEA,UAAI,GAAG,OAAO,KAAK,GAAG;AACrB,cAAM,aAAa,MAAM,MAAM,OAAO,MAAM;AAC5C,cAAM,YAAY,MAAM,MAAM,OAAO,IAAI;AACzC,eAAO;UACN,KAAK,eAAe,UAAa,MAAM,OAAO,IAC3C,WAAW,SAAS,IACpB,WAAW,UAAU,IAAI,MAAM,WAAW,SAAS;UACtD,QAAQ,CAAC;QACV;MACD;AAEA,UAAI,GAAG,OAAO,MAAM,GAAG;AACtB,cAAM,aAAa,OAAO,gBAAgB,KAAK;AAC/C,YAAI,QAAQ,iBAAiB,WAAW;AACvC,iBAAO,EAAE,KAAK,WAAW,UAAU,GAAG,QAAQ,CAAC,EAAE;QAClD;AAEA,cAAM,aAAa,MAAM,MAAM,MAAM,OAAO,MAAM;AAClD,eAAO;UACN,KAAK,MAAM,MAAM,OAAO,KAAK,eAAe,SACzC,WAAW,MAAM,MAAM,MAAM,OAAO,IAAI,CAAC,IAAI,MAAM,WAAW,UAAU,IACxE,WAAW,UAAU,IAAI,MAAM,WAAW,MAAM,MAAM,MAAM,OAAO,IAAI,CAAC,IAAI,MAC3E,WAAW,UAAU;UACzB,QAAQ,CAAC;QACV;MACD;AAEA,UAAI,GAAG,OAAO,IAAI,GAAG;AACpB,cAAM,aAAa,MAAM,cAAc,EAAE;AACzC,cAAM,WAAW,MAAM,cAAc,EAAE;AACvC,eAAO;UACN,KAAK,eAAe,UAAa,MAAM,cAAc,EAAE,UACpD,WAAW,QAAQ,IACnB,WAAW,UAAU,IAAI,MAAM,WAAW,QAAQ;UACrD,QAAQ,CAAC;QACV;MACD;AAEA,UAAI,GAAG,OAAO,KAAK,GAAG;AACrB,YAAI,GAAG,MAAM,OAAO,WAAW,GAAG;AACjC,iBAAO,EAAE,KAAK,YAAY,gBAAgB,SAAS,KAAK,GAAG,QAAQ,CAAC,KAAK,GAAG,SAAS,CAAC,MAAM,EAAE;QAC/F;AAEA,cAAM,cAAc,MAAM,UAAU,OAAO,OAAO,MAAM,QAAQ,iBAAiB,MAAM,KAAK;AAE5F,YAAI,GAAG,aAAa,IAAG,GAAG;AACzB,iBAAO,KAAK,2BAA2B,CAAC,WAAW,GAAG,MAAM;QAC7D;AAEA,YAAI,cAAc;AACjB,iBAAO,EAAE,KAAK,KAAK,eAAe,aAAa,MAAM,GAAG,QAAQ,CAAC,EAAE;QACpE;AAEA,YAAI,UAA+B,CAAC,MAAM;AAC1C,YAAI,eAAe;AAClB,oBAAU,CAAC,cAAc,MAAM,OAAO,CAAC;QACxC;AAEA,eAAO,EAAE,KAAK,YAAY,gBAAgB,SAAS,WAAW,GAAG,QAAQ,CAAC,WAAW,GAAG,QAAQ;MACjG;AAEA,UAAI,GAAG,OAAO,WAAW,GAAG;AAC3B,eAAO,EAAE,KAAK,YAAY,gBAAgB,SAAS,KAAK,GAAG,QAAQ,CAAC,KAAK,GAAG,SAAS,CAAC,MAAM,EAAE;MAC/F;AAEA,UAAI,GAAG,OAAO,KAAI,OAAO,KAAK,MAAM,eAAe,QAAW;AAC7D,eAAO,EAAE,KAAK,WAAW,MAAM,UAAU,GAAG,QAAQ,CAAC,EAAE;MACxD;AAEA,UAAI,GAAG,OAAO,QAAQ,GAAG;AACxB,YAAI,MAAM,EAAE,QAAQ;AACnB,iBAAO,EAAE,KAAK,WAAW,MAAM,EAAE,KAAK,GAAG,QAAQ,CAAC,EAAE;QACrD;AACA,eAAO,KAAK,2BAA2B;UACtC,IAAI,YAAY,GAAG;UACnB,MAAM,EAAE;UACR,IAAI,YAAY,IAAI;UACpB,IAAI,KAAK,MAAM,EAAE,KAAK;QACvB,GAAG,MAAM;MACV;AAEA,UAAI,SAAS,KAAK,GAAG;AACpB,YAAI,MAAM,QAAQ;AACjB,iBAAO,EAAE,KAAK,WAAW,MAAM,MAAM,IAAI,MAAM,WAAW,MAAM,QAAQ,GAAG,QAAQ,CAAC,EAAE;QACvF;AACA,eAAO,EAAE,KAAK,WAAW,MAAM,QAAQ,GAAG,QAAQ,CAAC,EAAE;MACtD;AAEA,UAAI,aAAa,KAAK,GAAG;AACxB,aAAIA,QAAA,MAAM,wBAAN,gBAAAA,MAAA,aAA+B;AAClC,iBAAO,KAAK,2BAA2B,CAAC,MAAM,OAAO,CAAC,GAAG,MAAM;QAChE;AACA,eAAO,KAAK,2BAA2B;UACtC,IAAI,YAAY,GAAG;UACnB,MAAM,OAAO;UACb,IAAI,YAAY,GAAG;QACpB,GAAG,MAAM;MACV;AAEA,UAAI,cAAc;AACjB,eAAO,EAAE,KAAK,KAAK,eAAe,OAAO,MAAM,GAAG,QAAQ,CAAC,EAAE;MAC9D;AAEA,aAAO,EAAE,KAAK,YAAY,gBAAgB,SAAS,KAAK,GAAG,QAAQ,CAAC,KAAK,GAAG,SAAS,CAAC,MAAM,EAAE;IAC/F,CAAC,CAAC;EACH;EAEQ,eACP,OACA,EAAE,aAAa,GACN;AACT,QAAI,UAAU,MAAM;AACnB,aAAO;IACR;AACA,QAAI,OAAO,UAAU,YAAY,OAAO,UAAU,WAAW;AAC5D,aAAO,MAAM,SAAS;IACvB;AACA,QAAI,OAAO,UAAU,UAAU;AAC9B,aAAO,aAAa,KAAK;IAC1B;AACA,QAAI,OAAO,UAAU,UAAU;AAC9B,YAAM,sBAAsB,MAAM,SAAS;AAC3C,UAAI,wBAAwB,mBAAmB;AAC9C,eAAO,aAAa,KAAK,UAAU,KAAK,CAAC;MAC1C;AACA,aAAO,aAAa,mBAAmB;IACxC;AACA,UAAM,IAAI,MAAM,6BAA6B,KAAK;EACnD;EAEA,SAAc;AACb,WAAO;EACR;EAaA,GAAG,OAAyC;AAE3C,QAAI,UAAU,QAAW;AACxB,aAAO;IACR;AAEA,WAAO,IAAI,KAAI,QAAQ,MAAM,KAAK;EACnC;EAEA,QAIE,SAAoD;AACrD,SAAK,UAAU,OAAO,YAAY,aAAa,EAAE,oBAAoB,QAAQ,IAAI;AACjF,WAAO;EACR;EAEA,eAAqB;AACpB,SAAK,qBAAqB;AAC1B,WAAO;EACR;;;;;;;EAQA,GAAG,WAA8C;AAChD,WAAO,YAAY,OAAO;EAC3B;AACD;AA7PC,cADY,MACKA,MAAsB;AADjC,IAAM,MAAN;AArGP,IAAAA;AA8WkBA,OAAA;AADX,IAAM,OAAN,MAAiC;EAKvC,YAAqB,OAAe;AAF1B;AAEW,SAAA,QAAA;EAAgB;EAErC,SAAuB;AACtB,WAAO,IAAI,IAAI,CAAC,IAAI,CAAC;EACtB;AACD;AATC,cADY,MACKA,MAAsB;AAejC,SAAS,KAAK,OAAqB;AACzC,SAAO,IAAI,KAAK,KAAK;AACtB;AAUO,SAAS,qBAAqB,OAAuD;AAC3F,SAAO,OAAO,UAAU,YAAY,UAAU,QAAQ,sBAAsB,SACxE,OAAQ,MAAc,qBAAqB;AAChD;AAEO,IAAM,cAA4C;EACxD,oBAAoB,CAAC,UAAU;AAChC;AAEO,IAAM,cAA4C;EACxD,kBAAkB,CAAC,UAAU;AAC9B;AAMO,IAAM,aAA0C;EACtD,GAAG;EACH,GAAG;AACJ;AA7ZA,IAAAA;AAiakBA,OAAA;AADX,IAAM,QAAN,MAAqF;;;;;EAS3F,YACU,OACA,UAA2D,aACnE;AATQ;AAOA,SAAA,QAAA;AACA,SAAA,UAAA;EACP;EAEH,SAAuB;AACtB,WAAO,IAAI,IAAI,CAAC,IAAI,CAAC;EACtB;AACD;AAhBC,cADY,OACKA,MAAsB;AAmBjC,SAAS,MACf,OACA,SACwB;AACxB,SAAO,IAAI,MAAM,OAAO,OAAO;AAChC;AA2BO,SAAS,IAAI,YAAkC,QAAyB;AAC9E,QAAM,cAA0B,CAAC;AACjC,MAAI,OAAO,SAAS,KAAM,QAAQ,SAAS,KAAK,QAAQ,CAAC,MAAM,IAAK;AACnE,gBAAY,KAAK,IAAI,YAAY,QAAQ,CAAC,CAAE,CAAC;EAC9C;AACA,aAAW,CAAC,YAAYC,MAAK,KAAK,OAAO,QAAQ,GAAG;AACnD,gBAAY,KAAKA,QAAO,IAAI,YAAY,QAAQ,aAAa,CAAC,CAAE,CAAC;EAClE;AAEA,SAAO,IAAI,IAAI,WAAW;AAC3B;CAEO,CAAUC,SAAV;AACC,WAAS,QAAa;AAC5B,WAAO,IAAI,IAAI,CAAC,CAAC;EAClB;AAFOA,OAAS,QAAA;AAKT,WAAS,SAAS,MAAuB;AAC/C,WAAO,IAAI,IAAI,IAAI;EACpB;AAFOA,OAAS,WAAA;AAQT,WAAS,IAAI,KAAkB;AACrC,WAAO,IAAI,IAAI,CAAC,IAAI,YAAY,GAAG,CAAC,CAAC;EACtC;AAFOA,OAAS,MAAA;AAiBT,WAAS,KAAK,QAAoB,WAA2B;AACnE,UAAM,SAAqB,CAAC;AAC5B,eAAW,CAAC,GAAG,KAAK,KAAK,OAAO,QAAQ,GAAG;AAC1C,UAAI,IAAI,KAAK,cAAc,QAAW;AACrC,eAAO,KAAK,SAAS;MACtB;AACA,aAAO,KAAK,KAAK;IAClB;AACA,WAAO,IAAI,IAAI,MAAM;EACtB;AATOA,OAAS,OAAA;AAuBT,WAAS,WAAW,OAAqB;AAC/C,WAAO,IAAI,KAAK,KAAK;EACtB;AAFOA,OAAS,aAAA;AAIT,WAASC,aAAkCC,OAAiC;AAClF,WAAO,IAAI,YAAYA,KAAI;EAC5B;AAFOF,OAAS,cAAAC;AAIT,WAASF,OACf,OACA,SACwB;AACxB,WAAO,IAAI,MAAM,OAAO,OAAO;EAChC;AALOC,OAAS,QAAAD;AAAA,GA9DA,QAAA,MAAA,CAAA,EAAA;CAsEV,CAAUI,SAAV;AAtiBP,MAAAL;AAwiBmB,EAAAA,QAAA;AADX,QAAM,WAAN,MAAM,SAA2C;IAWvD,YACUE,MACA,YACR;AALF;8CAAmB;AAGT,WAAA,MAAAA;AACA,WAAA,aAAA;IACP;IAEH,SAAc;AACb,aAAO,KAAK;IACb;;IAGA,QAAQ;AACP,aAAO,IAAI,SAAQ,KAAK,KAAK,KAAK,UAAU;IAC7C;EACD;AAvBC,gBADY,UACKF,OAAsB;AADjC,MAAM,UAAN;AAAAK,OAAM,UAAA;AAAA,GADG,QAAA,MAAA,CAAA,EAAA;AAtiBjB,IAAAL;AAmkBkBA,OAAA;AADX,IAAM,cAAN,MAAqF;EAK3F,YAAqBI,OAAa;AAAb,SAAA,OAAAA;EAAc;EAEnC,SAAc;AACb,WAAO,IAAI,IAAI,CAAC,IAAI,CAAC;EACtB;AACD;AATC,cADY,aACKJ,MAAsB;AAYjC,SAAS,YAAkCI,OAAiC;AAClF,SAAO,IAAI,YAAYA,KAAI;AAC5B;AAEO,SAAS,iBAAiB,QAAmB,QAA4C;AAC/F,SAAO,OAAO,IAAI,CAAC,MAAM;AACxB,QAAI,GAAG,GAAG,WAAW,GAAG;AACvB,UAAI,EAAE,EAAE,QAAQ,SAAS;AACxB,cAAM,IAAI,MAAM,6BAA6B,EAAE,IAAI,gBAAgB;MACpE;AAEA,aAAO,OAAO,EAAE,IAAI;IACrB;AAEA,QAAI,GAAG,GAAG,KAAK,KAAK,GAAG,EAAE,OAAO,WAAW,GAAG;AAC7C,UAAI,EAAE,EAAE,MAAM,QAAQ,SAAS;AAC9B,cAAM,IAAI,MAAM,6BAA6B,EAAE,MAAM,IAAI,gBAAgB;MAC1E;AAEA,aAAO,EAAE,QAAQ,iBAAiB,OAAO,EAAE,MAAM,IAAI,CAAC;IACvD;AAEA,WAAO;EACR,CAAC;AACF;AAIA,IAAM,gBAAgB,OAAO,IAAI,uBAAuB;AA3mBxD,IAAAJ,MAAAM,MAAAC;AAknBkBA,MAAA,YAWhBD,OAAA,gBAWAN,OAAA;AA3BK,IAAe,OAAf,MAIiB;EA2BvB,YACC,EAAE,MAAAI,OAAM,QAAQ,gBAAgB,MAAM,GAMrC;AAtBF;wBAACE;AAWD;wBAACN,MAAiB;AAYjB,SAAK,cAAc,IAAI;MACtB,MAAAI;MACA,cAAcA;MACd;MACA;MACA;MACA,YAAY,CAAC;MACb,SAAS;IACV;EACD;EAEA,SAAuB;AACtB,WAAO,IAAI,IAAI,CAAC,IAAI,CAAC;EACtB;AACD;AAhDC,cALqB,MAKJG,KAAsB;AAkDjC,SAAS,OAAO,MAA6B;AACnD,SAAO,OAAO,SAAS,YAAY,SAAS,QAAQ,iBAAiB;AACtE;AAEO,SAAS,YAA4B,MAAyB;AACpE,SAAO,KAAK,cAAc,EAAE;AAC7B;AAWA,OAAO,UAAU,SAAS,WAAW;AACpC,SAAO,IAAI,IAAI,CAAC,IAAI,CAAC;AACtB;AAGA,MAAM,UAAU,SAAS,WAAW;AACnC,SAAO,IAAI,IAAI,CAAC,IAAI,CAAC;AACtB;AAGA,SAAS,UAAU,SAAS,WAAW;AACtC,SAAO,IAAI,IAAI,CAAC,IAAI,CAAC;AACtB;;;ACjsBA,IAAAC;AASkBA,OAAA;AADX,IAAM,0BAAN,MAAuF;EAG7F,YAAoB,OAAqB;AAArB,SAAA,QAAA;EAAsB;EAE1C,IAAI,WAAoB,MAA4B;AACnD,QAAI,SAAS,SAAS;AACrB,aAAO,KAAK;IACb;AAEA,WAAO,UAAU,IAAqB;EACvC;AACD;AAXC,cADY,yBACKA,MAAsB;AATxC,IAAAA;AAuBkBA,OAAA;AADX,IAAM,yBAAN,MAAgF;EAGtF,YAAoB,OAAuB,qBAA8B;AAArD,SAAA,QAAA;AAAuB,SAAA,sBAAA;EAA+B;EAE1E,IAAI,QAAW,MAA4B;AAC1C,QAAI,SAAS,MAAM,OAAO,SAAS;AAClC,aAAO;IACR;AAEA,QAAI,SAAS,MAAM,OAAO,MAAM;AAC/B,aAAO,KAAK;IACb;AAEA,QAAI,KAAK,uBAAuB,SAAS,MAAM,OAAO,cAAc;AACnE,aAAO,KAAK;IACb;AAEA,QAAI,SAAS,gBAAgB;AAC5B,aAAO;QACN,GAAG,OAAO,cAAqC;QAC/C,MAAM,KAAK;QACX,SAAS;MACV;IACD;AAEA,QAAI,SAAS,MAAM,OAAO,SAAS;AAClC,YAAM,UAAW,OAAiB,MAAM,OAAO,OAAO;AACtD,UAAI,CAAC,SAAS;AACb,eAAO;MACR;AAEA,YAAM,iBAAyC,CAAC;AAEhD,aAAO,KAAK,OAAO,EAAE,IAAI,CAAC,QAAQ;AACjC,uBAAe,GAAG,IAAI,IAAI;UACzB,QAAQ,GAAG;UACX,IAAI,wBAAwB,IAAI,MAAM,QAAQ,IAAI,CAAC;QACpD;MACD,CAAC;AAED,aAAO;IACR;AAEA,UAAM,QAAQ,OAAO,IAA2B;AAChD,QAAI,GAAG,OAAO,MAAM,GAAG;AACtB,aAAO,IAAI,MAAM,OAAoB,IAAI,wBAAwB,IAAI,MAAM,QAAQ,IAAI,CAAC,CAAC;IAC1F;AAEA,WAAO;EACR;AACD;AAlDC,cADY,wBACKA,MAAsB;AAvBxC,IAAAA;AA4EkBA,OAAA;AADX,IAAM,iCAAN,MAAoF;EAG1F,YAAoB,OAAe;AAAf,SAAA,QAAA;EAAgB;EAEpC,IAAI,QAAW,MAA4B;AAC1C,QAAI,SAAS,eAAe;AAC3B,aAAO,aAAa,OAAO,aAAa,KAAK,KAAK;IACnD;AAEA,WAAO,OAAO,IAA2B;EAC1C;AACD;AAXC,cADY,gCACKA,MAAsB;AAajC,SAAS,aACf,OACA,YACI;AACJ,SAAO,IAAI,MAAM,OAAO,IAAI,uBAAuB,YAAY,KAAK,CAAC;AACtE;AAEO,SAAS,gBAAoC,UAAa,YAAuB;AACvF,SAAO,IAAI,MAAM,UAAU,IAAI,+BAA+B,UAAU,CAAC;AAC1E;AAEO,SAAS,mBAAwC,QAAW,YAAuB;AACzF,SAAO,IAAI;IACV;IACA,IAAI,wBAAwB,IAAI,MAAM,OAAO,OAAO,IAAI,uBAAuB,YAAY,KAAK,CAAC,CAAC;EACnG;AACD;AAEO,SAAS,8BAA8B,OAAoB,OAA4B;AAC7F,SAAO,IAAI,IAAI,QAAQ,uBAAuB,MAAM,KAAK,KAAK,GAAG,MAAM,UAAU;AAClF;AAEO,SAAS,uBAAuB,OAAY,OAAoB;AACtE,SAAO,IAAI,KAAK,MAAM,YAAY,IAAI,CAAC,MAAM;AAC5C,QAAI,GAAG,GAAG,MAAM,GAAG;AAClB,aAAO,mBAAmB,GAAG,KAAK;IACnC;AACA,QAAI,GAAG,GAAG,GAAG,GAAG;AACf,aAAO,uBAAuB,GAAG,KAAK;IACvC;AACA,QAAI,GAAG,GAAG,IAAI,OAAO,GAAG;AACvB,aAAO,8BAA8B,GAAG,KAAK;IAC9C;AACA,WAAO;EACR,CAAC,CAAC;AACH;;;AC7HA,IAAAC,MAAAC;AAEO,IAAM,eAAN,eAA2BA,OAAA,OAChBD,OAAA,YADgBC,MAAM;EAGvC,YAAY,EAAE,SAAS,MAAM,GAA0C;AACtE,UAAM,OAAO;AACb,SAAK,OAAO;AACZ,SAAK,QAAQ;EACd;AACD;AAPC,cADY,cACKD,MAAsB;AAHxC,IAAAA,MAAAC;AAYO,IAAM,2BAAN,eAAuCA,OAAA,cACnBD,OAAA,YADmBC,MAAa;EAG1D,cAAc;AACb,UAAM,EAAE,SAAS,WAAW,CAAC;EAC9B;AACD;AALC,cADY,0BACcD,MAAsB;;;ACG1C,SAAS,YAAY,OAAgB,QAA8B;AACzE,MACC,qBAAqB,MAAM,KACxB,CAAC,aAAa,KAAK,KACnB,CAAC,GAAG,OAAO,KAAK,KAChB,CAAC,GAAG,OAAO,WAAW,KACtB,CAAC,GAAG,OAAO,MAAM,KACjB,CAAC,GAAG,OAAO,KAAK,KAChB,CAAC,GAAG,OAAO,IAAI,GACjB;AACD,WAAO,IAAI,MAAM,OAAO,MAAM;EAC/B;AACA,SAAO;AACR;AAgCO,IAAM,KAAqB,CAAC,MAAkB,UAAwB;AAC5E,SAAO,MAAM,IAAI,MAAM,YAAY,OAAO,IAAI,CAAC;AAChD;AAoBO,IAAM,KAAqB,CAAC,MAAkB,UAAwB;AAC5E,SAAO,MAAM,IAAI,OAAO,YAAY,OAAO,IAAI,CAAC;AACjD;AAmBO,SAAS,OACZ,sBACe;AAClB,QAAM,aAAa,qBAAqB;IACvC,CAAC,MAAyC,MAAM;EACjD;AAEA,MAAI,WAAW,WAAW,GAAG;AAC5B,WAAO;EACR;AAEA,MAAI,WAAW,WAAW,GAAG;AAC5B,WAAO,IAAI,IAAI,UAAU;EAC1B;AAEA,SAAO,IAAI,IAAI;IACd,IAAI,YAAY,GAAG;IACnB,IAAI,KAAK,YAAY,IAAI,YAAY,OAAO,CAAC;IAC7C,IAAI,YAAY,GAAG;EACpB,CAAC;AACF;AAmBO,SAAS,MACZ,sBACe;AAClB,QAAM,aAAa,qBAAqB;IACvC,CAAC,MAAyC,MAAM;EACjD;AAEA,MAAI,WAAW,WAAW,GAAG;AAC5B,WAAO;EACR;AAEA,MAAI,WAAW,WAAW,GAAG;AAC5B,WAAO,IAAI,IAAI,UAAU;EAC1B;AAEA,SAAO,IAAI,IAAI;IACd,IAAI,YAAY,GAAG;IACnB,IAAI,KAAK,YAAY,IAAI,YAAY,MAAM,CAAC;IAC5C,IAAI,YAAY,GAAG;EACpB,CAAC;AACF;AAaO,SAAS,IAAI,WAA4B;AAC/C,SAAO,UAAU,SAAS;AAC3B;AAgBO,IAAM,KAAqB,CAAC,MAAkB,UAAwB;AAC5E,SAAO,MAAM,IAAI,MAAM,YAAY,OAAO,IAAI,CAAC;AAChD;AAkBO,IAAM,MAAsB,CAAC,MAAkB,UAAwB;AAC7E,SAAO,MAAM,IAAI,OAAO,YAAY,OAAO,IAAI,CAAC;AACjD;AAgBO,IAAM,KAAqB,CAAC,MAAkB,UAAwB;AAC5E,SAAO,MAAM,IAAI,MAAM,YAAY,OAAO,IAAI,CAAC;AAChD;AAgBO,IAAM,MAAsB,CAAC,MAAkB,UAAwB;AAC7E,SAAO,MAAM,IAAI,OAAO,YAAY,OAAO,IAAI,CAAC;AACjD;AA4BO,SAAS,QACf,QACA,QACM;AACN,MAAI,MAAM,QAAQ,MAAM,GAAG;AAC1B,QAAI,OAAO,WAAW,GAAG;AACxB,aAAO;IACR;AACA,WAAO,MAAM,MAAM,OAAO,OAAO,IAAI,CAAC,MAAM,YAAY,GAAG,MAAM,CAAC,CAAC;EACpE;AAEA,SAAO,MAAM,MAAM,OAAO,YAAY,QAAQ,MAAM,CAAC;AACtD;AA6BO,SAAS,WACf,QACA,QACM;AACN,MAAI,MAAM,QAAQ,MAAM,GAAG;AAC1B,QAAI,OAAO,WAAW,GAAG;AACxB,aAAO;IACR;AACA,WAAO,MAAM,MAAM,WAAW,OAAO,IAAI,CAAC,MAAM,YAAY,GAAG,MAAM,CAAC,CAAC;EACxE;AAEA,SAAO,MAAM,MAAM,WAAW,YAAY,QAAQ,MAAM,CAAC;AAC1D;AAkBO,SAAS,OAAO,OAAwB;AAC9C,SAAO,MAAM,KAAK;AACnB;AAkBO,SAAS,UAAU,OAAwB;AACjD,SAAO,MAAM,KAAK;AACnB;AAsBO,SAAS,OAAO,UAA2B;AACjD,SAAO,aAAa,QAAQ;AAC7B;AAuBO,SAAS,UAAU,UAA2B;AACpD,SAAO,iBAAiB,QAAQ;AACjC;AAoCO,SAAS,QAAQ,QAAoBE,MAAcC,MAAmB;AAC5E,SAAO,MAAM,MAAM,YAAY,YAAYD,MAAK,MAAM,CAAC,QACtD;IACCC;IACA;EACD,CACD;AACD;AAkCO,SAAS,WACf,QACAD,MACAC,MACM;AACN,SAAO,MAAM,MAAM,gBAClB;IACCD;IACA;EACD,CACD,QAAQ,YAAYC,MAAK,MAAM,CAAC;AACjC;AAkBO,SAAS,KAAK,QAAoC,OAAiC;AACzF,SAAO,MAAM,MAAM,SAAS,KAAK;AAClC;AAoBO,SAAS,QAAQ,QAAoC,OAAiC;AAC5F,SAAO,MAAM,MAAM,aAAa,KAAK;AACtC;AAqBO,SAAS,MAAM,QAAoC,OAAiC;AAC1F,SAAO,MAAM,MAAM,UAAU,KAAK;AACnC;AAoBO,SAAS,SAAS,QAAoC,OAAiC;AAC7F,SAAO,MAAM,MAAM,cAAc,KAAK;AACvC;AAkCO,SAAS,cACf,QACA,QACM;AACN,MAAI,MAAM,QAAQ,MAAM,GAAG;AAC1B,QAAI,OAAO,WAAW,GAAG;AACxB,YAAM,IAAI,MAAM,2CAA2C;IAC5D;AACA,UAAM,QAAQ,MAAM,YAAY,QAAQ,MAAM,CAAC;AAC/C,WAAO,MAAM,MAAM,OAAO,KAAK;EAChC;AAEA,SAAO,MAAM,MAAM,OAAO,YAAY,QAAQ,MAAM,CAAC;AACtD;AAmCO,SAAS,eACf,QACA,QACM;AACN,MAAI,MAAM,QAAQ,MAAM,GAAG;AAC1B,QAAI,OAAO,WAAW,GAAG;AACxB,YAAM,IAAI,MAAM,4CAA4C;IAC7D;AACA,UAAM,QAAQ,MAAM,YAAY,QAAQ,MAAM,CAAC;AAC/C,WAAO,MAAM,MAAM,OAAO,KAAK;EAChC;AAEA,SAAO,MAAM,MAAM,OAAO,YAAY,QAAQ,MAAM,CAAC;AACtD;AAkCO,SAAS,cACf,QACA,QACM;AACN,MAAI,MAAM,QAAQ,MAAM,GAAG;AAC1B,QAAI,OAAO,WAAW,GAAG;AACxB,YAAM,IAAI,MAAM,2CAA2C;IAC5D;AACA,UAAM,QAAQ,MAAM,YAAY,QAAQ,MAAM,CAAC;AAC/C,WAAO,MAAM,MAAM,OAAO,KAAK;EAChC;AAEA,SAAO,MAAM,MAAM,OAAO,YAAY,QAAQ,MAAM,CAAC;AACtD;;;AC7sBO,SAAS,IAAI,QAAqC;AACxD,SAAO,MAAM,MAAM;AACpB;AAkBO,SAAS,KAAK,QAAqC;AACzD,SAAO,MAAM,MAAM;AACpB;;;AC5CA,IAAAC,MAAAC;AAGkBA,OAAA,YAEhBD,OAAA,OAAO;AAHF,IAAe,eAAf,MAAqD;EAArD;AAGN,wBAACA,MAAsB;;EAEvB,MACC,YACuB;AACvB,WAAO,KAAK,KAAK,QAAW,UAAU;EACvC;EAEA,QAAQ,WAAyD;AAChE,WAAO,KAAK;MACX,CAAC,UAAU;AACV;AACA,eAAO;MACR;MACA,CAAC,WAAW;AACX;AACA,cAAM;MACP;IACD;EACD;EAEA,KACC,aACA,YAC+B;AAC/B,WAAO,KAAK,QAAQ,EAAE,KAAK,aAAa,UAAU;EACnD;AAGD;AA/BC,cADqB,cACJC,MAAsB;;;ACUjC,SAAS,aACf,SACA,KACA,qBACU;AAEV,QAAM,aAA6C,CAAC;AAEpD,QAAM,SAAS,QAAQ;IACtB,CAACC,SAAQ,EAAE,MAAM,MAAM,GAAG,gBAAgB;AACzC,UAAI;AACJ,UAAI,GAAG,OAAO,MAAM,GAAG;AACtB,kBAAU;MACX,WAAW,GAAG,OAAO,GAAG,GAAG;AAC1B,kBAAU,MAAM;MACjB,OAAO;AACN,kBAAU,MAAM,IAAI;MACrB;AACA,UAAI,OAAOA;AACX,iBAAW,CAAC,gBAAgB,SAAS,KAAK,KAAK,QAAQ,GAAG;AACzD,YAAI,iBAAiB,KAAK,SAAS,GAAG;AACrC,cAAI,EAAE,aAAa,OAAO;AACzB,iBAAK,SAAS,IAAI,CAAC;UACpB;AACA,iBAAO,KAAK,SAAS;QACtB,OAAO;AACN,gBAAM,WAAW,IAAI,WAAW;AAChC,gBAAM,QAAQ,KAAK,SAAS,IAAI,aAAa,OAAO,OAAO,QAAQ,mBAAmB,QAAQ;AAE9F,cAAI,uBAAuB,GAAG,OAAO,MAAM,KAAK,KAAK,WAAW,GAAG;AAClE,kBAAM,aAAa,KAAK,CAAC;AACzB,gBAAI,EAAE,cAAc,aAAa;AAChC,yBAAW,UAAU,IAAI,UAAU,OAAO,aAAa,MAAM,KAAK,IAAI;YACvE,WACC,OAAO,WAAW,UAAU,MAAM,YAAY,WAAW,UAAU,MAAM,aAAa,MAAM,KAAK,GAChG;AACD,yBAAW,UAAU,IAAI;YAC1B;UACD;QACD;MACD;AACA,aAAOA;IACR;IACA,CAAC;EACF;AAGA,MAAI,uBAAuB,OAAO,KAAK,UAAU,EAAE,SAAS,GAAG;AAC9D,eAAW,CAAC,YAAY,SAAS,KAAK,OAAO,QAAQ,UAAU,GAAG;AACjE,UAAI,OAAO,cAAc,YAAY,CAAC,oBAAoB,SAAS,GAAG;AACrE,eAAO,UAAU,IAAI;MACtB;IACD;EACD;AAEA,SAAO;AACR;AAGO,SAAS,oBACf,QACA,YACiC;AACjC,SAAO,OAAO,QAAQ,MAAM,EAAE,OAAyC,CAAC,QAAQ,CAACC,OAAM,KAAK,MAAM;AACjG,QAAI,OAAOA,UAAS,UAAU;AAC7B,aAAO;IACR;AAEA,UAAM,UAAU,aAAa,CAAC,GAAG,YAAYA,KAAI,IAAI,CAACA,KAAI;AAC1D,QAAI,GAAG,OAAO,MAAM,KAAK,GAAG,OAAO,GAAG,KAAK,GAAG,OAAO,IAAI,OAAO,GAAG;AAClE,aAAO,KAAK,EAAE,MAAM,SAAS,MAAM,CAAC;IACrC,WAAW,GAAG,OAAO,KAAK,GAAG;AAC5B,aAAO,KAAK,GAAG,oBAAoB,MAAM,MAAM,OAAO,OAAO,GAAG,OAAO,CAAC;IACzE,OAAO;AACN,aAAO,KAAK,GAAG,oBAAoB,OAAkC,OAAO,CAAC;IAC9E;AACA,WAAO;EACR,GAAG,CAAC,CAAC;AACN;AAEO,SAAS,aAAa,MAA+B,OAAgC;AAC3F,QAAM,WAAW,OAAO,KAAK,IAAI;AACjC,QAAM,YAAY,OAAO,KAAK,KAAK;AAEnC,MAAI,SAAS,WAAW,UAAU,QAAQ;AACzC,WAAO;EACR;AAEA,aAAW,CAAC,OAAO,GAAG,KAAK,SAAS,QAAQ,GAAG;AAC9C,QAAI,QAAQ,UAAU,KAAK,GAAG;AAC7B,aAAO;IACR;EACD;AAEA,SAAO;AACR;AAGO,SAAS,aAAa,OAAc,QAA4C;AACtF,QAAM,UAAyC,OAAO,QAAQ,MAAM,EAClE,OAAO,CAAC,CAAC,EAAE,KAAK,MAAM,UAAU,MAAS,EACzC,IAAI,CAAC,CAAC,KAAK,KAAK,MAAM;AAEtB,QAAI,GAAG,OAAO,GAAG,KAAK,GAAG,OAAO,MAAM,GAAG;AACxC,aAAO,CAAC,KAAK,KAAK;IACnB,OAAO;AACN,aAAO,CAAC,KAAK,IAAI,MAAM,OAAO,MAAM,MAAM,OAAO,OAAO,EAAE,GAAG,CAAC,CAAC;IAChE;EACD,CAAC;AAEF,MAAI,QAAQ,WAAW,GAAG;AACzB,UAAM,IAAI,MAAM,kBAAkB;EACnC;AAEA,SAAO,OAAO,YAAY,OAAO;AAClC;AAkCO,SAAS,YAAY,WAAgB,iBAAwB;AACnE,aAAW,iBAAiB,iBAAiB;AAC5C,eAAWA,SAAQ,OAAO,oBAAoB,cAAc,SAAS,GAAG;AACvE,UAAIA,UAAS;AAAe;AAE5B,aAAO;QACN,UAAU;QACVA;QACA,OAAO,yBAAyB,cAAc,WAAWA,KAAI,KAAK,uBAAO,OAAO,IAAI;MACrF;IACD;EACD;AACD;AAYO,SAAS,gBAAiC,OAA6B;AAC7E,SAAO,MAAM,MAAM,OAAO,OAAO;AAClC;AAEO,SAAS,sBAAsC,MAAmC;AACxF,SAAO,KAAK,cAAc,EAAE;AAC7B;AAGO,SAAS,iBAAiB,OAAsC;AACtE,SAAO,GAAG,OAAO,QAAQ,IACtB,MAAM,EAAE,QACR,GAAG,OAAO,IAAI,IACd,MAAM,cAAc,EAAE,OACtB,GAAG,OAAO,GAAG,IACb,SACA,MAAM,MAAM,OAAO,OAAO,IAC1B,MAAM,MAAM,OAAO,IAAI,IACvB,MAAM,MAAM,OAAO,QAAQ;AAC/B;AA6BO,SAAS,uBAEd,GAAiC,GAAwB;AAC1D,SAAO;IACN,MAAM,OAAO,MAAM,YAAY,EAAE,SAAS,IAAI,IAAI;IAClD,QAAQ,OAAO,MAAM,WAAW,IAAI;EACrC;AACD;AAuBO,SAAS,SAAS,MAAoB;AAC5C,MAAI,OAAO,SAAS,YAAY,SAAS;AAAM,WAAO;AAEtD,MAAI,KAAK,YAAY,SAAS;AAAU,WAAO;AAE/C,MAAI,YAAY,MAAM;AACrB,UAAM,OAAO,OAAO,KAAK,QAAQ;AACjC,QACC,SAAS,cAAc,SAAS,YAAY,OAAO,KAAK,QAAQ,EAAE,UAAU,MAAM,eAC/E,SAAS;AACX,aAAO;AAET,WAAO;EACR;AAEA,MAAI,YAAY,MAAM;AACrB,UAAM,OAAO,OAAO,KAAK,QAAQ;AACjC,QAAI,SAAS,YAAY,SAAS;AAAa,aAAO;AAEtD,WAAO;EACR;AAEA,MAAI,YAAY,MAAM;AACrB,UAAM,OAAO,OAAO,KAAK,QAAQ;AACjC,QAAI,SAAS,YAAY,SAAS;AAAa,aAAO;AAEtD,WAAO;EACR;AAEA,MAAI,UAAU,MAAM;AACnB,QAAI,KAAK,MAAM,MAAM,aAAa,KAAK,MAAM,MAAM,iBAAiB,KAAK,MAAM,MAAM;AAAW,aAAO;AAEvG,WAAO;EACR;AAEA,MAAI,gBAAgB,MAAM;AACzB,UAAM,OAAO,OAAO,KAAK,YAAY;AACrC,QAAI,SAAS,YAAY,SAAS,YAAY,SAAS;AAAa,aAAO;AAE3E,WAAO;EACR;AAEA,MAAI,YAAY,MAAM;AACrB,UAAM,OAAO,OAAO,KAAK,QAAQ;AACjC,QAAI,SAAS,YAAY,SAAS,cAAc,SAAS;AAAa,aAAO;AAE7E,WAAO;EACR;AAEA,MAAI,OAAO,KAAK,IAAI,EAAE,WAAW;AAAG,WAAO;AAE3C,SAAO;AACR;;;AC3TA,IAAAC,MAAAC;AAIO,IAAe,yBAAf,eAEGA,OAAA,iBAIiBD,OAAA,YAJjBC,MAGR;EAGD,0BACC,UAC6B;AAC7B,QAAI,UAAU;AACb,YAAM,EAAE,MAAAC,OAAM,GAAG,QAAQ,IAAI;AAC7B,WAAK,OAAO,oBAAoB;QAC/B,MAAM;QACN,cAAcA;QACd,iBAAiB;MAClB;IACD,OAAO;AACN,WAAK,OAAO,oBAAoB;QAC/B,MAAM;MACP;IACD;AAEA,SAAK,OAAO,aAAa;AACzB,SAAK,OAAO,UAAU;AAEtB,WAAO;EACR;EAEA,6BACC,UACgC;AAChC,QAAI,UAAU;AACb,YAAM,EAAE,MAAAA,OAAM,GAAG,QAAQ,IAAI;AAC7B,WAAK,OAAO,oBAAoB;QAC/B,MAAM;QACN,cAAcA;QACd,iBAAiB;MAClB;IACD,OAAO;AACN,WAAK,OAAO,oBAAoB;QAC/B,MAAM;MACP;IACD;AAEA,SAAK,OAAO,aAAa;AACzB,SAAK,OAAO,UAAU;AAEtB,WAAO;EACR;AACD;AA7CC,cANqB,wBAMKF,MAAsB;;;ACTjD,IAAAG,MAAAC;AAgBO,IAAM,oBAAN,eACEA,OAAA,wBAEkBD,OAAA,YAFlBC,MACT;EAGC,YAAYC,OAAiB;AAC5B,UAAMA,OAAM,UAAU,YAAY;EACnC;;EAGS,MACR,OAC8C;AAC9C,WAAO,IAAI,WAA4C,OAAO,KAAK,MAA8C;EAClH;AACD;AAZC,cAHY,mBAGcF,MAAsB;AAnBjD,IAAAA,MAAAC;AAiCO,IAAM,aAAN,eAA6EA,OAAA,UACzDD,OAAA,YADyDC,MAAY;EAG/F,aAAqB;AACpB,WAAO;EACR;EAES,mBAAmB,OAAgC;AAC3D,QAAI,OAAO,UAAU,UAAU;AAC9B,aAAO;IACR;AACA,WAAO,OAAO,KAAK;EACpB;AACD;AAZC,cADY,YACcD,MAAsB;AAlCjD,IAAAA,MAAAC;AAyDO,IAAM,oBAAN,eACEA,OAAA,wBAEkBD,OAAA,YAFlBC,MACT;EAGC,YAAYC,OAAiB;AAC5B,UAAMA,OAAM,UAAU,YAAY;EACnC;;EAGS,MACR,OAC8C;AAC9C,WAAO,IAAI;MACV;MACA,KAAK;IACN;EACD;AACD;AAfC,cAHY,mBAGcF,MAAsB;AA5DjD,IAAAA,MAAAC;AA6EO,IAAM,aAAN,eAA6EA,OAAA,UACzDD,OAAA,YADyDC,MAAY;EAG/F,aAAqB;AACpB,WAAO;EACR;;EAGS,mBAAmB,OAAuB;AAClD,WAAO,OAAO,KAAK;EACpB;AACD;AAVC,cADY,YACcD,MAAsB;AAuB1C,SAAS,OAAO,GAA4B,GAAoB;AACtE,QAAM,EAAE,MAAAE,OAAM,OAAO,IAAI,uBAAuC,GAAG,CAAC;AACpE,MAAI,OAAO,SAAS,UAAU;AAC7B,WAAO,IAAI,kBAAkBA,KAAI;EAClC;AACA,SAAO,IAAI,kBAAkBA,KAAI;AAClC;;;ACrGA,IAAAC,MAAAC;AAkBO,IAAM,uBAAN,eACEA,OAAA,iBAEkBD,OAAA,YAFlBC,MACT;EAGC,YAAYC,OAAc;AACzB,UAAMA,OAAM,UAAU,eAAe;AACrC,SAAK,OAAO,aAAa;AACzB,SAAK,OAAO,UAAU;EACvB;;EAGS,MACR,OACiD;AACjD,WAAO,IAAI;MACV;MACA,KAAK;IACN;EACD;AACD;AAjBC,cAHY,sBAGcF,MAAsB;AArBjD,IAAAA,MAAAC;AAwCO,IAAM,gBAAN,eAAmFA,OAAA,UAC/DD,OAAA,YAD+DC,MAAY;EAGrG,aAAqB;AACpB,WAAO;EACR;EAES,mBAAmB,OAAuB;AAClD,QAAI,OAAO,UAAU,UAAU;AAC9B,aAAO;IACR;AACA,WAAO,OAAO,KAAK;EACpB;AACD;AAZC,cADY,eACcD,MAAsB;AAzCjD,IAAAA,MAAAC;AAoEO,IAAM,uBAAN,eACEA,OAAA,iBAEkBD,OAAA,YAFlBC,MACT;EAGC,YAAYC,OAAc;AACzB,UAAMA,OAAM,UAAU,eAAe;AACrC,SAAK,OAAO,aAAa;EAC1B;;EAGS,MACR,OACiD;AACjD,WAAO,IAAI;MACV;MACA,KAAK;IACN;EACD;AACD;AAhBC,cAHY,sBAGcF,MAAsB;AAvEjD,IAAAA,MAAAC;AAyFO,IAAM,gBAAN,eAAmFA,OAAA,UAC/DD,OAAA,YAD+DC,MAAY;EAGrG,aAAqB;AACpB,WAAO;EACR;;EAGS,mBAAmB,OAAuB;AAClD,WAAO,OAAO,KAAK;EACpB;AACD;AAVC,cADY,eACcD,MAAsB;AAuB1C,SAAS,UAAU,GAA+B,GAAuB;AAC/E,QAAM,EAAE,MAAAE,OAAM,OAAO,IAAI,uBAA0C,GAAG,CAAC;AACvE,MAAI,OAAO,SAAS,UAAU;AAC7B,WAAO,IAAI,qBAAqBA,KAAI;EACrC;AACA,SAAO,IAAI,qBAAqBA,KAAI;AACrC;;;AC7HA,IAAAC,MAAAC;AAaO,IAAM,mBAAN,eAA0FA,OAAA,iBACtED,OAAA,YADsEC,MAAmB;EAGnH,YAAYC,OAAiB;AAC5B,UAAMA,OAAM,WAAW,WAAW;EACnC;;EAGS,MACR,OAC6C;AAC7C,WAAO,IAAI,UAA2C,OAAO,KAAK,MAA8C;EACjH;AACD;AAZC,cADY,kBACcF,MAAsB;AAdjD,IAAAA,MAAAC;AA4BO,IAAM,YAAN,eAA4EA,OAAA,UACxDD,OAAA,YADwDC,MAAY;EAG9F,aAAqB;AACpB,WAAO;EACR;AACD;AALC,cADY,WACcD,MAAsB;AAS1C,SAAS,QAAQE,OAAe;AACtC,SAAO,IAAI,iBAAiBA,SAAQ,EAAE;AACvC;;;ACxCA,IAAAC,MAAAC;AAmBO,IAAM,gBAAN,eACEA,OAAA,iBAMkBD,OAAA,YANlBC,MAKT;EAGC,YAAYC,OAAiB,QAAoD;AAChF,UAAMA,OAAM,UAAU,QAAQ;AAC9B,SAAK,OAAO,SAAS,OAAO;AAC5B,SAAK,OAAO,aAAa,OAAO;EACjC;;EAGS,MACR,OACoE;AACpE,WAAO,IAAI;MACV;MACA,KAAK;IACN;EACD;AACD;AAjBC,cAPY,eAOcF,MAAsB;AA1BjD,IAAAA,MAAAC;AA6CO,IAAM,SAAN,eACEA,OAAA,UAEkBD,OAAA,YAFlBC,MACT;EAFO;;AAKG,kCAAS,KAAK,OAAO;AACZ,sCAAa,KAAK,OAAO;;EAE3C,aAAqB;AACpB,WAAO,KAAK,WAAW,SAAY,SAAS,QAAQ,KAAK,MAAM;EAChE;AACD;AARC,cAHY,QAGcD,MAAsB;AA+B1C,SAAS,KAAK,GAA2B,IAAkB,CAAC,GAAQ;AAC1E,QAAM,EAAE,MAAAE,OAAM,OAAO,IAAI,uBAAqC,GAAG,CAAC;AAClE,SAAO,IAAI,cAAcA,OAAM,MAAa;AAC7C;;;AClFA,IAAAC,MAAAC;AAaO,IAAM,gBAAN,eAAmFA,OAAA,iBAC/DD,OAAA,YAD+DC,MAAmB;EAG5G,YAAYC,OAAiB;AAC5B,UAAMA,OAAM,UAAU,QAAQ;EAC/B;;EAGS,MACR,OAC0C;AAC1C,WAAO,IAAI,OAAwC,OAAO,KAAK,MAA8C;EAC9G;AACD;AAZC,cADY,eACcF,MAAsB;AAdjD,IAAAA,MAAAC;AA4BO,IAAM,SAAN,eAAqEA,OAAA,UACjDD,OAAA,YADiDC,MAAY;EAGvF,aAAqB;AACpB,WAAO;EACR;AACD;AALC,cADY,QACcD,MAAsB;AAS1C,SAAS,KAAKE,OAAe;AACnC,SAAO,IAAI,cAAcA,SAAQ,EAAE;AACpC;;;ACxCA,IAAAC,MAAAC;AAsBO,IAAM,wBAAN,eACEA,OAAA,iBAWkBD,OAAA,YAXlBC,MAUT;EAGC,YACCC,OACA,aACA,kBACC;AACD,UAAMA,OAAM,UAAU,gBAAgB;AACtC,SAAK,OAAO,cAAc;AAC1B,SAAK,OAAO,mBAAmB;EAChC;;EAGA,MACC,OACkD;AAClD,WAAO,IAAI;MACV;MACA,KAAK;IACN;EACD;AACD;AArBC,cAZY,uBAYcF,MAAsB;AAlCjD,IAAAA,MAAAC;AAyDO,IAAM,iBAAN,eAAqFA,OAAA,UACjED,OAAA,YADiEC,MAAY;EAOvG,YACC,OACA,QACC;AACD,UAAM,OAAO,MAAM;AARZ;AACA;AACA;AAOP,SAAK,UAAU,OAAO,iBAAiB,SAAS,OAAO,WAAW;AAClE,SAAK,QAAQ,OAAO,iBAAiB;AACrC,SAAK,UAAU,OAAO,iBAAiB;EACxC;EAEA,aAAqB;AACpB,WAAO,KAAK;EACb;EAES,mBAAmB,OAAoC;AAC/D,WAAO,OAAO,KAAK,YAAY,aAAa,KAAK,QAAQ,KAAK,IAAI;EACnE;EAES,iBAAiB,OAAoC;AAC7D,WAAO,OAAO,KAAK,UAAU,aAAa,KAAK,MAAM,KAAK,IAAI;EAC/D;AACD;AA3BC,cADY,gBACcD,MAAsB;AA8I1C,SAAS,WACf,kBAoBD;AACC,SAAO,CACN,GACA,MAC0D;AAC1D,UAAM,EAAE,MAAAE,OAAM,OAAO,IAAI,uBAAoC,GAAG,CAAC;AACjE,WAAO,IAAI,sBAAsBA,OAA+C,QAAQ,gBAAgB;EACzG;AACD;;;ACtOA,IAAAC,MAAAC;AAIO,IAAe,0BAAf,eAGGA,OAAA,iBACiBD,OAAA,YADjBC,MAAmC;EAG5C,aAAa;AACZ,WAAO,KAAK,QAAQ,UAAU;EAC/B;AACD;AALC,cAJqB,yBAIKD,MAAsB;;;ACPjD,IAAAE,MAAAC;AAeO,IAAM,gBAAN,eAAiFA,OAAA,yBAC7DD,OAAA,YAD6DC,MAA2B;EAGlH,YAAYC,OAAiB;AAC5B,UAAMA,OAAM,QAAQ,QAAQ;EAC7B;;EAGS,MACR,OAC0C;AAC1C,WAAO,IAAI,OAAwC,OAAO,KAAK,MAA8C;EAC9G;AACD;AAZC,cADY,eACcF,MAAsB;AAhBjD,IAAAA,MAAAC;AA8BO,IAAM,SAAN,eAAmEA,OAAA,UAC/CD,OAAA,YAD+CC,MAAY;EAGrF,aAAqB;AACpB,WAAO;EACR;EAES,mBAAmB,OAAqB;AAChD,WAAO,IAAI,KAAK,KAAK;EACtB;EAES,iBAAiB,OAAqB;AAC9C,WAAO,MAAM,YAAY;EAC1B;AACD;AAbC,cADY,QACcD,MAAsB;AA/BjD,IAAAA,MAAAC;AAuDO,IAAM,sBAAN,eACEA,OAAA,yBAEkBD,OAAA,YAFlBC,MACT;EAGC,YAAYC,OAAiB;AAC5B,UAAMA,OAAM,UAAU,cAAc;EACrC;;EAGS,MACR,OACgD;AAChD,WAAO,IAAI;MACV;MACA,KAAK;IACN;EACD;AACD;AAfC,cAHY,qBAGcF,MAAsB;AA1DjD,IAAAA,MAAAC;AA2EO,IAAM,eAAN,eAAiFA,OAAA,UAC7DD,OAAA,YAD6DC,MAAY;EAGnG,aAAqB;AACpB,WAAO;EACR;AACD;AALC,cADY,cACcD,MAAsB;AAmB1C,SAAS,KAAK,GAA2B,GAAkB;AACjE,QAAM,EAAE,MAAAE,OAAM,OAAO,IAAI,uBAAqC,GAAG,CAAC;AAClE,OAAI,iCAAQ,UAAS,QAAQ;AAC5B,WAAO,IAAI,cAAcA,KAAI;EAC9B;AACA,SAAO,IAAI,oBAAoBA,KAAI;AACpC;;;ACrGA,IAAAC,MAAAC;AAaO,IAAM,2BAAN,eACEA,OAAA,iBAEkBD,OAAA,YAFlBC,MACT;EAGC,YAAYC,OAAiB;AAC5B,UAAMA,OAAM,UAAU,mBAAmB;EAC1C;;EAGS,MACR,OACqD;AACrD,WAAO,IAAI;MACV;MACA,KAAK;IACN;EACD;AACD;AAfC,cAHY,0BAGcF,MAAsB;AAhBjD,IAAAA,MAAAC;AAiCO,IAAM,oBAAN,eAA2FA,OAAA,UACvED,OAAA,YADuEC,MAAY;EAG7G,aAAqB;AACpB,WAAO;EACR;EAES,mBAAmB,OAAgC;AAC3D,QAAI,OAAO,UAAU,UAAU;AAC9B,aAAO,OAAO,WAAW,KAAK;IAC/B;AACA,WAAO;EACR;AACD;AAZC,cADY,mBACcD,MAAsB;AAgB1C,SAAS,gBAAgBE,OAAe;AAC9C,SAAO,IAAI,yBAAyBA,SAAQ,EAAE;AAC/C;;;ACpDA,IAAAC,MAAAC;AAaO,IAAM,gBAAN,eAAmFA,OAAA,iBAC/DD,OAAA,YAD+DC,MAAmB;EAG5G,YAAYC,OAAiB;AAC5B,UAAMA,OAAM,UAAU,QAAQ;EAC/B;;EAGS,MACR,OAC0C;AAC1C,WAAO,IAAI,OAAwC,OAAO,KAAK,MAA8C;EAC9G;AACD;AAZC,cADY,eACcF,MAAsB;AAdjD,IAAAA,MAAAC;AA4BO,IAAM,SAAN,eAAqEA,OAAA,UACjDD,OAAA,YADiDC,MAAY;EAGvF,aAAqB;AACpB,WAAO;EACR;AACD;AALC,cADY,QACcD,MAAsB;AAS1C,SAAS,KAAKE,OAAe;AACnC,SAAO,IAAI,cAAcA,SAAQ,EAAE;AACpC;;;ACxCA,IAAAC,MAAAC;AAcO,IAAM,mBAAN,eACEA,OAAA,wBAEkBD,OAAA,YAFlBC,MACT;EAGC,YAAYC,OAAiB;AAC5B,UAAMA,OAAM,UAAU,WAAW;EAClC;;EAGS,MACR,OAC6C;AAC7C,WAAO,IAAI,UAA2C,OAAO,KAAK,MAA8C;EACjH;AACD;AAZC,cAHY,kBAGcF,MAAsB;AAjBjD,IAAAA,MAAAC;AA+BO,IAAM,YAAN,eAA2EA,OAAA,UACvDD,OAAA,YADuDC,MAAY;EAG7F,aAAqB;AACpB,WAAO;EACR;EAES,mBAAmB,OAAgC;AAC3D,QAAI,OAAO,UAAU,UAAU;AAC9B,aAAO,OAAO,SAAS,KAAK;IAC7B;AACA,WAAO;EACR;AACD;AAZC,cADY,WACcD,MAAsB;AAgB1C,SAAS,QAAQE,OAAe;AACtC,SAAO,IAAI,iBAAiBA,SAAQ,EAAE;AACvC;;;AClDA,IAAAC,MAAAC;AAeO,IAAM,oBAAN,eACEA,OAAA,iBAEkBD,OAAA,YAFlBC,MACT;EAGC,YACCC,OACA,gBACC;AACD,UAAMA,OAAM,UAAU,YAAY;AAClC,SAAK,OAAO,iBAAiB;EAC9B;;EAGS,MACR,OAC8C;AAC9C,WAAO,IAAI,WAA4C,OAAO,KAAK,MAA8C;EAClH;AACD;AAhBC,cAHY,mBAGcF,MAAsB;AAlBjD,IAAAA,MAAAC;AAoCO,IAAM,aAAN,eACEA,OAAA,UAEkBD,OAAA,YAFlBC,MACT;EAFO;;AAKG,kCAAmC,KAAK,OAAO,eAAe;AAC9D,qCAAyC,KAAK,OAAO,eAAe;;EAE7E,aAAqB;AACpB,UAAM,SAAS,KAAK,SAAS,IAAI,KAAK,MAAM,KAAK;AACjD,UAAM,YAAY,KAAK,YAAY,IAAI,KAAK,SAAS,MAAM;AAC3D,WAAO,WAAW,MAAM,GAAG,SAAS;EACrC;AACD;AAVC,cAHY,YAGcD,MAAsB;AAsC1C,SAAS,SAAS,GAA6B,IAAoB,CAAC,GAAG;AAC7E,QAAM,EAAE,MAAAE,OAAM,OAAO,IAAI,uBAAuC,GAAG,CAAC;AACpE,SAAO,IAAI,kBAAkBA,OAAM,MAAM;AAC1C;;;AChFA,IAAAC,MAAAC;AAaO,IAAM,gBAAN,eAAiFA,OAAA,iBAG7DD,OAAA,YAH6DC,MAEtF;EAGD,YAAYC,OAAiB;AAC5B,UAAMA,OAAM,QAAQ,QAAQ;EAC7B;;EAGS,MACR,OAC0C;AAC1C,WAAO,IAAI,OAAwC,OAAO,KAAK,MAA8C;EAC9G;AACD;AAZC,cAHY,eAGcF,MAAsB;AAhBjD,IAAAA,MAAAC;AA8BO,IAAM,SAAN,eAAmEA,OAAA,UAC/CD,OAAA,YAD+CC,MAAY;EAGrF,YAAY,OAA6C,QAAoC;AAC5F,UAAM,OAAO,MAAM;EACpB;EAEA,aAAqB;AACpB,WAAO;EACR;EAES,iBAAiB,OAA0B;AACnD,WAAO,KAAK,UAAU,KAAK;EAC5B;EAES,mBAAmB,OAAsC;AACjE,QAAI,OAAO,UAAU,UAAU;AAC9B,UAAI;AACH,eAAO,KAAK,MAAM,KAAK;MACxB,QAAQ;AACP,eAAO;MACR;IACD;AACA,WAAO;EACR;AACD;AAxBC,cADY,QACcD,MAAsB;AA4B1C,SAAS,KAAKE,OAAe;AACnC,SAAO,IAAI,cAAcA,SAAQ,EAAE;AACpC;;;AC7DA,IAAAC,MAAAC;AAaO,IAAM,iBAAN,eAAmFA,OAAA,iBAC/DD,OAAA,YAD+DC,MAAmB;EAG5G,YAAYC,OAAiB;AAC5B,UAAMA,OAAM,QAAQ,SAAS;EAC9B;;EAGS,MACR,OAC2C;AAC3C,WAAO,IAAI,QAAyC,OAAO,KAAK,MAA8C;EAC/G;AACD;AAZC,cADY,gBACcF,MAAsB;AAdjD,IAAAA,MAAAC;AA4BO,IAAM,UAAN,eAAqEA,OAAA,UACjDD,OAAA,YADiDC,MAAY;EAGvF,YAAY,OAA6C,QAAqC;AAC7F,UAAM,OAAO,MAAM;EACpB;EAEA,aAAqB;AACpB,WAAO;EACR;EAES,iBAAiB,OAA0B;AACnD,WAAO,KAAK,UAAU,KAAK;EAC5B;EAES,mBAAmB,OAAsC;AACjE,QAAI,OAAO,UAAU,UAAU;AAC9B,UAAI;AACH,eAAO,KAAK,MAAM,KAAK;MACxB,QAAQ;AACP,eAAO;MACR;IACD;AACA,WAAO;EACR;AACD;AAxBC,cADY,SACcD,MAAsB;AA4B1C,SAAS,MAAME,OAAe;AACpC,SAAO,IAAI,eAAeA,SAAQ,EAAE;AACrC;;;AC3DA,IAAAC,MAAAC;AAeO,IAAM,gBAAN,eAAkFA,OAAA,iBAC9DD,OAAA,YAD8DC,MAAmB;EAG3G,YAAYC,OAAiB;AAC5B,UAAMA,OAAM,SAAS,QAAQ;EAC9B;;EAGS,MACR,OAC+C;AAC/C,WAAO,IAAI;MACV;MACA,KAAK;IACN;EACD;AACD;AAfC,cADY,eACcF,MAAsB;AAhBjD,IAAAA,MAAAC;AAiCO,IAAM,cAAN,eAAyEA,OAAA,UACrDD,OAAA,YADqDC,MAAY;EAG3F,aAAqB;AACpB,WAAO;EACR;EAES,mBAAmB,OAAyC;AACpE,UAAM,CAAC,GAAG,GAAG,CAAC,IAAI,MAAM,MAAM,GAAG,EAAE,EAAE,MAAM,GAAG;AAC9C,WAAO,CAAC,OAAO,WAAW,CAAE,GAAG,OAAO,WAAW,CAAE,GAAG,OAAO,WAAW,CAAE,CAAC;EAC5E;EAES,iBAAiB,OAAyC;AAClE,WAAO,IAAI,MAAM,CAAC,CAAC,IAAI,MAAM,CAAC,CAAC,IAAI,MAAM,CAAC,CAAC;EAC5C;AACD;AAdC,cADY,aACcD,MAAsB;AAlCjD,IAAAA,MAAAC;AA2DO,IAAM,mBAAN,eAAuFA,OAAA,iBACnED,OAAA,YADmEC,MAAmB;EAGhH,YAAYC,OAAiB;AAC5B,UAAMA,OAAM,QAAQ,WAAW;EAChC;;EAGS,MACR,OAC6C;AAC7C,WAAO,IAAI;MACV;MACA,KAAK;IACN;EACD;AACD;AAfC,cADY,kBACcF,MAAsB;AA5DjD,IAAAA,MAAAC;AA6EO,IAAM,YAAN,eAAyEA,OAAA,UACrDD,OAAA,YADqDC,MAAY;EAG3F,aAAqB;AACpB,WAAO;EACR;EAES,mBAAmB,OAAoD;AAC/E,UAAM,CAAC,GAAG,GAAG,CAAC,IAAI,MAAM,MAAM,GAAG,EAAE,EAAE,MAAM,GAAG;AAC9C,WAAO,EAAE,GAAG,OAAO,WAAW,CAAE,GAAG,GAAG,OAAO,WAAW,CAAE,GAAG,GAAG,OAAO,WAAW,CAAE,EAAE;EACvF;EAES,iBAAiB,OAAoD;AAC7E,WAAO,IAAI,MAAM,CAAC,IAAI,MAAM,CAAC,IAAI,MAAM,CAAC;EACzC;AACD;AAdC,cADY,WACcD,MAAsB;AA8B1C,SAAS,KAAK,GAA+B,GAAsB;AACzE,QAAM,EAAE,MAAAE,OAAM,OAAO,IAAI,uBAAyC,GAAG,CAAC;AACtE,MAAI,EAAC,iCAAQ,SAAQ,OAAO,SAAS,SAAS;AAC7C,WAAO,IAAI,cAAcA,KAAI;EAC9B;AACA,SAAO,IAAI,iBAAiBA,KAAI;AACjC;;;AClHA,IAAAC,MAAAC;AAaO,IAAM,mBAAN,eAAyFA,OAAA,iBACrED,OAAA,YADqEC,MAAmB;EAGlH,YAAYC,OAAiB;AAC5B,UAAMA,OAAM,UAAU,WAAW;EAClC;;EAGS,MACR,OAC6C;AAC7C,WAAO,IAAI,UAA2C,OAAO,KAAK,MAA8C;EACjH;AACD;AAZC,cADY,kBACcF,MAAsB;AAdjD,IAAAA,MAAAC;AA4BO,IAAM,YAAN,eAA2EA,OAAA,UACvDD,OAAA,YADuDC,MAAY;EAG7F,aAAqB;AACpB,WAAO;EACR;AACD;AALC,cADY,WACcD,MAAsB;AAS1C,SAAS,QAAQE,OAAe;AACtC,SAAO,IAAI,iBAAiBA,SAAQ,EAAE;AACvC;;;ACxCA,IAAAC,MAAAC;AAaO,IAAM,oBAAN,eAA2FA,OAAA,iBACvED,OAAA,YADuEC,MAAmB;EAGpH,YAAYC,OAAiB;AAC5B,UAAMA,OAAM,UAAU,YAAY;EACnC;;EAGS,MACR,OAC8C;AAC9C,WAAO,IAAI,WAA4C,OAAO,KAAK,MAA8C;EAClH;AACD;AAZC,cADY,mBACcF,MAAsB;AAdjD,IAAAA,MAAAC;AA4BO,IAAM,aAAN,eAA6EA,OAAA,UACzDD,OAAA,YADyDC,MAAY;EAG/F,aAAqB;AACpB,WAAO;EACR;AACD;AALC,cADY,YACcD,MAAsB;AAS1C,SAAS,SAASE,OAAe;AACvC,SAAO,IAAI,kBAAkBA,SAAQ,EAAE;AACxC;;;ACxCA,IAAAC,MAAAC;AAcO,IAAM,mBAAN,eAAyFA,OAAA,iBAOrED,OAAA,YAPqEC,MAM9F;EAGD,YAAYC,OAAiB,WAAoB,OAAgB;AAChE,UAAMA,OAAM,UAAU,WAAW;AACjC,SAAK,OAAO,YAAY;AACxB,SAAK,OAAO,QAAQ;EACrB;;EAGS,MACR,OAC6C;AAC7C,WAAO,IAAI,UAA2C,OAAO,KAAK,MAA8C;EACjH;AACD;AAdC,cAPY,kBAOcF,MAAsB;AArBjD,IAAAA,MAAAC;AAqCO,IAAM,YAAN,eAA2EA,OAAA,UACvDD,OAAA,YADuDC,MAAY;EAM7F,YAAY,OAA6C,QAAuC;AAC/F,UAAM,OAAO,MAAM;AAJX;AACA;AAIR,SAAK,YAAY,OAAO;AACxB,SAAK,QAAQ,OAAO;EACrB;EAEA,aAAqB;AACpB,QAAI,KAAK,cAAc,UAAa,KAAK,UAAU,QAAW;AAC7D,aAAO,WAAW,KAAK,SAAS,KAAK,KAAK,KAAK;IAChD,WAAW,KAAK,cAAc,QAAW;AACxC,aAAO;IACR,OAAO;AACN,aAAO,WAAW,KAAK,SAAS;IACjC;EACD;AACD;AApBC,cADY,WACcD,MAAsB;AAmC1C,SAAS,QAAQ,GAA8B,GAAqB;AAC1E,QAAM,EAAE,MAAAE,OAAM,OAAO,IAAI,uBAAwC,GAAG,CAAC;AACrE,SAAO,IAAI,iBAAiBA,OAAM,iCAAQ,WAAW,iCAAQ,KAAK;AACnE;AAEO,IAAM,UAAU;;;AC9EvB,IAAAC,MAAAC;AAeO,IAAM,sBAAN,eACEA,OAAA,iBAEkBD,OAAA,YAFlBC,MACT;EAGC,YAAYC,OAAc;AACzB,UAAMA,OAAM,SAAS,cAAc;EACpC;;EAGS,MACR,OACgD;AAChD,WAAO,IAAI;MACV;MACA,KAAK;IACN;EACD;AACD;AAfC,cAHY,qBAGcF,MAAsB;AAlBjD,IAAAA,MAAAC;AAmCO,IAAM,eAAN,eAAgFA,OAAA,UAC5DD,OAAA,YAD4DC,MAAY;EAGlG,aAAqB;AACpB,WAAO;EACR;EAES,mBAAmB,OAA4D;AACvF,QAAI,OAAO,UAAU,UAAU;AAC9B,YAAM,CAAC,GAAG,CAAC,IAAI,MAAM,MAAM,GAAG,EAAE,EAAE,MAAM,GAAG;AAC3C,aAAO,CAAC,OAAO,WAAW,CAAE,GAAG,OAAO,WAAW,CAAE,CAAC;IACrD;AACA,WAAO,CAAC,MAAM,GAAG,MAAM,CAAC;EACzB;EAES,iBAAiB,OAAiC;AAC1D,WAAO,IAAI,MAAM,CAAC,CAAC,IAAI,MAAM,CAAC,CAAC;EAChC;AACD;AAjBC,cADY,cACcD,MAAsB;AApCjD,IAAAA,MAAAC;AAgEO,IAAM,uBAAN,eACEA,OAAA,iBAEkBD,OAAA,YAFlBC,MACT;EAGC,YAAYC,OAAc;AACzB,UAAMA,OAAM,QAAQ,eAAe;EACpC;;EAGS,MACR,OACiD;AACjD,WAAO,IAAI;MACV;MACA,KAAK;IACN;EACD;AACD;AAfC,cAHY,sBAGcF,MAAsB;AAnEjD,IAAAA,MAAAC;AAoFO,IAAM,gBAAN,eAAiFA,OAAA,UAC7DD,OAAA,YAD6DC,MAAY;EAGnG,aAAqB;AACpB,WAAO;EACR;EAES,mBAAmB,OAAoE;AAC/F,QAAI,OAAO,UAAU,UAAU;AAC9B,YAAM,CAAC,GAAG,CAAC,IAAI,MAAM,MAAM,GAAG,EAAE,EAAE,MAAM,GAAG;AAC3C,aAAO,EAAE,GAAG,OAAO,WAAW,CAAE,GAAG,GAAG,OAAO,WAAW,CAAE,EAAE;IAC7D;AACA,WAAO;EACR;EAES,iBAAiB,OAAyC;AAClE,WAAO,IAAI,MAAM,CAAC,IAAI,MAAM,CAAC;EAC9B;AACD;AAjBC,cADY,eACcD,MAAsB;AAiC1C,SAAS,MAAM,GAA4B,GAAmB;AACpE,QAAM,EAAE,MAAAE,OAAM,OAAO,IAAI,uBAAsC,GAAG,CAAC;AACnE,MAAI,EAAC,iCAAQ,SAAQ,OAAO,SAAS,SAAS;AAC7C,WAAO,IAAI,oBAAoBA,KAAI;EACpC;AACA,SAAO,IAAI,qBAAqBA,KAAI;AACrC;;;AC9HA,SAAS,WAAW,KAAyB;AAC5C,QAAM,QAAkB,CAAC;AACzB,WAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK,GAAG;AACvC,UAAM,KAAK,OAAO,SAAS,IAAI,MAAM,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;EACpD;AACA,SAAO,IAAI,WAAW,KAAK;AAC5B;AAEA,SAAS,eAAe,OAAmB,QAAwB;AAClE,QAAM,SAAS,IAAI,YAAY,CAAC;AAChC,QAAM,OAAO,IAAI,SAAS,MAAM;AAChC,WAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC3B,SAAK,SAAS,GAAG,MAAM,SAAS,CAAC,CAAE;EACpC;AACA,SAAO,KAAK,WAAW,GAAG,IAAI;AAC/B;AAEO,SAAS,UAAU,KAA+B;AACxD,QAAM,QAAQ,WAAW,GAAG;AAE5B,MAAI,SAAS;AAGb,QAAM,YAAY,MAAM,MAAM;AAC9B,YAAU;AAEV,QAAM,OAAO,IAAI,SAAS,MAAM,MAAM;AACtC,QAAM,WAAW,KAAK,UAAU,QAAQ,cAAc,CAAC;AACvD,YAAU;AAEV,MAAI;AACJ,MAAI,WAAW,WAAY;AAC1B,YAAQ,KAAK,UAAU,QAAQ,cAAc,CAAC;AAC9C,cAAU;EACX;AAEA,OAAK,WAAW,WAAY,GAAG;AAC9B,UAAM,IAAI,eAAe,OAAO,MAAM;AACtC,cAAU;AACV,UAAM,IAAI,eAAe,OAAO,MAAM;AACtC,cAAU;AAEV,WAAO,CAAC,GAAG,CAAC;EACb;AAEA,QAAM,IAAI,MAAM,2BAA2B;AAC5C;;;AC5CA,IAAAC,MAAAC;AAgBO,IAAM,oBAAN,eAA0FA,OAAA,iBACtED,OAAA,YADsEC,MAAmB;EAGnH,YAAYC,OAAiB;AAC5B,UAAMA,OAAM,SAAS,YAAY;EAClC;;EAGS,MACR,OAC8C;AAC9C,WAAO,IAAI;MACV;MACA,KAAK;IACN;EACD;AACD;AAfC,cADY,mBACcF,MAAsB;AAjBjD,IAAAA,MAAAC;AAkCO,IAAM,aAAN,eAA4EA,OAAA,UACxDD,OAAA,YADwDC,MAAY;EAG9F,aAAqB;AACpB,WAAO;EACR;EAES,mBAAmB,OAAiC;AAC5D,WAAO,UAAU,KAAK;EACvB;EAES,iBAAiB,OAAiC;AAC1D,WAAO,SAAS,MAAM,CAAC,CAAC,IAAI,MAAM,CAAC,CAAC;EACrC;AACD;AAbC,cADY,YACcD,MAAsB;AAnCjD,IAAAA,MAAAC;AA2DO,IAAM,0BAAN,eACEA,OAAA,iBAEkBD,OAAA,YAFlBC,MACT;EAGC,YAAYC,OAAiB;AAC5B,UAAMA,OAAM,QAAQ,kBAAkB;EACvC;;EAGS,MACR,OACoD;AACpD,WAAO,IAAI;MACV;MACA,KAAK;IACN;EACD;AACD;AAfC,cAHY,yBAGcF,MAAsB;AA9DjD,IAAAA,MAAAC;AA+EO,IAAM,mBAAN,eAAuFA,OAAA,UACnED,OAAA,YADmEC,MAAY;EAGzG,aAAqB;AACpB,WAAO;EACR;EAES,mBAAmB,OAAyC;AACpE,UAAM,SAAS,UAAU,KAAK;AAC9B,WAAO,EAAE,GAAG,OAAO,CAAC,GAAG,GAAG,OAAO,CAAC,EAAE;EACrC;EAES,iBAAiB,OAAyC;AAClE,WAAO,SAAS,MAAM,CAAC,IAAI,MAAM,CAAC;EACnC;AACD;AAdC,cADY,kBACcD,MAAsB;AA8B1C,SAAS,SAAS,GAA+B,GAAsB;AAC7E,QAAM,EAAE,MAAAE,OAAM,OAAO,IAAI,uBAAyC,GAAG,CAAC;AACtE,MAAI,EAAC,iCAAQ,SAAQ,OAAO,SAAS,SAAS;AAC7C,WAAO,IAAI,kBAAkBA,KAAI;EAClC;AACA,SAAO,IAAI,wBAAwBA,KAAI;AACxC;;;ACpHA,IAAAC,MAAAC;AAaO,IAAM,gBAAN,eAAmFA,OAAA,iBAI/DD,OAAA,YAJ+DC,MAGxF;EAGD,YAAYC,OAAiB,QAAiB;AAC7C,UAAMA,OAAM,UAAU,QAAQ;AAC9B,SAAK,OAAO,SAAS;EACtB;;EAGS,MACR,OAC0C;AAC1C,WAAO,IAAI,OAAwC,OAAO,KAAK,MAA8C;EAC9G;AACD;AAbC,cAJY,eAIcF,MAAsB;AAjBjD,IAAAA,MAAAC;AAgCO,IAAM,SAAN,eAAqEA,OAAA,UACjDD,OAAA,YADiDC,MAAY;EAGvF,YAAY,OAA6C,QAAoC;AAC5F,UAAM,OAAO,MAAM;AAOX,8CAAqB,CAAC,UAAmC;AACjE,UAAI,OAAO,UAAU,UAAU;AAC9B,eAAO,OAAO,WAAW,KAAK;MAC/B;AACA,aAAO;IACR;EAXA;EAEA,aAAqB;AACpB,WAAO;EACR;AAQD;AAhBC,cADY,QACcD,MAAsB;AAoB1C,SAAS,KAAKE,OAAe;AACnC,SAAO,IAAI,cAAcA,SAAQ,EAAE;AACpC;;;ACjDA,IAAAC,MAAAC;AAiBO,IAAM,kBAAN,eAAuFA,OAAA,iBACnED,OAAA,YADmEC,MAAmB;EAGhH,YAAYC,OAAiB;AAC5B,UAAMA,OAAM,UAAU,UAAU;AAChC,SAAK,OAAO,aAAa;AACzB,SAAK,OAAO,UAAU;EACvB;;EAGS,MACR,OAC4C;AAC5C,WAAO,IAAI,SAA0C,OAAO,KAAK,MAA8C;EAChH;AACD;AAdC,cADY,iBACcF,MAAsB;AAlBjD,IAAAA,MAAAC;AAkCO,IAAM,WAAN,eAAyEA,OAAA,UACrDD,OAAA,YADqDC,MAAY;EAG3F,aAAqB;AACpB,WAAO;EACR;AACD;AALC,cADY,UACcD,MAAsB;AAS1C,SAAS,OAAOE,OAAe;AACrC,SAAO,IAAI,gBAAgBA,SAAQ,EAAE;AACtC;;;ACpDA,IAAAC,MAAAC;AAcO,IAAM,oBAAN,eACEA,OAAA,wBAEkBD,OAAA,YAFlBC,MACT;EAGC,YAAYC,OAAiB;AAC5B,UAAMA,OAAM,UAAU,YAAY;EACnC;;EAGS,MACR,OAC8C;AAC9C,WAAO,IAAI,WAA4C,OAAO,KAAK,MAA8C;EAClH;AACD;AAZC,cAHY,mBAGcF,MAAsB;AAjBjD,IAAAA,MAAAC;AA+BO,IAAM,aAAN,eAA6EA,OAAA,UACzDD,OAAA,YADyDC,MAAY;EAAzF;;AAOG,8CAAqB,CAAC,UAAmC;AACjE,UAAI,OAAO,UAAU,UAAU;AAC9B,eAAO,OAAO,KAAK;MACpB;AACA,aAAO;IACR;;EATA,aAAqB;AACpB,WAAO;EACR;AAQD;AAZC,cADY,YACcD,MAAsB;AAgB1C,SAAS,SAASE,OAAe;AACvC,SAAO,IAAI,kBAAkBA,SAAQ,EAAE;AACxC;;;AC5CA,IAAAC,MAAAC;AAiBO,IAAM,uBAAN,eACEA,OAAA,iBAEkBD,OAAA,YAFlBC,MACT;EAGC,YAAYC,OAAiB;AAC5B,UAAMA,OAAM,UAAU,eAAe;AACrC,SAAK,OAAO,aAAa;AACzB,SAAK,OAAO,UAAU;EACvB;;EAGS,MACR,OACiD;AACjD,WAAO,IAAI;MACV;MACA,KAAK;IACN;EACD;AACD;AAjBC,cAHY,sBAGcF,MAAsB;AApBjD,IAAAA,MAAAC;AAuCO,IAAM,gBAAN,eAAmFA,OAAA,UAC/DD,OAAA,YAD+DC,MAAY;EAGrG,aAAqB;AACpB,WAAO;EACR;AACD;AALC,cADY,eACcD,MAAsB;AAS1C,SAAS,YAAYE,OAAe;AAC1C,SAAO,IAAI,qBAAqBA,SAAQ,EAAE;AAC3C;;;ACzDA,IAAAC,MAAAC;AAcO,IAAM,gBAAN,eAEGA,OAAA,iBACiBD,OAAA,YADjBC,MAAoD;EAG7D,YACCC,OACA,QACC;AACD,UAAMA,OAAM,UAAU,QAAQ;AAC9B,SAAK,OAAO,aAAa,OAAO;EACjC;;EAGS,MACR,OAC0C;AAC1C,WAAO,IAAI,OAAwC,OAAO,KAAK,MAA8C;EAC9G;AACD;AAhBC,cAHY,eAGcF,MAAsB;AAjBjD,IAAAA,MAAAC;AAmCO,IAAM,SAAN,eACEA,OAAA,UAEkBD,OAAA,YAFlBC,MACT;EAFO;;AAKY,sCAAa,KAAK,OAAO;;EAE3C,aAAqB;AACpB,WAAO;EACR;AACD;AAPC,cAHY,QAGcD,MAAsB;AAuB1C,SAAS,KAAK,GAA2B,IAAkB,CAAC,GAAQ;AAC1E,QAAM,EAAE,MAAAE,OAAM,OAAO,IAAI,uBAAqC,GAAG,CAAC;AAClE,SAAO,IAAI,cAAcA,OAAM,MAAa;AAC7C;;;AChEA,IAAAC,MAAAC;AAgBO,IAAM,gBAAN,eAAmFA,OAAA,yBAI/DD,OAAA,YAJ+DC,MAGxF;EAGD,YACCC,OACS,cACA,WACR;AACD,UAAMA,OAAM,UAAU,QAAQ;AAHrB,SAAA,eAAA;AACA,SAAA,YAAA;AAGT,SAAK,OAAO,eAAe;AAC3B,SAAK,OAAO,YAAY;EACzB;;EAGS,MACR,OAC0C;AAC1C,WAAO,IAAI,OAAwC,OAAO,KAAK,MAA8C;EAC9G;AACD;AAlBC,cAJY,eAIcF,MAAsB;AApBjD,IAAAA,MAAAC;AAwCO,IAAM,SAAN,eAAqEA,OAAA,UACjDD,OAAA,YADiDC,MAAY;EAMvF,YAAY,OAA6C,QAAoC;AAC5F,UAAM,OAAO,MAAM;AAJX;AACA;AAIR,SAAK,eAAe,OAAO;AAC3B,SAAK,YAAY,OAAO;EACzB;EAEA,aAAqB;AACpB,UAAM,YAAY,KAAK,cAAc,SAAY,KAAK,IAAI,KAAK,SAAS;AACxE,WAAO,OAAO,SAAS,GAAG,KAAK,eAAe,oBAAoB,EAAE;EACrE;AACD;AAfC,cADY,QACcD,MAAsB;AAyB1C,SAAS,KAAK,GAAyB,IAAgB,CAAC,GAAG;AACjE,QAAM,EAAE,MAAAE,OAAM,OAAO,IAAI,uBAAmC,GAAG,CAAC;AAChE,SAAO,IAAI,cAAcA,OAAM,OAAO,gBAAgB,OAAO,OAAO,SAAS;AAC9E;;;ACrEA,IAAAC,MAAAC;AAeO,IAAM,qBAAN,eACEA,OAAA,yBAKkBD,OAAA,YALlBC,MAIT;EAGC,YACCC,OACA,cACA,WACC;AACD,UAAMA,OAAM,QAAQ,aAAa;AACjC,SAAK,OAAO,eAAe;AAC3B,SAAK,OAAO,YAAY;EACzB;;EAGS,MACR,OAC+C;AAC/C,WAAO,IAAI,YAA6C,OAAO,KAAK,MAA8C;EACnH;AACD;AAlBC,cANY,oBAMcF,MAAsB;AArBjD,IAAAA,MAAAC;AAyCO,IAAM,cAAN,eAA6EA,OAAA,UACzDD,OAAA,YADyDC,MAAY;EAM/F,YAAY,OAA6C,QAAyC;AACjG,UAAM,OAAO,MAAM;AAJX;AACA;AAaA,8CAAqB,CAAC,UAA+B;AAC7D,aAAO,IAAI,KAAK,KAAK,eAAe,QAAQ,QAAQ,OAAO;IAC5D;AAES,4CAAmB,CAAC,UAAwB;AACpD,aAAO,MAAM,YAAY;IAC1B;AAfC,SAAK,eAAe,OAAO;AAC3B,SAAK,YAAY,OAAO;EACzB;EAEA,aAAqB;AACpB,UAAM,YAAY,KAAK,cAAc,SAAY,KAAK,KAAK,KAAK,SAAS;AACzE,WAAO,YAAY,SAAS,GAAG,KAAK,eAAe,oBAAoB,EAAE;EAC1E;AASD;AAvBC,cADY,aACcD,MAAsB;AA1CjD,IAAAA,MAAAC;AA4EO,IAAM,2BAAN,eACEA,OAAA,yBAKkBD,OAAA,YALlBC,MAIT;EAGC,YACCC,OACA,cACA,WACC;AACD,UAAMA,OAAM,UAAU,mBAAmB;AACzC,SAAK,OAAO,eAAe;AAC3B,SAAK,OAAO,YAAY;EACzB;;EAGS,MACR,OACqD;AACrD,WAAO,IAAI;MACV;MACA,KAAK;IACN;EACD;AACD;AArBC,cANY,0BAMcF,MAAsB;AAlFjD,IAAAA,MAAAC;AAyGO,IAAM,oBAAN,eAA2FA,OAAA,UACvED,OAAA,YADuEC,MAAY;EAM7G,YAAY,OAA6C,QAA+C;AACvG,UAAM,OAAO,MAAM;AAJX;AACA;AAIR,SAAK,eAAe,OAAO;AAC3B,SAAK,YAAY,OAAO;EACzB;EAEA,aAAqB;AACpB,UAAM,YAAY,KAAK,cAAc,SAAY,KAAK,IAAI,KAAK,SAAS;AACxE,WAAO,YAAY,SAAS,GAAG,KAAK,eAAe,oBAAoB,EAAE;EAC1E;AACD;AAfC,cADY,mBACcD,MAAsB;AAiC1C,SAAS,UAAU,GAAgC,IAAuB,CAAC,GAAG;AACpF,QAAM,EAAE,MAAAE,OAAM,OAAO,IAAI,uBAAsD,GAAG,CAAC;AACnF,OAAI,iCAAQ,UAAS,UAAU;AAC9B,WAAO,IAAI,yBAAyBA,OAAM,OAAO,gBAAgB,OAAO,OAAO,SAAS;EACzF;AACA,SAAO,IAAI,mBAAmBA,QAAM,iCAAQ,iBAAgB,OAAO,iCAAQ,SAAS;AACrF;;;ACjJA,IAAAC,OAAAC;AAcO,IAAM,gBAAN,eAAmFA,OAAA,iBAC/DD,QAAA,YAD+DC,MAAmB;EAG5G,YAAYC,OAAiB;AAC5B,UAAMA,OAAM,UAAU,QAAQ;EAC/B;;;;EAKA,gBAA6C;AAC5C,WAAO,KAAK,QAAQ,sBAAsB;EAC3C;;EAGS,MACR,OAC0C;AAC1C,WAAO,IAAI,OAAwC,OAAO,KAAK,MAA8C;EAC9G;AACD;AAnBC,cADY,eACcF,OAAsB;AAfjD,IAAAA,OAAAC;AAoCO,IAAM,SAAN,eAAqEA,OAAA,UACjDD,QAAA,YADiDC,MAAY;EAGvF,aAAqB;AACpB,WAAO;EACR;AACD;AALC,cADY,QACcD,OAAsB;AAS1C,SAAS,KAAKE,OAAe;AACnC,SAAO,IAAI,cAAcA,SAAQ,EAAE;AACpC;;;AChDA,IAAAC,OAAAC;AAmBO,IAAM,mBAAN,eAEGA,OAAA,iBAKiBD,QAAA,YALjBC,MAIR;EAGD,YAAYC,OAAiB,QAAuD;AACnF,UAAMA,OAAM,UAAU,WAAW;AACjC,SAAK,OAAO,SAAS,OAAO;AAC5B,SAAK,OAAO,aAAa,OAAO;EACjC;;EAGS,MACR,OACuE;AACvE,WAAO,IAAI;MACV;MACA,KAAK;IACN;EACD;AACD;AAjBC,cAPY,kBAOcF,OAAsB;AA1BjD,IAAAA,OAAAC;AA6CO,IAAM,YAAN,eACEA,OAAA,UAEkBD,QAAA,YAFlBC,MACT;EAFO;;AAKG,kCAAS,KAAK,OAAO;AACZ,sCAAa,KAAK,OAAO;;EAE3C,aAAqB;AACpB,WAAO,KAAK,WAAW,SAAY,YAAY,WAAW,KAAK,MAAM;EACtE;AACD;AARC,cAHY,WAGcD,OAAsB;AAmC1C,SAAS,QAAQ,GAA8B,IAAqB,CAAC,GAAQ;AACnF,QAAM,EAAE,MAAAE,OAAM,OAAO,IAAI,uBAAwC,GAAG,CAAC;AACrE,SAAO,IAAI,iBAAiBA,OAAM,MAAa;AAChD;;;ACtFA,IAAAC,OAAAC;AAeO,IAAM,wBAAN,eAEGA,OAAA,iBAIiBD,QAAA,YAJjBC,MAGR;EAGD,YAAYC,OAAc,QAA+C;AACxE,UAAMA,OAAM,UAAU,gBAAgB;AACtC,SAAK,OAAO,aAAa,OAAO;EACjC;;EAGS,MACR,OACoF;AACpF,WAAO,IAAI;MACV;MACA,KAAK;IACN;EACD;AACD;AAhBC,cANY,uBAMcF,OAAsB;AArBjD,IAAAA,OAAAC;AAuCO,IAAM,iBAAN,eACEA,OAAA,UAEkBD,QAAA,YAFlBC,MACT;EAFO;;AAKG,sCAAa,KAAK,OAAO;;EAElC,aAAqB;AACpB,WAAO,OAAO,KAAK,UAAU;EAC9B;AACD;AAPC,cAHY,gBAGcD,OAAsB;AAoB1C,SAAS,IAAI,GAAkC,GAA0B;AAC/E,QAAM,EAAE,MAAAE,OAAM,OAAO,IAAI,uBAA6C,GAAG,CAAC;AAC1E,SAAO,IAAI,sBAAsBA,OAAM,MAAM;AAC9C;;;ACjEA,IAAAC,OAAAC;AAeO,IAAM,sBAAN,eACEA,OAAA,iBAMkBD,QAAA,YANlBC,MAKT;EAGC,YAAYC,OAAc,QAA6C;AACtE,UAAMA,OAAM,SAAS,cAAc;AACnC,SAAK,OAAO,aAAa,OAAO;EACjC;;EAGS,MACR,OACkF;AAClF,WAAO,IAAI;MACV;MACA,KAAK;IACN;EACD;AACD;AAhBC,cAPY,qBAOcF,OAAsB;AAtBjD,IAAAA,OAAAC;AAwCO,IAAM,eAAN,eACEA,OAAA,UAEkBD,QAAA,YAFlBC,MACT;EAFO;;AAKG,sCAA8B,KAAK,OAAO;;EAEnD,aAAqB;AACpB,WAAO,WAAW,KAAK,UAAU;EAClC;EAES,iBAAiB,OAAyB;AAClD,WAAO,KAAK,UAAU,KAAK;EAC5B;EAES,mBAAmB,OAAwB;AACnD,WAAO,MACL,MAAM,GAAG,EAAE,EACX,MAAM,GAAG,EACT,IAAI,CAAC,MAAM,OAAO,WAAW,CAAC,CAAC;EAClC;AACD;AAlBC,cAHY,cAGcD,OAAsB;AA+B1C,SAAS,QAAQ,GAAgC,GAAwB;AAC/E,QAAM,EAAE,MAAAE,OAAM,OAAO,IAAI,uBAA2C,GAAG,CAAC;AACxE,SAAO,IAAI,oBAAoBA,OAAM,MAAM;AAC5C;;;AC7EA,IAAAC,OAAAC;AAcO,IAAM,wBAAN,eACEA,OAAA,iBAKkBD,QAAA,YALlBC,MAIT;EAGC,YAAYC,OAAc,QAA8B;AACvD,UAAMA,OAAM,UAAU,gBAAgB;AACtC,SAAK,OAAO,aAAa,OAAO;EACjC;;EAGS,MACR,OACkD;AAClD,WAAO,IAAI;MACV;MACA,KAAK;IACN;EACD;AACD;AAhBC,cANY,uBAMcF,OAAsB;AApBjD,IAAAA,OAAAC;AAsCO,IAAM,iBAAN,eACEA,OAAA,UAEkBD,QAAA,YAFlBC,MACT;EAFO;;AAKG,sCAAa,KAAK,OAAO;;EAElC,aAAqB;AACpB,WAAO,aAAa,KAAK,UAAU;EACpC;AACD;AAPC,cAHY,gBAGcD,OAAsB;AAoB1C,SAAS,UAAU,GAAkC,GAA0B;AACrF,QAAM,EAAE,MAAAE,OAAM,OAAO,IAAI,uBAA6C,GAAG,CAAC;AAC1E,SAAO,IAAI,sBAAsBA,OAAM,MAAM;AAC9C;;;AChEA,IAAAC,OAAAC;AAeO,IAAM,kBAAN,eACEA,OAAA,iBAMkBD,QAAA,YANlBC,MAKT;EAGC,YAAYC,OAAc,QAAyC;AAClE,UAAMA,OAAM,SAAS,UAAU;AAC/B,SAAK,OAAO,aAAa,OAAO;EACjC;;EAGS,MACR,OAC8E;AAC9E,WAAO,IAAI;MACV;MACA,KAAK;IACN;EACD;AACD;AAhBC,cAPY,iBAOcF,OAAsB;AAtBjD,IAAAA,OAAAC;AAwCO,IAAM,WAAN,eACEA,OAAA,UAEkBD,QAAA,YAFlBC,MACT;EAFO;;AAKG,sCAA8B,KAAK,OAAO;;EAEnD,aAAqB;AACpB,WAAO,UAAU,KAAK,UAAU;EACjC;EAES,iBAAiB,OAAyB;AAClD,WAAO,KAAK,UAAU,KAAK;EAC5B;EAES,mBAAmB,OAAwB;AACnD,WAAO,MACL,MAAM,GAAG,EAAE,EACX,MAAM,GAAG,EACT,IAAI,CAAC,MAAM,OAAO,WAAW,CAAC,CAAC;EAClC;AACD;AAlBC,cAHY,UAGcD,OAAsB;AA+B1C,SAAS,OAAO,GAA4B,GAAoB;AACtE,QAAM,EAAE,MAAAE,OAAM,OAAO,IAAI,uBAAuC,GAAG,CAAC;AACpE,SAAO,IAAI,gBAAgBA,OAAM,MAAM;AACxC;;;AC9CO,SAAS,sBAAsB;AACrC,SAAO;IACN;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EACD;AACD;;;ACxCO,IAAM,oBAAoB,OAAO,IAAI,6BAA6B;AAElE,IAAM,YAAY,OAAO,IAAI,mBAAmB;AA7BvD,IAAAC,OAAAC,MAAAC,KAAAC,KAAAC;AA+BO,IAAM,UAAN,eAA2DA,MAAA,OACvCD,MAAA,YASzBD,MAAA,mBAGAD,OAAA,WAGSD,QAAA,MAAM,OAAO,oBAhB0CI,KAAS;EAApE;;AAUN;wBAACF,KAAmC,CAAC;AAGrC;wBAACD,MAAsB;AAGvB;wBAAUD;;AAEX;AAjBC,cADY,SACcG,KAAsB;;AAGhD,cAJY,SAIa,UAAS,OAAO,OAAO,CAAC,GAAG,MAAM,QAAQ;EACjE;EACA;AACD,CAAC;AA4BK,SAAS,kBAKfE,OACA,SACA,aAGA,QACA,WAAWA,OAMT;AACF,QAAM,WAAW,IAAI,QAKlBA,OAAM,QAAQ,QAAQ;AAEzB,QAAM,gBAA6B,OAAO,YAAY,aAAa,QAAQ,oBAAoB,CAAC,IAAI;AAEpG,QAAM,eAAe,OAAO;IAC3B,OAAO,QAAQ,aAAa,EAAE,IAAI,CAAC,CAACA,QAAM,cAAc,MAAM;AAC7D,YAAM,aAAa;AACnB,iBAAW,QAAQA,MAAI;AACvB,YAAM,SAAS,WAAW,MAAM,QAAQ;AACxC,eAAS,iBAAiB,EAAE,KAAK,GAAG,WAAW,iBAAiB,QAAQ,QAAQ,CAAC;AACjF,aAAO,CAACA,QAAM,MAAM;IACrB,CAAC;EACF;AAEA,QAAM,6BAA6B,OAAO;IACzC,OAAO,QAAQ,aAAa,EAAE,IAAI,CAAC,CAACA,QAAM,cAAc,MAAM;AAC7D,YAAM,aAAa;AACnB,iBAAW,QAAQA,MAAI;AACvB,YAAM,SAAS,WAAW,uBAAuB,QAAQ;AACzD,aAAO,CAACA,QAAM,MAAM;IACrB,CAAC;EACF;AAEA,QAAM,QAAQ,OAAO,OAAO,UAAU,YAAY;AAElD,QAAM,MAAM,OAAO,OAAO,IAAI;AAC9B,QAAM,MAAM,OAAO,kBAAkB,IAAI;AAEzC,MAAI,aAAa;AAChB,UAAM,QAAQ,OAAO,kBAAkB,IAAI;EAC5C;AAEA,SAAO,OAAO,OAAO,OAAO;IAC3B,WAAW,MAAM;AAChB,YAAM,QAAQ,OAAO,SAAS,IAAI;AAClC,aAAO;IAMR;EACD,CAAC;AACF;AA2GO,IAAM,UAAqB,CAACA,OAAM,SAAS,gBAAgB;AACjE,SAAO,kBAAkBA,OAAM,SAAS,aAAa,MAAS;AAC/D;AAEO,SAAS,eAAe,oBAAyD;AACvF,SAAO,CAACA,OAAM,SAAS,gBAAgB;AACtC,WAAO,kBAAkB,mBAAmBA,KAAI,GAAkB,SAAS,aAAa,QAAWA,KAAI;EACxG;AACD;;;ACvOO,SAAS,cAAc,QAAa;AAC1C,MAAI,OAAO,CAAC,EAAE,SAAS;AACtB,WAAO,IAAI,kBAAkB,OAAO,CAAC,EAAE,SAAS,OAAO,CAAC,EAAE,IAAI;EAC/D;AACA,SAAO,IAAI,kBAAkB,MAAM;AACpC;AAtBA,IAAAC;AAyBkBA,QAAA;AADX,IAAM,oBAAN,MAAwB;EAS9B,YACC,SACAC,OACC;AARF;;AAGA;;AAMC,SAAK,UAAU;AACf,SAAK,OAAOA;EACb;;EAGA,MAAM,OAA4B;AACjC,WAAO,IAAI,WAAW,OAAO,KAAK,SAAS,KAAK,IAAI;EACrD;AACD;AApBC,cADY,mBACKD,OAAsB;AAzBxC,IAAAA;AAgDkBA,QAAA;AADX,IAAM,aAAN,MAAiB;EAMvB,YAAqB,OAAgB,SAA4BC,OAAe;AAHvE;AACA;AAEY,SAAA,QAAA;AACpB,SAAK,UAAU;AACf,SAAK,OAAOA;EACb;EAEA,UAAkB;AACjB,WAAO,KAAK,QAAQ,GAAG,KAAK,MAAM,QAAQ,OAAO,IAAI,CAAC,IAAI,KAAK,QAAQ,IAAI,CAAC,WAAW,OAAO,IAAI,EAAE,KAAK,GAAG,CAAC;EAC9G;AACD;AAbC,cADY,YACKD,OAAsB;;;AChDxC,IAAAE;AAiCkBA,QAAA;AADX,IAAe,WAAf,MAA4D;EAOlE,YACU,aACA,iBACA,cACR;AAPO;AACT;AAGU,SAAA,cAAA;AACA,SAAA,kBAAA;AACA,SAAA,eAAA;AAET,SAAK,sBAAsB,gBAAgB,MAAM,OAAO,IAAI;EAC7D;AAGD;AAfC,cADqB,UACJA,OAAsB;AAjCxC,IAAAA;AAsDkBA,QAAA;AAJX,IAAM,YAAN,MAGL;EAKD,YACU,OACA,QACR;AAFQ,SAAA,QAAA;AACA,SAAA,SAAA;EACP;AACJ;AARC,cAJY,WAIKA,OAAsB;AAtDxC,IAAAA,OAAAC;AAgEO,IAAM,OAAN,MAAM,cAGHA,OAAA,UACiBD,QAAA,YADjBC,MAAqB;EAK9B,YACC,aACA,iBACS,QAOA,YACR;AACD,UAAM,aAAa,iBAAiB,iCAAQ,YAAY;AAT/C,SAAA,SAAA;AAOA,SAAA,aAAA;EAGV;EAEA,cAAc,WAAoC;AACjD,UAAM,WAAW,IAAI;MACpB,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;IACN;AACA,aAAS,YAAY;AACrB,WAAO;EACR;AACD;AA7BC,cAJY,MAIcD,OAAsB;AAJ1C,IAAM,MAAN;AAhEP,IAAAA,OAAAC;AAmGO,IAAM,QAAN,MAAM,eAAwCA,OAAA,UAC1BD,QAAA,YAD0BC,MAAqB;EAKzE,YACC,aACA,iBACS,QACR;AACD,UAAM,aAAa,iBAAiB,iCAAQ,YAAY;AAF/C,SAAA,SAAA;EAGV;EAEA,cAAc,WAAqC;AAClD,UAAM,WAAW,IAAI;MACpB,KAAK;MACL,KAAK;MACL,KAAK;IACN;AACA,aAAS,YAAY;AACrB,WAAO;EACR;AACD;AArBC,cADY,OACcD,OAAsB;AAD1C,IAAM,OAAN;AA2DA,SAAS,eAAe;AAC9B,SAAO;IACN;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EACD;AACD;AAIO,SAAS,sBAAsB;AACrC,SAAO;IACN;IACA;IACA;EACD;AACD;AA8NO,SAAS,8BAGf,QACA,eAC6D;AApa9D,MAAAA;AAqaC,MACC,OAAO,KAAK,MAAM,EAAE,WAAW,KAC5B,aAAa,UACb,CAAC,GAAG,OAAO,SAAS,GAAG,KAAK,GAC9B;AACD,aAAS,OAAO,SAAS;EAC1B;AAGA,QAAM,gBAAwC,CAAC;AAE/C,QAAM,kBAGF,CAAC;AACL,QAAM,eAAuC,CAAC;AAC9C,aAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,MAAM,GAAG;AAClD,QAAI,GAAG,OAAO,KAAK,GAAG;AACrB,YAAM,SAAS,mBAAmB,KAAK;AACvC,YAAM,oBAAoB,gBAAgB,MAAM;AAChD,oBAAc,MAAM,IAAI;AACxB,mBAAa,GAAG,IAAI;QACnB,QAAQ;QACR,QAAQ,MAAM,MAAM,OAAO,IAAI;QAC/B,QAAQ,MAAM,MAAM,OAAO,MAAM;QACjC,SAAS,MAAM,MAAM,OAAO,OAAO;QACnC,YAAW,uDAAmB,cAAa,CAAC;QAC5C,aAAY,uDAAmB,eAAc,CAAC;MAC/C;AAGA,iBACO,UAAU,OAAO;QACrB,MAAgB,MAAM,OAAO,OAAO;MACtC,GACC;AACD,YAAI,OAAO,SAAS;AACnB,uBAAa,GAAG,EAAG,WAAW,KAAK,MAAM;QAC1C;MACD;AAEA,YAAM,eAAcA,QAAA,MAAM,MAAM,OAAO,wBAAnB,gBAAAA,MAAA,YAA0C,MAAgB,MAAM,OAAO,kBAAkB;AAC7G,UAAI,aAAa;AAChB,mBAAW,eAAe,OAAO,OAAO,WAAW,GAAG;AACrD,cAAI,GAAG,aAAa,iBAAiB,GAAG;AACvC,yBAAa,GAAG,EAAG,WAAW,KAAK,GAAG,YAAY,OAAO;UAC1D;QACD;MACD;IACD,WAAW,GAAG,OAAO,SAAS,GAAG;AAChC,YAAM,SAAS,mBAAmB,MAAM,KAAK;AAC7C,YAAM,YAAY,cAAc,MAAM;AACtC,YAAME,aAAsC,MAAM;QACjD,cAAc,MAAM,KAAK;MAC1B;AACA,UAAIC;AAEJ,iBAAW,CAAC,cAAc,QAAQ,KAAK,OAAO,QAAQD,UAAS,GAAG;AACjE,YAAI,WAAW;AACd,gBAAM,cAAc,aAAa,SAAS;AAC1C,sBAAY,UAAU,YAAY,IAAI;AACtC,cAAIC,aAAY;AACf,wBAAY,WAAW,KAAK,GAAGA,WAAU;UAC1C;QACD,OAAO;AACN,cAAI,EAAE,UAAU,kBAAkB;AACjC,4BAAgB,MAAM,IAAI;cACzB,WAAW,CAAC;cACZ,YAAAA;YACD;UACD;AACA,0BAAgB,MAAM,EAAG,UAAU,YAAY,IAAI;QACpD;MACD;IACD;EACD;AAEA,SAAO,EAAE,QAAQ,cAAyB,cAAc;AACzD;AAEO,SAAS,UAIf,OACAD,YACoC;AACpC,SAAO,IAAI;IACV;IACA,CAAC,YACA,OAAO;MACN,OAAO,QAAQA,WAAU,OAAO,CAAC,EAAE,IAAI,CAAC,CAAC,KAAK,KAAK,MAAM;QACxD;QACA,MAAM,cAAc,GAAG;MACxB,CAAC;IACF;EACF;AACD;AAEO,SAAS,UAAqC,aAAoB;AACxE,SAAO,SAAS,IAOf,OACA,QAIC;AACD,WAAO,IAAI;MACV;MACA;MACA;OACC,iCAAQ,OAAO,OAAgB,CAAC,KAAK,MAAM,OAAO,EAAE,SAAS,UAC1D;IACL;EACD;AACD;AAEO,SAAS,WAAW,aAAoB;AAC9C,SAAO,SAAS,KACf,iBACA,QACmC;AACnC,WAAO,IAAI,KAAK,aAAa,iBAAiB,MAAM;EACrD;AACD;AAOO,SAAS,kBACf,QACA,eACA,UACqB;AACrB,MAAI,GAAG,UAAU,GAAG,KAAK,SAAS,QAAQ;AACzC,WAAO;MACN,QAAQ,SAAS,OAAO;MACxB,YAAY,SAAS,OAAO;IAC7B;EACD;AAEA,QAAM,wBAAwB,cAAc,mBAAmB,SAAS,eAAe,CAAC;AACxF,MAAI,CAAC,uBAAuB;AAC3B,UAAM,IAAI;MACT,UAAU,SAAS,gBAAgB,MAAM,OAAO,IAAI,CAAC;IACtD;EACD;AAEA,QAAM,wBAAwB,OAAO,qBAAqB;AAC1D,MAAI,CAAC,uBAAuB;AAC3B,UAAM,IAAI,MAAM,UAAU,qBAAqB,uBAAuB;EACvE;AAEA,QAAM,cAAc,SAAS;AAC7B,QAAM,oBAAoB,cAAc,mBAAmB,WAAW,CAAC;AACvE,MAAI,CAAC,mBAAmB;AACvB,UAAM,IAAI;MACT,UAAU,YAAY,MAAM,OAAO,IAAI,CAAC;IACzC;EACD;AAEA,QAAM,mBAA+B,CAAC;AACtC,aACO,2BAA2B,OAAO;IACvC,sBAAsB;EACvB,GACC;AACD,QACE,SAAS,gBACN,aAAa,2BACb,wBAAwB,iBAAiB,SAAS,gBAClD,CAAC,SAAS,gBACV,wBAAwB,oBAAoB,SAAS,aACxD;AACD,uBAAiB,KAAK,uBAAuB;IAC9C;EACD;AAEA,MAAI,iBAAiB,SAAS,GAAG;AAChC,UAAM,SAAS,eACZ,IAAI;MACL,2CAA2C,SAAS,YAAY,eAAe,qBAAqB;IACrG,IACE,IAAI;MACL,yCAAyC,qBAAqB,UAC7D,SAAS,YAAY,MAAM,OAAO,IAAI,CACvC;IACD;EACF;AAEA,MACC,iBAAiB,CAAC,KACf,GAAG,iBAAiB,CAAC,GAAG,GAAG,KAC3B,iBAAiB,CAAC,EAAE,QACtB;AACD,WAAO;MACN,QAAQ,iBAAiB,CAAC,EAAE,OAAO;MACnC,YAAY,iBAAiB,CAAC,EAAE,OAAO;IACxC;EACD;AAEA,QAAM,IAAI;IACT,sDAAsD,iBAAiB,IAAI,SAAS,SAAS;EAC9F;AACD;AAEO,SAAS,4BACf,aACC;AACD,SAAO;IACN,KAAK,UAAsB,WAAW;IACtC,MAAM,WAAW,WAAW;EAC7B;AACD;AAuBO,SAAS,iBACf,cACA,aACA,KACA,2BACA,iBAA8C,CAAC,UAAU,OAC/B;AAC1B,QAAM,SAAkC,CAAC;AAEzC,aACO;IACL;IACA;EACD,KAAK,0BAA0B,QAAQ,GACtC;AACD,QAAI,cAAc,QAAQ;AACzB,YAAM,WAAW,YAAY,UAAU,cAAc,KAAK;AAC1D,YAAM,aAAa,IAAI,kBAAkB;AAKzC,YAAM,UAAU,OAAO,eAAe,WAClC,KAAK,MAAM,UAAU,IACtB;AACH,aAAO,cAAc,KAAK,IAAI,GAAG,UAAU,GAAG,IAC3C,WACE;QACF;QACA,aAAa,cAAc,kBAAmB;QAC9C;QACA,cAAc;QACd;MACD,IACE,QAAwB;QAAI,CAAC,WAC/B;UACC;UACA,aAAa,cAAc,kBAAmB;UAC9C;UACA,cAAc;UACd;QACD;MACD;IACF,OAAO;AACN,YAAM,QAAQ,eAAe,IAAI,kBAAkB,CAAC;AACpD,YAAM,QAAQ,cAAc;AAC5B,UAAI;AACJ,UAAI,GAAG,OAAO,MAAM,GAAG;AACtB,kBAAU;MACX,WAAW,GAAG,OAAO,GAAG,GAAG;AAC1B,kBAAU,MAAM;MACjB,OAAO;AACN,kBAAU,MAAM,IAAI;MACrB;AACA,aAAO,cAAc,KAAK,IAAI,UAAU,OAAO,OAAO,QAAQ,mBAAmB,KAAK;IACvF;EACD;AAEA,SAAO;AACR;;;AClsBO,SAAS,MAAM,YAAsC;AAC3D,SAAO,YAAY,cAAc,IAAI,IAAI,GAAG,CAAC,IAAI,QAAQ,MAAM;AAChE;AAcO,SAAS,cAAc,YAAqC;AAClE,SAAO,qBAAqB,UAAU,IAAI,QAAQ,MAAM;AACzD;AAcO,SAAS,IAAI,YAA4C;AAC/D,SAAO,UAAU,UAAU,IAAI,QAAQ,MAAM;AAC9C;AAcO,SAAS,YAAY,YAA4C;AACvE,SAAO,mBAAmB,UAAU,IAAI,QAAQ,MAAM;AACvD;AAcO,SAAS,IAAI,YAA4C;AAC/D,SAAO,UAAU,UAAU,IAAI,QAAQ,MAAM;AAC9C;AAcO,SAAS,YAAY,YAA4C;AACvE,SAAO,mBAAmB,UAAU,IAAI,QAAQ,MAAM;AACvD;AAYO,SAAS,IAA0B,YAA4E;AACrH,SAAO,UAAU,UAAU,IAAI,QAAQ,GAAG,YAAY,MAAM,IAAI,aAAa,MAAM;AACpF;AAYO,SAAS,IAA0B,YAA4E;AACrH,SAAO,UAAU,UAAU,IAAI,QAAQ,GAAG,YAAY,MAAM,IAAI,aAAa,MAAM;AACpF;;;AC5HA,SAAS,MAAM,OAAoC;AAClD,SAAO,KAAK,UAAU,KAAK;AAC5B;AAwBO,SAAS,WACf,QACA,OACM;AACN,MAAI,MAAM,QAAQ,KAAK,GAAG;AACzB,WAAO,MAAM,MAAM,QAAQ,MAAM,KAAK,CAAC;EACxC;AACA,SAAO,MAAM,MAAM,QAAQ,KAAK;AACjC;AAsBO,SAAS,WACf,QACA,OACM;AACN,MAAI,MAAM,QAAQ,KAAK,GAAG;AACzB,WAAO,MAAM,MAAM,QAAQ,MAAM,KAAK,CAAC;EACxC;AACA,SAAO,MAAM,MAAM,QAAQ,KAAK;AACjC;AAwBO,SAAS,aACf,QACA,OACM;AACN,MAAI,MAAM,QAAQ,KAAK,GAAG;AACzB,WAAO,MAAM,MAAM,QAAQ,MAAM,KAAK,CAAC;EACxC;AACA,SAAO,MAAM,MAAM,QAAQ,KAAK;AACjC;AAwBO,SAAS,eACf,QACA,OACM;AACN,MAAI,MAAM,QAAQ,KAAK,GAAG;AACzB,WAAO,MAAM,MAAM,QAAQ,MAAM,KAAK,CAAC;EACxC;AACA,SAAO,MAAM,MAAM,QAAQ,KAAK;AACjC;AAiBO,SAAS,gBACf,QACA,OACM;AACN,MAAI,MAAM,QAAQ,KAAK,GAAG;AACzB,WAAO,MAAM,MAAM,QAAQ,MAAM,KAAK,CAAC;EACxC;AACA,SAAO,MAAM,MAAM,QAAQ,KAAK;AACjC;AAYO,SAAS,gBACf,QACA,OACM;AACN,MAAI,MAAM,QAAQ,KAAK,GAAG;AACzB,WAAO,MAAM,MAAM,QAAQ,MAAM,KAAK,CAAC;EACxC;AACA,SAAO,MAAM,MAAM,QAAQ,KAAK;AACjC;", "names": ["_a", "name", "_a", "name", "name", "_a", "char", "value", "startFrom", "_a", "name", "ref", "actions", "_b", "_a", "_b", "name", "_a", "sql", "_b", "_a", "_b", "name", "name", "otel", "rawTracer", "_a", "param", "sql", "placeholder", "name", "SQL", "_b", "_c", "_a", "_a", "_b", "min", "max", "_a", "_b", "result", "name", "_a", "_b", "name", "_a", "_b", "name", "_a", "_b", "name", "_a", "_b", "name", "_a", "_b", "name", "_a", "_b", "name", "_a", "_b", "name", "_a", "_b", "_a", "_b", "name", "_a", "_b", "name", "_a", "_b", "name", "_a", "_b", "name", "_a", "_b", "name", "_a", "_b", "name", "_a", "_b", "name", "_a", "_b", "name", "_a", "_b", "name", "_a", "_b", "name", "_a", "_b", "name", "_a", "_b", "name", "_a", "_b", "name", "_a", "_b", "name", "_a", "_b", "name", "_a", "_b", "name", "_a", "_b", "name", "_a", "_b", "name", "_a", "_b", "name", "_a", "_b", "name", "_a", "_b", "name", "_a", "_b", "name", "_a", "_b", "name", "_a", "_b", "name", "_a", "_b", "name", "_a", "_b", "name", "_a", "_b", "_c", "_d", "_e", "name", "_a", "name", "_a", "_b", "relations", "<PERSON><PERSON><PERSON>"]}