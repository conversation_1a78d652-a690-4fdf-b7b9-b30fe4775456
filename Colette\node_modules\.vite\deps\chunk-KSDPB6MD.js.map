{"version": 3, "sources": ["../../src/logger.ts"], "sourcesContent": ["import { entityKind } from '~/entity.ts';\n\nexport interface Logger {\n\tlogQuery(query: string, params: unknown[]): void;\n}\n\nexport interface LogWriter {\n\twrite(message: string): void;\n}\n\nexport class ConsoleLogWriter implements LogWriter {\n\tstatic readonly [entityKind]: string = 'ConsoleLogWriter';\n\n\twrite(message: string) {\n\t\tconsole.log(message);\n\t}\n}\n\nexport class DefaultLogger implements Logger {\n\tstatic readonly [entityKind]: string = 'DefaultLogger';\n\n\treadonly writer: LogWriter;\n\n\tconstructor(config?: { writer: LogWriter }) {\n\t\tthis.writer = config?.writer ?? new ConsoleLogWriter();\n\t}\n\n\tlogQuery(query: string, params: unknown[]): void {\n\t\tconst stringifiedParams = params.map((p) => {\n\t\t\ttry {\n\t\t\t\treturn JSON.stringify(p);\n\t\t\t} catch {\n\t\t\t\treturn String(p);\n\t\t\t}\n\t\t});\n\t\tconst paramsStr = stringifiedParams.length ? ` -- params: [${stringifiedParams.join(', ')}]` : '';\n\t\tthis.writer.write(`Query: ${query}${paramsStr}`);\n\t}\n}\n\nexport class NoopLogger implements Logger {\n\tstatic readonly [entityKind]: string = 'NoopLogger';\n\n\tlogQuery(): void {\n\t\t// noop\n\t}\n}\n"], "mappings": ";;;;;;;;AAAA;AAWkB;AADX,IAAM,mBAAN,MAA4C;EAGlD,MAAM,SAAiB;AACtB,YAAQ,IAAI,OAAO;EACpB;AACD;AALC,cADY,kBACK,IAAsB;AAXxC,IAAAA;AAmBkBA,MAAA;AADX,IAAM,gBAAN,MAAsC;EAK5C,YAAY,QAAgC;AAFnC;AAGR,SAAK,UAAS,iCAAQ,WAAU,IAAI,iBAAiB;EACtD;EAEA,SAAS,OAAe,QAAyB;AAChD,UAAM,oBAAoB,OAAO,IAAI,CAAC,MAAM;AAC3C,UAAI;AACH,eAAO,KAAK,UAAU,CAAC;MACxB,QAAQ;AACP,eAAO,OAAO,CAAC;MAChB;IACD,CAAC;AACD,UAAM,YAAY,kBAAkB,SAAS,gBAAgB,kBAAkB,KAAK,IAAI,CAAC,MAAM;AAC/F,SAAK,OAAO,MAAM,UAAU,KAAK,GAAG,SAAS,EAAE;EAChD;AACD;AAnBC,cADY,eACKA,KAAsB;AAnBxC,IAAAA;AAyCkBA,MAAA;AADX,IAAM,aAAN,MAAmC;EAGzC,WAAiB;EAEjB;AACD;AALC,cADY,YACKA,KAAsB;", "names": ["_a"]}