"use strict";
exports.roundToNearestMinutesWithOptions = void 0;

var _index = require("../roundToNearestMinutes.cjs");
var _index2 = require("./_lib/convertToFP.cjs"); // This file is generated automatically by `scripts/build/fp.ts`. Please, don't change it.

const roundToNearestMinutesWithOptions =
  (exports.roundToNearestMinutesWithOptions = (0, _index2.convertToFP)(
    _index.roundToNearestMinutes,
    2,
  ));
